-- 有被分配使用權限的人 = 1255
select count(t.id) as 有被分配使用權限的人 from (
	select u.id, count(*) as cnt from t_user u
	inner join t_user_role ur on u.id = ur.user_id
	inner join t_role r on r.id = ur.role_id
	where u.is_enabled = 1 and u.creation_time < '2024-03-01' and ur.creation_time < '2024-03-01'
	group by u.id
) as t

-- 共有多少人使用ESG系統(不計算碳中和) = 623
select count(username) as 共有多少人使用ESG系統 from (
select distinct try_cast(t.user_id as uniqueidentifier) as user_id, tu.username from 
(
select distinct try_cast(last_update_user_id as uniqueidentifier) as user_id from t_ambient_head where year(last_update_time)*100 + month(last_update_time) = 202402
union
select distinct try_cast(last_update_user_id as uniqueidentifier) as user_id from t_ce_basic_info_head where year(last_update_time)*100 + month(last_update_time) = 202402
union
select distinct try_cast(last_update_user_id as uniqueidentifier) as user_id from t_ce_identification_head where year(last_update_time)*100 + month(last_update_time) = 202402
union
select distinct try_cast(last_update_user_id as uniqueidentifier) as user_id from t_emission_reduction_head where year(last_update_time)*100 + month(last_update_time) = 202402
union
select distinct try_cast(last_update_user_id as uniqueidentifier) as user_id from t_ff_cm_fixed_head where year(last_update_time)*100 + month(last_update_time) = 202402
union
select distinct try_cast(last_update_user_id as uniqueidentifier) as user_id from t_ff_cm_mobile_head where year(last_update_time)*100 + month(last_update_time) = 202402
union
select distinct try_cast(last_update_user_id as uniqueidentifier) as user_id from t_social_perf_three_head where year(last_update_time)*100 + month(last_update_time) = 202402
union
select distinct try_cast(last_update_user_id as uniqueidentifier) as user_id from t_social_perf_two_head where year(last_update_time)*100 + month(last_update_time) = 202402
union
select distinct try_cast(last_update_user_id as uniqueidentifier) as user_id from t_social_performance_head where year(last_update_time)*100 + month(last_update_time) = 202402
union
select distinct try_cast(user_id as uniqueidentifier) from t_workflow_node_user where year(last_update_time)*100 + month(last_update_time) = 202402
) t
left join t_user tu on tu.id = try_cast(t.user_id as uniqueidentifier)
) ot


-- 環境積效共多少組織發起流程 = 34
select count(*) as 環境積效共多少組織發起流程 from (
select distinct organization_id  from t_workflow tw 
inner join t_workflow_control twc on tw.id = twc.workflow_id 
where twc.state in (1,2) and tw.form_id = '50856eb8-730b-4349-9534-b1bfe90a7a10'
and year = 2024
) t

-- 環境積效共發起多少流程 = 35
select count(*) as 環境積效共發起多少流程 from t_workflow tw 
inner join t_workflow_control twc on tw.id = twc.workflow_id 
where twc.state in (1,2) and tw.form_id = '50856eb8-730b-4349-9534-b1bfe90a7a10'
and year = 2024

-- 5月份共有多少人登錄 = 127
select count(*) as 共有多少人使用ESG系統 from (
select distinct t.user_id, tu.username as 共有多少人使用系統 from 
(
select distinct last_update_user_id as user_id from t_ambient_head  where year(last_update_time)*100 + month(last_update_time) IN (202405)
union
select distinct last_update_user_id as user_id from t_ce_basic_info_head  where year(last_update_time)*100 + month(last_update_time) IN (202405)
union
select distinct last_update_user_id as user_id from t_ce_identification_head  where year(last_update_time)*100 + month(last_update_time) IN (202405)
union
select distinct last_update_user_id as user_id from t_emission_reduction_head  where year(last_update_time)*100 + month(last_update_time) IN (202405)
union
select distinct last_update_user_id as user_id from t_ff_cm_fixed_head  where year(last_update_time)*100 + month(last_update_time) IN (202405)
union
select distinct last_update_user_id as user_id from t_ff_cm_mobile_head  where year(last_update_time)*100 + month(last_update_time) IN (202405)
union
select distinct last_update_user_id as user_id from t_social_perf_three_head  where year(last_update_time)*100 + month(last_update_time) IN (202405) 
union
select distinct last_update_user_id as user_id from t_social_perf_two_head  where year(last_update_time)*100 + month(last_update_time) IN (202405)
union
select distinct last_update_user_id as user_id from t_social_performance_head  where year(last_update_time)*100 + month(last_update_time) IN (202405)
union
select distinct user_id from t_workflow_node_user where year(last_update_time)*100 + month(last_update_time) IN (202405)
) t
left join t_user tu on tu.id = t.user_id
) ot

-- 5月份共有多少人登錄 = 44
select count(*) as 共有多少人使用ESG系統 from (
 select distinct al.username from t_api_log al where  year(al.start_time)*100 + month(al.start_time) IN (202405)
) t

-- 6月份共有多少人登錄 = 67
select count(*) as 共有多少人使用ESG系統 from (
select distinct t.user_id, tu.username as 共有多少人使用系統 from 
(
select distinct last_update_user_id as user_id from t_ambient_head  where year(last_update_time)*100 + month(last_update_time) IN (202406)
union
select distinct last_update_user_id as user_id from t_ce_basic_info_head  where year(last_update_time)*100 + month(last_update_time) IN (202406)
union
select distinct last_update_user_id as user_id from t_ce_identification_head  where year(last_update_time)*100 + month(last_update_time) IN (202406)
union
select distinct last_update_user_id as user_id from t_emission_reduction_head  where year(last_update_time)*100 + month(last_update_time) IN (202406)
union
select distinct last_update_user_id as user_id from t_ff_cm_fixed_head  where year(last_update_time)*100 + month(last_update_time) IN (202406)
union
select distinct last_update_user_id as user_id from t_ff_cm_mobile_head  where year(last_update_time)*100 + month(last_update_time) IN (202406)
union
select distinct last_update_user_id as user_id from t_social_perf_three_head  where year(last_update_time)*100 + month(last_update_time) IN (202406) 
union
select distinct last_update_user_id as user_id from t_social_perf_two_head  where year(last_update_time)*100 + month(last_update_time) IN (202406)
union
select distinct last_update_user_id as user_id from t_social_performance_head  where year(last_update_time)*100 + month(last_update_time) IN (202406)
union
select distinct user_id from t_workflow_node_user where year(last_update_time)*100 + month(last_update_time) IN (202406)
) t
left join t_user tu on tu.id = t.user_id
) ot

-- 6月份共有多少人登錄 = 77
select count(*) as 共有多少人使用ESG系統 from (
 select distinct al.username from t_api_log al where  year(al.start_time)*100 + month(al.start_time) IN (202406)
) t

-- 環境積效表單共修改了多少次版本 = 573
select cast(sum(last_update_version-1)/2 as int) from t_ambient_head tah where year = 2024 and last_update_version > 1


-- 2024年3月份的填报，中建香港涉及多少个地盘 = 61
SELECT count(organization_id) as 多少个地盘 from (
select distinct organization_id from
(
select distinct organization_id  from t_ambient_head  where year = 2024 and month = 3 and last_update_version > 1
union
select distinct organization_id from t_ce_basic_info_head  where year = 2024 and month = 3 and last_update_version > 1
union
select distinct organization_id from t_ce_identification_head  where year = 2024 and month = 3 and last_update_version > 1
union
select distinct organization_id from t_emission_reduction_head  where year = 2024 and month = 3 and last_update_version > 1
union
select distinct organization_id from t_ff_cm_fixed_head  where year = 2024 and month = 3 and last_update_version > 1
union
select distinct organization_id from t_ff_cm_mobile_head  where year = 2024 and month = 3 and last_update_version > 1
union
select distinct organization_id from t_social_perf_three_head  where year = 2024 and month = 3 and last_update_version > 1
union
select distinct organization_id from t_social_perf_two_head  where year = 2024 and month = 3 and last_update_version > 1
union
select distinct organization_id  from t_social_performance_head where year = 2024 and month = 3 and last_update_version > 1
) t
where t.organization_id IN (select _o.id from t_organization _o where _o.is_deleted = 0 and _o.no LIKE N'001001%')
) ot


-- 环境绩效有多少条数据 = 372485
select count(*) from t_ambient_detail

-- 车辆用油有多少条数据 = 8790
select count(*) from t_ab_vehicle_fuel_usage

-- 商务旅行有多少条数据 = 14477
select count(*) from t_ab_business_trip

-- 總共多少條附件 = 16396
select count(*) from t_attachment
