DROP TABLE Tzh_Project;

DROP TABLE Tzh_Result;

DROP TABLE D_Project;

DROP TABLE D_CDMS_MaterialTransportCarbonFactor;

DROP TABLE D_CDMS_WasterWaterCarbonFactor;

ALTER TABLE D_CDMS_ElectricityCarbonFactor DROP COLUMN SiteId, InputDatetime, SyncDatetime;

ALTER TABLE F_CDMS_GasCarbonFactor DROP COLUMN SiteId, InputDatetime, SyncDatetime;

ALTER TABLE F_CDMS_WasteTransportationCarbonFactor DROP COLUMN SiteId, InputDatetime, SyncDatetime;;

ALTER TABLE F_CDMS_WaterCarbonFactor DROP COLUMN SiteId, InputDatetime, SyncDatetime;

/*
ALTER TABLE D_CDMS_ElectricityCarbonFactor ADD SiteId INT NULL;

ALTER TABLE F_CDMS_GasCarbonFactor ADD SiteId INT NULL;

ALTER TABLE F_CDMS_WasteTransportationCarbonFactor ADD SiteId INT NULL;

ALTER TABLE F_CDMS_WaterCarbonFactor ADD SiteId INT NULL;


ALTER TABLE D_CDMS_ElectricityCarbonFactor ADD InputDatetime datetime NULL;

ALTER TABLE F_CDMS_GasCarbonFactor ADD InputDatetime datetime NULL;

ALTER TABLE F_CDMS_WasteTransportationCarbonFactor ADD InputDatetime datetime NULL;

ALTER TABLE F_CDMS_WaterCarbonFactor ADD InputDatetime datetime NULL;


ALTER TABLE D_CDMS_ElectricityCarbonFactor ADD SyncDatetime datetime NULL;

ALTER TABLE F_CDMS_GasCarbonFactor ADD SyncDatetime datetime NULL;

ALTER TABLE F_CDMS_WasteTransportationCarbonFactor ADD SyncDatetime datetime NULL;

ALTER TABLE F_CDMS_WaterCarbonFactor ADD SyncDatetime datetime NULL;
*/