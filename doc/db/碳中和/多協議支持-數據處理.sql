
UPDATE D_CDMS_ElectricityCarbonFactor SET CarbonEmissionLocationId = (SELECT Id FROM Tzh_CarbonEmissionLocation CEL WHERE CEL.Name = CarbonEmissionLocation)

UPDATE D_CDMS_MaterialCarbonFactor SET CarbonEmissionLocationId = (SELECT Id FROM Tzh_CarbonEmissionLocation CEL WHERE CEL.Name = CarbonEmissionLocation)

UPDATE F_CDMS_GasCarbonFactor SET CarbonEmissionLocationId = (SELECT Id FROM Tzh_CarbonEmissionLocation CEL WHERE CEL.Name = CarbonEmissionLocation)

UPDATE F_CDMS_WasteTransportationCarbonFactor SET CarbonEmissionLocationId = (SELECT Id FROM Tzh_CarbonEmissionLocation CEL WHERE CEL.Name = N'地盤')

UPDATE F_CDMS_WaterCarbonFactor SET CarbonEmissionLocationId = (SELECT Id FROM Tzh_CarbonEmissionLocation CEL WHERE CEL.Name = CarbonEmissionLocation)



UPDATE Tzh_MonthlyMaterialCarbon SET ProtocolId = N'C48D975A-47A8-4265-9F7A-64601E0FF52F' WHERE SiteName = N'有機資源回收中心第二期（CDV）'

UPDATE Tzh_MonthlyMaterialCarbon SET ProtocolId = N'CE02D242-9300-4B6C-A98B-39E46195ACE4' WHERE SiteName = N'東北大馬路長者公寓設計連建造工程'

UPDATE Tzh_MonthlyMaterialCarbon SET ProtocolId = N'C48D975A-47A8-4265-9F7A-64601E0FF52F' WHERE SiteName = N'設計及建造將軍澳中醫院及政府中藥檢測中心'

UPDATE Tzh_MonthlyMaterialCarbon SET ProtocolId = N'C48D975A-47A8-4265-9F7A-64601E0FF52F' WHERE SiteName = N'中海总部基地'



UPDATE F_Result_Latest SET Protocol = N'ISO 14064' WHERE SiteName = N'有機資源回收中心第二期（CDV）'

UPDATE F_Result_Latest SET Protocol = N'HJ XXXX' WHERE SiteName = N'東北大馬路長者公寓設計連建造工程'

UPDATE F_Result_Latest SET Protocol = N'ISO 14064' WHERE SiteName = N'設計及建造將軍澳中醫院及政府中藥檢測中心'

UPDATE F_Result_Latest SET Protocol = N'ISO 14064' WHERE SiteName = N'中海总部基地'



UPDATE F_CombineAll SET Protocol = N'ISO 14064' WHERE SiteName = N'有機資源回收中心第二期（CDV）'

UPDATE F_CombineAll SET Protocol = N'HJ XXXX' WHERE SiteName = N'東北大馬路長者公寓設計連建造工程'

UPDATE F_CombineAll SET Protocol = N'ISO 14064' WHERE SiteName = N'設計及建造將軍澳中醫院及政府中藥檢測中心'

UPDATE F_CombineAll SET Protocol = N'ISO 14064' WHERE SiteName = N'中海总部基地'



UPDATE F_Electricity SET Protocol = N'ISO 14064' WHERE SiteName = N'有機資源回收中心第二期（CDV）'

UPDATE F_Electricity SET Protocol = N'HJ XXXX' WHERE SiteName = N'東北大馬路長者公寓設計連建造工程'

UPDATE F_Electricity SET Protocol = N'ISO 14064' WHERE SiteName = N'設計及建造將軍澳中醫院及政府中藥檢測中心'

UPDATE F_Electricity SET Protocol = N'ISO 14064' WHERE SiteName = N'中海总部基地'




UPDATE F_Gas SET Protocol = N'ISO 14064' WHERE SiteName = N'有機資源回收中心第二期（CDV）'

UPDATE F_Gas SET Protocol = N'HJ XXXX' WHERE SiteName = N'東北大馬路長者公寓設計連建造工程'

UPDATE F_Gas SET Protocol = N'ISO 14064' WHERE SiteName = N'設計及建造將軍澳中醫院及政府中藥檢測中心'

UPDATE F_Gas SET Protocol = N'ISO 14064' WHERE SiteName = N'中海总部基地'




UPDATE F_Material SET Protocol = N'ISO 14064' WHERE SiteName = N'有機資源回收中心第二期（CDV）'

UPDATE F_Material SET Protocol = N'HJ XXXX' WHERE SiteName = N'東北大馬路長者公寓設計連建造工程'

UPDATE F_Material SET Protocol = N'ISO 14064' WHERE SiteName = N'設計及建造將軍澳中醫院及政府中藥檢測中心'

UPDATE F_Material SET Protocol = N'ISO 14064' WHERE SiteName = N'中海总部基地'




UPDATE F_Material_Detail SET Protocol = N'ISO 14064' WHERE SiteName = N'有機資源回收中心第二期（CDV）'

UPDATE F_Material_Detail SET Protocol = N'HJ XXXX' WHERE SiteName = N'東北大馬路長者公寓設計連建造工程'

UPDATE F_Material_Detail SET Protocol = N'ISO 14064' WHERE SiteName = N'設計及建造將軍澳中醫院及政府中藥檢測中心'

UPDATE F_Material_Detail SET Protocol = N'ISO 14064' WHERE SiteName = N'中海总部基地'




UPDATE F_Material_Invoice SET Protocol = N'ISO 14064' WHERE SiteName = N'有機資源回收中心第二期（CDV）'

UPDATE F_Material_Invoice SET Protocol = N'HJ XXXX' WHERE SiteName = N'東北大馬路長者公寓設計連建造工程'

UPDATE F_Material_Invoice SET Protocol = N'ISO 14064' WHERE SiteName = N'設計及建造將軍澳中醫院及政府中藥檢測中心'

UPDATE F_Material_Invoice SET Protocol = N'ISO 14064' WHERE SiteName = N'中海总部基地'




UPDATE F_WasterWater SET Protocol = N'ISO 14064' WHERE SiteName = N'有機資源回收中心第二期（CDV）'

UPDATE F_WasterWater SET Protocol = N'HJ XXXX' WHERE SiteName = N'東北大馬路長者公寓設計連建造工程'

UPDATE F_WasterWater SET Protocol = N'ISO 14064' WHERE SiteName = N'設計及建造將軍澳中醫院及政府中藥檢測中心'

UPDATE F_WasterWater SET Protocol = N'ISO 14064' WHERE SiteName = N'中海总部基地'




UPDATE F_WasterWater_Detail SET Protocol = N'ISO 14064' WHERE SiteName = N'有機資源回收中心第二期（CDV）'

UPDATE F_WasterWater_Detail SET Protocol = N'HJ XXXX' WHERE SiteName = N'東北大馬路長者公寓設計連建造工程'

UPDATE F_WasterWater_Detail SET Protocol = N'ISO 14064' WHERE SiteName = N'設計及建造將軍澳中醫院及政府中藥檢測中心'

UPDATE F_WasterWater_Detail SET Protocol = N'ISO 14064' WHERE SiteName = N'中海总部基地'




UPDATE F_WasteTransportation SET Protocol = N'ISO 14064' WHERE SiteName = N'有機資源回收中心第二期（CDV）'

UPDATE F_WasteTransportation SET Protocol = N'HJ XXXX' WHERE SiteName = N'東北大馬路長者公寓設計連建造工程'

UPDATE F_WasteTransportation SET Protocol = N'ISO 14064' WHERE SiteName = N'設計及建造將軍澳中醫院及政府中藥檢測中心'

UPDATE F_WasteTransportation SET Protocol = N'ISO 14064' WHERE SiteName = N'中海总部基地'




UPDATE F_Water SET Protocol = N'ISO 14064' WHERE SiteName = N'有機資源回收中心第二期（CDV）'

UPDATE F_Water SET Protocol = N'HJ XXXX' WHERE SiteName = N'東北大馬路長者公寓設計連建造工程'

UPDATE F_Water SET Protocol = N'ISO 14064' WHERE SiteName = N'設計及建造將軍澳中醫院及政府中藥檢測中心'

UPDATE F_Water SET Protocol = N'ISO 14064' WHERE SiteName = N'中海总部基地'


UPDATE Tzh_Planning SET ProtocolId = N'C48D975A-47A8-4265-9F7A-64601E0FF52F' WHERE SiteName = N'有機資源回收中心第二期（CDV）'

UPDATE Tzh_Planning SET ProtocolId = N'CE02D242-9300-4B6C-A98B-39E46195ACE4' WHERE SiteName = N'東北大馬路長者公寓設計連建造工程'

UPDATE Tzh_Planning SET ProtocolId = N'C48D975A-47A8-4265-9F7A-64601E0FF52F' WHERE SiteName = N'設計及建造將軍澳中醫院及政府中藥檢測中心'

UPDATE Tzh_Planning SET ProtocolId = N'C48D975A-47A8-4265-9F7A-64601E0FF52F' WHERE SiteName = N'中海总部基地'



UPDATE Tzh_EmissionReductionDescription SET ProtocolId = N'C48D975A-47A8-4265-9F7A-64601E0FF52F' WHERE SiteName = N'有機資源回收中心第二期（CDV）'

UPDATE Tzh_EmissionReductionDescription SET ProtocolId = N'CE02D242-9300-4B6C-A98B-39E46195ACE4' WHERE SiteName = N'東北大馬路長者公寓設計連建造工程'

UPDATE Tzh_EmissionReductionDescription SET ProtocolId = N'C48D975A-47A8-4265-9F7A-64601E0FF52F' WHERE SiteName = N'設計及建造將軍澳中醫院及政府中藥檢測中心'

UPDATE Tzh_EmissionReductionDescription SET ProtocolId = N'C48D975A-47A8-4265-9F7A-64601E0FF52F' WHERE SiteName = N'中海总部基地'



UPDATE D_CDMS_ElectricityCarbonFactor SET ProtocolId = N'C48D975A-47A8-4265-9F7A-64601E0FF52F' WHERE SiteName = N'有機資源回收中心第二期（CDV）'

UPDATE D_CDMS_ElectricityCarbonFactor SET ProtocolId = N'CE02D242-9300-4B6C-A98B-39E46195ACE4' WHERE SiteName = N'東北大馬路長者公寓設計連建造工程'

UPDATE D_CDMS_ElectricityCarbonFactor SET ProtocolId = N'C48D975A-47A8-4265-9F7A-64601E0FF52F' WHERE SiteName = N'設計及建造將軍澳中醫院及政府中藥檢測中心'

UPDATE D_CDMS_ElectricityCarbonFactor SET ProtocolId = N'C48D975A-47A8-4265-9F7A-64601E0FF52F' WHERE SiteName = N'中海总部基地'



UPDATE F_CDMS_GasCarbonFactor SET ProtocolId = N'C48D975A-47A8-4265-9F7A-64601E0FF52F' WHERE SiteName = N'有機資源回收中心第二期（CDV）'

UPDATE F_CDMS_GasCarbonFactor SET ProtocolId = N'CE02D242-9300-4B6C-A98B-39E46195ACE4' WHERE SiteName = N'東北大馬路長者公寓設計連建造工程'

UPDATE F_CDMS_GasCarbonFactor SET ProtocolId = N'C48D975A-47A8-4265-9F7A-64601E0FF52F' WHERE SiteName = N'設計及建造將軍澳中醫院及政府中藥檢測中心'

UPDATE F_CDMS_GasCarbonFactor SET ProtocolId = N'C48D975A-47A8-4265-9F7A-64601E0FF52F' WHERE SiteName = N'中海总部基地'



UPDATE F_CDMS_WasteTransportationCarbonFactor SET ProtocolId = N'C48D975A-47A8-4265-9F7A-64601E0FF52F' WHERE SiteName = N'有機資源回收中心第二期（CDV）'

UPDATE F_CDMS_WasteTransportationCarbonFactor SET ProtocolId = N'CE02D242-9300-4B6C-A98B-39E46195ACE4' WHERE SiteName = N'東北大馬路長者公寓設計連建造工程'

UPDATE F_CDMS_WasteTransportationCarbonFactor SET ProtocolId = N'C48D975A-47A8-4265-9F7A-64601E0FF52F' WHERE SiteName = N'設計及建造將軍澳中醫院及政府中藥檢測中心'

UPDATE F_CDMS_WasteTransportationCarbonFactor SET ProtocolId = N'C48D975A-47A8-4265-9F7A-64601E0FF52F' WHERE SiteName = N'中海总部基地'



UPDATE F_CDMS_WaterCarbonFactor SET ProtocolId = N'C48D975A-47A8-4265-9F7A-64601E0FF52F' WHERE SiteName = N'有機資源回收中心第二期（CDV）'

UPDATE F_CDMS_WaterCarbonFactor SET ProtocolId = N'CE02D242-9300-4B6C-A98B-39E46195ACE4' WHERE SiteName = N'東北大馬路長者公寓設計連建造工程'

UPDATE F_CDMS_WaterCarbonFactor SET ProtocolId = N'C48D975A-47A8-4265-9F7A-64601E0FF52F' WHERE SiteName = N'設計及建造將軍澳中醫院及政府中藥檢測中心'

UPDATE F_CDMS_WaterCarbonFactor SET ProtocolId = N'C48D975A-47A8-4265-9F7A-64601E0FF52F' WHERE SiteName = N'中海总部基地'



UPDATE Tzh_MaterialInvoiceTransport SET ProtocolId = N'C48D975A-47A8-4265-9F7A-64601E0FF52F' WHERE SiteName = N'有機資源回收中心第二期（CDV）'

UPDATE Tzh_MaterialInvoiceTransport SET ProtocolId = N'CE02D242-9300-4B6C-A98B-39E46195ACE4' WHERE SiteName = N'東北大馬路長者公寓設計連建造工程'

UPDATE Tzh_MaterialInvoiceTransport SET ProtocolId = N'C48D975A-47A8-4265-9F7A-64601E0FF52F' WHERE SiteName = N'設計及建造將軍澳中醫院及政府中藥檢測中心'

UPDATE Tzh_MaterialInvoiceTransport SET ProtocolId = N'C48D975A-47A8-4265-9F7A-64601E0FF52F' WHERE SiteName = N'中海总部基地'


UPDATE D_CDMS_MaterialCarbonFactor SET ProtocolSubCategoryId = (SELECT TOP 1 Id FROM Tzh_Protocol_SubCategory SC WHERE SC.SubCategoryName = Scope) 


UPDATE Tzh_ExpectedEmissionReduction SET CarbonEmissionLocationId = (SELECT TOP 1 Id FROM Tzh_CarbonEmissionLocation L WHERE L.Name = CarbonEmissionLocation)


UPDATE Tzh_OtherSite SET Protocol = N'ISO 14064'
  WHERE SiteName <> N'東北大馬路長者公寓設計連建造工程'

UPDATE Tzh_OtherSite SET Protocol = N'HJ XXXX'
  WHERE SiteName = N'東北大馬路長者公寓設計連建造工程'

UPDATE Tzh_OtherSite_Detail SET Protocol = N'ISO 14064'
  WHERE SiteName <> N'東北大馬路長者公寓設計連建造工程'

UPDATE Tzh_OtherSite_Detail SET Protocol = N'HJ XXXX'
  WHERE SiteName = N'東北大馬路長者公寓設計連建造工程'

UPDATE Tzh_EmissionReductionHead SET ProtocolCategoryId = N'A8E67A4E-6FF5-4A14-909D-C56909A059B5' WHERE ScopeMain = N'???'

UPDATE Tzh_EmissionReductionHead SET ProtocolCategoryId = N'4580B12F-6801-4DAC-98DC-D28AFB3883B6' WHERE ScopeMain = N'???'

UPDATE Tzh_EmissionReductionHead SET ProtocolCategoryId = N'F6391A23-D24B-42C0-86E9-D5FBC46349FD' WHERE ScopeMain = N'???'

UPDATE Tzh_EmissionReductionHead SET ProtocolCategoryId = N'F6391A23-D24B-42C0-86E9-D5FBC46349FD' WHERE ScopeMain = N'???(A)'

UPDATE Tzh_EmissionReductionHead SET ProtocolCategoryId = N'3214BA62-5DEB-4A1D-96A8-AD5FFA1AC911' WHERE ScopeMain = N'???(B)'

UPDATE Tzh_EmissionReductionHead SET ProtocolCategoryId = N'E917DC7A-F4AC-47B4-8DE2-103DB7807F7C' WHERE ScopeMain = N'????'

UPDATE Tzh_EmissionReductionHead SET ProtocolCategoryId = N'81575A8D-44FE-44DA-B2CD-AEBAFC2C8BC0' WHERE ScopeMain = N'????'



UPDATE Tzh_ExpectedEmissionReduction SET ProtocolCategoryId = N'A8E67A4E-6FF5-4A14-909D-C56909A059B5' WHERE ScopeMain = N'???'

UPDATE Tzh_ExpectedEmissionReduction SET ProtocolCategoryId = N'4580B12F-6801-4DAC-98DC-D28AFB3883B6' WHERE ScopeMain = N'???'

UPDATE Tzh_ExpectedEmissionReduction SET ProtocolCategoryId = N'F6391A23-D24B-42C0-86E9-D5FBC46349FD' WHERE ScopeMain = N'???'

UPDATE Tzh_ExpectedEmissionReduction SET ProtocolCategoryId = N'F6391A23-D24B-42C0-86E9-D5FBC46349FD' WHERE ScopeMain = N'???(A)'

UPDATE Tzh_ExpectedEmissionReduction SET ProtocolCategoryId = N'3214BA62-5DEB-4A1D-96A8-AD5FFA1AC911' WHERE ScopeMain = N'???(B)'

UPDATE Tzh_ExpectedEmissionReduction SET ProtocolCategoryId = N'E917DC7A-F4AC-47B4-8DE2-103DB7807F7C' WHERE ScopeMain = N'????'

UPDATE Tzh_ExpectedEmissionReduction SET ProtocolCategoryId = N'81575A8D-44FE-44DA-B2CD-AEBAFC2C8BC0' WHERE ScopeMain = N'????'





SELECT ERH.Id, ERH.SiteName, C.CategoryName, C.CategoryNameSC, C.CategoryNameEN, ERH.Title, ERH.TitleSC,
ERH.TitleEN, L.Name AS CarbonEmissionLocation, L.NameSC AS CarbonEmissionLocationSC, L.NameEN AS CarbonEmissionLocationEN,
ERH.MethodDescription, ERH.MethodDescriptionSC, ERH.MethodDescriptionEN, ERH.CalculationDescription
FROM Tzh_EmissionReductionHead ERH
LEFT JOIN Tzh_Protocol_Category C ON C.Id = ERH.ProtocolCategoryId
LEFT JOIN Tzh_Protocol PT ON PT.Id = C.ProtocolId
LEFT JOIN Tzh_CarbonEmissionLocation L ON L.Id = ERH.CarbonEmissionLocationId
WHERE ERH.IsDeleted = 0
AND ERH.SiteName = #{siteName} 
AND PT.NameEN = #{protocol}


SELECT ScopeDetail, SUM(ISNULL(CarbonReductionAmount, 0))/1000 AS  CarbonReductionAmount 
FROM [Tzh_EmissionReductionHead] h 
LEFT JOIN Tzh_Protocol_Category C ON C.Id = h.ProtocolCategoryId
LEFT JOIN Tzh_Protocol PT ON PT.Id = C.ProtocolId
LEFT JOIN Tzh_CarbonEmissionLocation L ON L.Id = h.CarbonEmissionLocationId
LEFT JOIN  [Tzh_EmissionReduction] t ON t.headId = h.id AND t.isDeleted <> 1 AND h.isDeleted <> 1  
WHERE h.SiteName=#{siteName} 
AND PT.NameEN = #{protocol}
AND L.Name = #{carbonEmissionLocation}
AND RecordYearMonth >= #{startMonth} AND RecordYearMonth <= #{endMonth} 
AND (ScopeDetail = #{scopeDetail} OR N'' = #{scopeDetail} OR #{scopeDetail} IS NULL) 
GROUP BY ScopeDetail 
ORDER BY SUM(ISNULL(CarbonReductionAmount, 0))/1000 DESC 



SELECT t1.RecordYearMonth, t2.ExpectedCarbonReductionAmount, 
SUM(t1.CarbonReductionAmount) OVER (ORDER BY t1.RecordYearMonth) AS TotalCarbonReductionAmount 
FROM ( 
SELECT _t.RecordYearMonth, SUM(ISNULL(_t.CarbonReductionAmount, 0))/1000 AS CarbonReductionAmount 
FROM [Tzh_EmissionReductionHead] _h 
LEFT JOIN  [Tzh_EmissionReduction] _t ON _t.headId = _h.id AND _t.isDeleted <> 1 AND _h.isDeleted <> 1 
WHERE SiteName=#{siteName} 
AND _h.CarbonEmissionLocation LIKE CONCAT('%', #{carbonEmissionLocation},'%') 
AND _t.RecordYearMonth >= #{startMonth} AND _t.RecordYearMonth <= #{endMonth} 
GROUP BY RecordYearMonth 
) t1 
LEFT JOIN (
SELECT SUM(_t.CarbonReductionAmount) AS ExpectedCarbonReductionAmount 
FROM Tzh_ExpectedEmissionReduction _t 
WHERE _t.SiteName=#{siteName} 
AND _t.CarbonEmissionLocation LIKE CONCAT('%', #{carbonEmissionLocation},'%') 
) t2 ON 1 = 1 
ORDER BY t1.RecordYearMonth 


SELECT t1.RecordYearMonth, t2.ExpectedCarbonReductionAmount, 
SUM(t1.CarbonReductionAmount) OVER (ORDER BY t1.RecordYearMonth) AS TotalCarbonReductionAmount 
FROM ( 
SELECT _t.RecordYearMonth, SUM(ISNULL(_t.CarbonReductionAmount, 0))/1000 AS CarbonReductionAmount 
FROM [Tzh_EmissionReductionHead] _h 
LEFT JOIN Tzh_Protocol_Category _C ON _C.Id = _h.ProtocolCategoryId
LEFT JOIN Tzh_Protocol _PT ON _PT.Id = _C.ProtocolId
LEFT JOIN Tzh_CarbonEmissionLocation _L ON _L.Id = _h.CarbonEmissionLocationId
LEFT JOIN  [Tzh_EmissionReduction] _t ON _t.headId = _h.id AND _t.isDeleted <> 1 AND _h.isDeleted <> 1 
WHERE _h.SiteName = #{siteName} 
AND _PT.NameEN = #{protocol} 
AND _L.Name = #{carbonEmissionLocation} 
AND _t.RecordYearMonth >= #{startMonth} AND _t.RecordYearMonth <= #{endMonth} 
GROUP BY RecordYearMonth 
) t1 
LEFT JOIN (
SELECT SUM(_t.CarbonReductionAmount) AS ExpectedCarbonReductionAmount 
FROM Tzh_ExpectedEmissionReduction _t 
LEFT JOIN Tzh_Protocol_Category _C ON _C.Id = _t.ProtocolCategoryId
LEFT JOIN Tzh_Protocol _PT ON _PT.Id = _C.ProtocolId
LEFT JOIN Tzh_CarbonEmissionLocation _L ON _L.Id = _t.CarbonEmissionLocationId
WHERE _t.SiteName=#{siteName} 
AND _PT.NameEN = #{protocol} 
AND _L.Name = #{carbonEmissionLocation} 
) t2 ON 1 = 1 
ORDER BY t1.RecordYearMonth 


SELECT t1.ScopeMain AS ScopeMain, IIF(t1.RecordCount > 0, t2.CarbonAmountTotal/t1.RecordCount, 0) AS CarbonAmount FROM 
( 
SELECT _t1.ScopeMain, COUNT(DISTINCT _t1.RecordYearMonth) AS RecordCount 
FROM dbo.F_Result_Latest _t1
WHERE _t1.CalculateDate = (SELECT MAX(CalculateDate) FROM dbo.F_Result_Latest)
AND _t1.SiteName = #{siteName} 
AND _t1.Protocol = #{protocol}
AND _t1.CarbonEmissionLocation = #{carbonEmissionLocationSimp}
GROUP BY _t1.ScopeMain 
) t1 
LEFT JOIN ( 
SELECT _t2.ScopeMain, ISNULL(SUM(ISNULL(_t2.CarbonAmount, 0)),1)/1000 AS CarbonAmountTotal 
FROM dbo.F_Result_Latest _t2
WHERE _t2.CalculateDate = (SELECT MAX(CalculateDate) FROM dbo.F_Result_Latest)
AND _t2.SiteName = #{siteName} 
AND _t2.Protocol = #{protocol}
AND _t2.CarbonEmissionLocation = #{carbonEmissionLocation} 
GROUP BY _t2.ScopeMain 
) t2 ON t1.ScopeMain = t2.ScopeMain 
ORDER BY t1.ScopeMain 



SELECT t1.ScopeDetail AS ScopeMain 
,SUM(ISNULL(t1.CarbonAmount, 0)) AS CarbonAmount 
FROM Tzh_ExpectedEmissionIso t1 
LEFT JOIN Tzh_Protocol_SubCategory SC ON SC.Id = t1.ProtocolSubCategoryId
LEFT JOIN Tzh_Protocol_Category C ON C.Id = SC.CategoryId
LEFT JOIN Tzh_Protocol PT ON PT.Id = C.ProtocolId
LEFT JOIN Tzh_CarbonEmissionLocation L ON L.Id = t1.CarbonEmissionLocationId
WHERE t1.SiteName = #{siteName} 
AND PT.Protocol = #{protocol}
AND L.Name = #{carbonEmissionLocation} 
AND t1.ScopeDetail IN (N'??', N'???') 
AND t1.IsDeleted = 0 
GROUP BY t1.ScopeDetail 
ORDER BY t1.ScopeDetail 



SELECT C.CategoryName, C.CategoryNameSC, C.CategoryNameEN, SUM(ISNULL(EEI.CarbonAmount,0)) AS CarbonAmount 
FROM Tzh_ExpectedEmissionIso EEI
LEFT JOIN Tzh_Protocol_SubCategory SC ON EEI.ProtocolSubCategoryId = SC.Id  
LEFT JOIN Tzh_Protocol_Category C ON SC.CategoryId = C.Id 
LEFT JOIN Tzh_Protocol PT ON PT.Id = C.ProtocolId
LEFT JOIN Tzh_CarbonEmissionLocation L ON L.Id = EEI.CarbonEmissionLocationId 
WHERE EEI.IsDeleted = 0
AND EEI.SiteName = #{siteName}
AND PT.Protocol = #{protocol}
AND L.Name = #{carbonEmissionLocation} 
GROUP BY C.CategoryName, C.CategoryNameSC, C.CategoryNameEN
ORDER BY C.CategoryName 


SELECT t1.ScopeMain, t1.ScopeDetail, C.CategoryName, C.CategoryNameSC, C.CategoryNameEN, SC.SubCategoryName, SC.SubCategoryNameSC, SC.SubCategoryNameEN, t1.CarbonEmissionLocation
,t1.CarbonAmount, t1.CarbonAmountUnderMeasure, t1.CarbonUnit
,t2.CarbonAmountTotal, IIF(t2.CarbonAmountTotal > 0, t1.CarbonAmount/t2.CarbonAmountTotal*100, 0) AS CarbonAmountProportion
,t3.CarbonAmountUnderMeasureTotal, IIF(t3.CarbonAmountUnderMeasureTotal > 0, t1.CarbonAmountUnderMeasure/t3.CarbonAmountUnderMeasureTotal*100, 0) AS CarbonAmountUnderMeasureProportion
FROM Tzh_ExpectedEmissionIso t1
LEFT JOIN
(
SELECT ISNULL(SUM(ISNULL(_t2.CarbonAmount, 0)), 1) AS CarbonAmountTotal
FROM dbo.Tzh_ExpectedEmissionIso _t2
LEFT JOIN Tzh_Protocol_SubCategory SC ON _t2.ProtocolSubCategoryId = SC.Id  
LEFT JOIN Tzh_Protocol_Category C ON SC.CategoryId = C.Id 
LEFT JOIN Tzh_Protocol PT ON PT.Id = C.ProtocolId
LEFT JOIN Tzh_CarbonEmissionLocation L ON L.Id = _t2.CarbonEmissionLocationId 
WHERE _t2.SiteName = #{siteName}
AND PT.Protocol = #{protocol}
AND L.Name = #{carbonEmissionLocation} 
AND _t2.IsDeleted = 0
) t2 ON 1 = 1
LEFT JOIN
(
SELECT ISNULL(SUM(ISNULL(_t3.CarbonAmountUnderMeasure, 0)), 1) AS CarbonAmountUnderMeasureTotal
FROM dbo.Tzh_ExpectedEmissionIso _t3
LEFT JOIN Tzh_Protocol_SubCategory SC ON _t3.ProtocolSubCategoryId = SC.Id  
LEFT JOIN Tzh_Protocol_Category C ON SC.CategoryId = C.Id 
LEFT JOIN Tzh_Protocol PT ON PT.Id = C.ProtocolId
LEFT JOIN Tzh_CarbonEmissionLocation L ON L.Id = _t3.CarbonEmissionLocationId 
WHERE _t3.SiteName = #{siteName}
AND PT.Protocol = #{protocol}
AND L.Name = #{carbonEmissionLocation} 
AND _t3.IsDeleted = 0
) t3 ON 1 = 1
INNER JOIN Tzh_ProjectInfo PI ON PI.Name = t1.SiteName AND PI.IsDeleted = 0
INNER JOIN Tzh_Protocol P ON P.Id = PI.ProtocolId
INNER JOIN Tzh_Protocol_Category C ON C.ProtocolId = P.Id
INNER JOIN Tzh_Protocol_SubCategory SC ON SC.CategoryId = C.Id AND SC.SubCategoryName = LTRIM(RTRIM(t1.ScopeDetail))
WHERE t1.SiteName = #{siteName}
AND t1.CarbonEmissionLocation LIKE CONCAT('%', #{carbonEmissionLocation},'%')
AND t1.IsDeleted = 0
ORDER BY t1.CarbonEmissionLocation, t1.ScopeMain, t1.CarbonAmount DESC