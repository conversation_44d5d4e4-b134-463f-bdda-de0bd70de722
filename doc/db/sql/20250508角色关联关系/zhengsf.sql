CREATE TABLE [dbo].[t_role_organization] (
                                             [id] nvarchar(50) COLLATE Chinese_Hong_Kong_Stroke_90_CI_AS  NOT NULL,
                                             [role_id] nvarchar(50) COLLATE Chinese_Hong_Kong_Stroke_90_CI_AS  NULL,
                                             [organization_id] nvarchar(50) COLLATE Chinese_Hong_Kong_Stroke_90_CI_AS  NULL,
                                             [creation_time] datetime2(7) DEFAULT getdate() NULL,
                                             [create_username] nvarchar(50) COLLATE Chinese_Hong_Kong_Stroke_90_CI_AS  NULL,
                                             [create_user_id] nvarchar(50) COLLATE Chinese_Hong_Kong_Stroke_90_CI_AS  NULL,
                                             [last_update_time] datetime2(7) DEFAULT getdate() NULL,
                                             [last_update_username] nvarchar(50) COLLATE Chinese_Hong_Kong_Stroke_90_CI_AS  NULL,
                                             [last_update_user_id] nvarchar(50) COLLATE Chinese_Hong_Kong_Stroke_90_CI_AS  NULL,
                                             [is_deleted] bit DEFAULT 0  NULL,
                                             CONSTRAINT [PK_t_role_organization] PRIMARY KEY CLUSTERED ([id])
                                                 WITH (PAD_INDEX = OFF, FILLFACTOR = 80, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON)
                                                 ON [PRIMARY]
)
    ON [PRIMARY]
GO

ALTER TABLE [dbo].[t_role_organization] SET (LOCK_ESCALATION = TABLE)
GO

CREATE NONCLUSTERED INDEX [missing_index_795]
    ON [dbo].[t_role_organization] (
                                    [role_id] ASC
        )
    WITH (
        FILLFACTOR = 80
        )
GO

CREATE NONCLUSTERED INDEX [missing_index_799]
    ON [dbo].[t_role_organization] (
                                    [role_id] ASC,
                                    [organization_id] ASC
        )
    WITH (
        FILLFACTOR = 80
        )
GO

CREATE NONCLUSTERED INDEX [missing_index_800]
    ON [dbo].[t_role_organization] (
                                    [organization_id] ASC
        )
    WITH (
        FILLFACTOR = 80
        )
GO

EXEC sp_addextendedproperty
     'MS_Description', N'角色组织架构关联表',
     'SCHEMA', N'dbo',
     'TABLE', N't_role_organization'


-- 同步角色对应用户下对应组织权限
INSERT INTO [t_role_organization] ([id], [role_id], [organization_id])
select
    NEWID(),
    t.role_id,
    t.organization_id
from (
         select
             DISTINCT
             a.id as role_id,
             d.organization_id
         from  t_role a
                   inner join t_user_role b
                              on b.role_id = a.id
                                  and b.is_deleted = 0
                   inner join t_user c
                              on c.id = b.user_id
                   inner join t_user_organization d
                              on d.is_deleted = 0
                                  and d.user_id = c.id
                   inner join t_organization e
                              on e.is_deleted = 0
                                  and e.id = d.organization_id
     )t