# GET /bi/auth/login 接口技術文檔

## 1. 接口概述

### 1.1 接口描述
OAuth 登錄接口，用於處理第三方認證登錄並重定向到目標頁面。該接口通過驗證外部 OAuth token，完成用戶身份認證，並生成本地 token 後重定向到指定的業務頁面。

### 1.2 接口基本信息
- **接口路徑**: `/bi/auth/login`
- **請求方法**: `GET`
- **接口分類**: 鑒權中心
- **是否需要認證**: 否（OAuth token 驗證）
- **返回類型**: 重定向（302）

## 2. 方法調用鏈介紹

### 2.1 主要調用流程
```
BiAuthController.oAuthLogin()
    ↓
AuthService.oAuthValidateToken() - 驗證外部 OAuth token
    ↓
AuthService.oAuthLogin() - 執行本地登錄邏輯
    ↓
HttpServletResponse.sendRedirect() - 重定向到目標頁面
```

### 2.2 實現概述
該接口主要負責 OAuth 第三方登錄的處理，包括：
1. 接收並解析請求參數
2. 驗證外部 OAuth token 的有效性
3. 提取用戶名並執行本地登錄邏輯
4. 生成本地 token 並構建重定向 URL
5. 執行頁面重定向

## 3. 請求參數

### 3.1 Query Parameters

| 參數名 | 類型 | 必填 | 默認值 | 描述 | 示例 |
|--------|------|------|--------|------|------|
| token | String | 是 | - | OAuth 認證 token | `eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...` |
| sitename | String | 否 | - | 站點名稱 | `project_site_01` |
| siteid | String | 否 | - | 站點 ID | `12345` |
| isheader | String | 否 | `"0"` | 是否顯示頭部 | `"1"` 或 `"0"` |
| protocol | String | 否 | `"GBT 51366"` | 協議標準 | `"GBT 51366"` |
| language | String | 否 | `""` | 語言設置 | `"zh-CN"` |
| theme | String | 否 | `"blue"` | 主題樣式 | `"blue"`, `"dark"` |

### 3.2 請求示例

```http
GET /bi/auth/login?token=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...&sitename=project_site_01&siteid=12345&theme=blue&isheader=1&protocol=GBT%2051366&language=zh-CN HTTP/1.1
Host: your-domain.com
```

## 4. 響應說明

### 4.1 成功響應
**HTTP 狀態碼**: `302 Found`

**響應頭**:
```http
Location: {baseUrl}?theme={theme}&token={localToken}&sitename={sitename}&siteid={siteid}&user={username}&isheader={isheader}&protocol={protocol}&language={language}
```

### 4.2 重定向 URL 參數說明

| 參數名 | 類型 | 描述 | 示例 |
|--------|------|------|------|
| theme | String | 主題樣式 | `blue` |
| token | String | 本地生成的認證 token | `a1b2c3d4-e5f6-7890-abcd-ef1234567890` |
| sitename | String | 站點名稱 | `project_site_01` |
| siteid | String | 站點 ID | `12345` |
| user | String | 用戶名 | `john.doe` |
| isheader | String | 是否顯示頭部 | `1` |
| protocol | String | 協議標準 | `GBT%2051366` |
| language | String | 語言設置 | `zh-CN` |

### 4.3 失敗響應
當 OAuth token 驗證失敗時：

**HTTP 狀態碼**: `302 Found`

**響應頭**:
```http
Location: {baseUrl}
```

## 5. 業務邏輯說明

### 5.1 Token 驗證流程
1. 接收外部 OAuth token
2. 調用 `authService.oAuthValidateToken(token)` 驗證 token 有效性
3. 從 TokenVO 中提取用戶信息

### 5.2 用戶名處理
```java
String[] arrUsername = tokenVO.getUsername().split("\\\\");
String username = arrUsername[arrUsername.length-1];
```
- 支持域用戶名格式（如：`DOMAIN\\username`）
- 提取最後一部分作為實際用戶名

### 5.3 本地登錄處理
1. 調用 `authService.oAuthLogin(username, sitename, siteid)` 執行本地登錄
2. 驗證用戶對指定站點的權限
3. 生成本地 token 並創建用戶會話

### 5.4 站點 ID 處理
```java
if (StringUtil.isEmptyNotTrim(siteid)) {
    siteid = authService.getBySiteName(sitename);
}
```
- 如果未提供 siteid，則根據 sitename 查詢對應的 siteid

## 6. 錯誤處理

### 6.1 常見錯誤情況

| 錯誤類型 | 描述 | 處理方式 |
|----------|------|----------|
| Token 無效 | OAuth token 驗證失敗 | 重定向到 baseUrl（無參數） |
| 用戶無權限 | 用戶對指定站點無訪問權限 | 拋出異常："沒有該地盤權限" |
| 參數缺失 | 必要參數 token 缺失 | 可能導致 NullPointerException |

### 6.2 異常處理機制
- 由全局異常處理器 `BiGlobalExceptionHandler` 統一處理
- 異常會被轉換為標準的 `ResultBody` 格式返回

## 7. 安全考慮

### 7.1 認證安全
- 依賴外部 OAuth 服務進行身份驗證
- 本地生成新的 token，避免直接使用外部 token
- 實施用戶權限驗證，確保用戶只能訪問授權的站點

### 7.2 參數安全
- 所有重定向參數都經過 URL 編碼處理
- 防止 URL 注入攻擊

## 8. 使用示例

### 8.1 完整調用示例

```bash
curl -X GET "https://your-domain.com/bi/auth/login?token=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...&sitename=project_site_01&theme=blue&isheader=1" \
  -H "Accept: text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8" \
  -L
```

### 8.2 JavaScript 調用示例

```javascript
// 構建登錄 URL
const loginUrl = new URL('/bi/auth/login', 'https://your-domain.com');
loginUrl.searchParams.set('token', oauthToken);
loginUrl.searchParams.set('sitename', 'project_site_01');
loginUrl.searchParams.set('theme', 'blue');
loginUrl.searchParams.set('isheader', '1');

// 重定向到登錄頁面
window.location.href = loginUrl.toString();
```

## 9. 相關接口

### 9.1 相關認證接口
- `POST /bi/auth/login` - 表單登錄接口
- `POST /bi/auth/logout` - 登出接口
- `GET /bi/auth/list-current-org-permission` - 獲取用戶權限列表

### 9.2 依賴服務
- OAuth 驗證服務：用於驗證外部 token
- 用戶會話服務：管理用戶登錄狀態
- 權限服務：驗證用戶站點訪問權限

## 10. 注意事項

1. **重定向行為**: 該接口不返回 JSON 數據，而是執行 HTTP 重定向
2. **參數處理**: 所有可選參數都有默認值，確保重定向 URL 的完整性
3. **編碼處理**: 重定向參數必須進行 URL 編碼，特別是中文字符
4. **權限驗證**: 用戶必須對指定站點有訪問權限，否則會拋出異常
5. **Token 生命週期**: 本地生成的 token 有過期時間，需要定期刷新
