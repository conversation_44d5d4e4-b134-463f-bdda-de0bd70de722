#
# There is insufficient memory for the Java Runtime Environment to continue.
# Native memory allocation (mmap) failed to map 266338304 bytes. Error detail: G1 virtual space
# Possible reasons:
#   The system is out of physical RAM or swap space
#   This process is running with CompressedOops enabled, and the Java Heap may be blocking the growth of the native heap
# Possible solutions:
#   Reduce memory load on the system
#   Increase physical memory or swap space
#   Check if swap backing store is full
#   Decrease Java heap size (-Xmx/-Xms)
#   Decrease number of Java threads
#   Decrease Java thread stack sizes (-Xss)
#   Set larger code cache with -XX:ReservedCodeCacheSize=
#   JVM is running with Zero Based Compressed Oops mode in which the Java heap is
#     placed in the first 32GB address space. The Java Heap base address is the
#     maximum limit for the native heap growth. Please use -XX:HeapBaseMinAddress
#     to set the Java Heap base and to place the Java Heap above 32GB virtual address.
# This output file may be truncated or incomplete.
#
#  Out of Memory Error (os_windows.cpp:3898), pid=25216, tid=13376
#
# JRE version:  (21.0.6+8) (build )
# Java VM: OpenJDK 64-Bit Server VM (21.0.6+8-b631.39, mixed mode, sharing, tiered, compressed oops, compressed class ptrs, g1 gc, windows-amd64)
# No core dump will be written. Minidumps are not enabled by default on client versions of Windows
#

---------------  S U M M A R Y ------------

Command Line: git4idea.http.GitAskPassApp Username for 'https://git.csci.com.hk': 

Host: Intel(R) Core(TM) i7-10700T CPU @ 2.00GHz, 16 cores, 15G,  Windows 10 , 64 bit Build 19041 (10.0.19041.2913)
Time: Wed Jul  2 14:45:06 2025 China Standard Time elapsed time: 0.049979 seconds (0d 0h 0m 0s)

---------------  T H R E A D  ---------------

Current thread (0x000001638b4376c0):  JavaThread "Unknown thread" [_thread_in_vm, id=13376, stack(0x00000017e1300000,0x00000017e1400000) (1024K)]

Stack: [0x00000017e1300000,0x00000017e1400000]
Native frames: (J=compiled Java code, j=interpreted, Vv=VM code, C=native code)
V  [jvm.dll+0x6e5d39]
V  [jvm.dll+0x8c4133]
V  [jvm.dll+0x8c668e]
V  [jvm.dll+0x8c6d73]
V  [jvm.dll+0x288f76]
V  [jvm.dll+0x6e25f5]
V  [jvm.dll+0x6d60aa]
V  [jvm.dll+0x3635bb]
V  [jvm.dll+0x36b186]
V  [jvm.dll+0x3bd4b6]
V  [jvm.dll+0x3bd788]
V  [jvm.dll+0x335d2c]
V  [jvm.dll+0x336a1b]
V  [jvm.dll+0x88b589]
V  [jvm.dll+0x3ca688]
V  [jvm.dll+0x874698]
V  [jvm.dll+0x45f04e]
V  [jvm.dll+0x460d31]
C  [jli.dll+0x52ab]
C  [ucrtbase.dll+0x21bb2]
C  [KERNEL32.DLL+0x17614]
C  [ntdll.dll+0x526a1]


---------------  P R O C E S S  ---------------

Threads class SMR info:
_java_thread_list=0x00007ffee92da148, length=0, elements={
}

Java Threads: ( => current thread )
Total: 0

Other Threads:
  0x000001638b49da10 WorkerThread "GC Thread#0"                     [id=24496, stack(0x00000017e1400000,0x00000017e1500000) (1024K)]
  0x000001638b4b0850 ConcurrentGCThread "G1 Main Marker"            [id=17104, stack(0x00000017e1500000,0x00000017e1600000) (1024K)]
  0x000001638b4b1af0 WorkerThread "G1 Conc#0"                       [id=2252, stack(0x00000017e1600000,0x00000017e1700000) (1024K)]

[error occurred during error reporting (printing all threads), id 0xc0000005, EXCEPTION_ACCESS_VIOLATION (0xc0000005) at pc=0x00007ffee89c8de7]
VM state: not at safepoint (not fully initialized)

VM Mutex/Monitor currently owned by a thread:  ([mutex/lock_event])
[0x00007ffee934eb30] Heap_lock - owner thread: 0x000001638b4376c0

Heap address: 0x0000000703600000, size: 4042 MB, Compressed Oops mode: Zero based, Oop shift amount: 3

CDS archive(s) mapped at: [0x0000000000000000-0x0000000000000000-0x0000000000000000), size 0, SharedBaseAddress: 0x0000000800000000, ArchiveRelocationMode: 1.
Narrow klass base: 0x0000000000000000, Narrow klass shift: 0, Narrow klass range: 0x0

GC Precious Log:
 CardTable entry size: 512
 Card Set container configuration: InlinePtr #cards 4 size 8 Array Of Cards #cards 16 size 48 Howl #buckets 8 coarsen threshold 3686 Howl Bitmap #cards 512 size 80 coarsen threshold 460 Card regions per heap region 1 cards per card region 4096

Heap:
 garbage-first heap   total 0K, used 0K [0x0000000703600000, 0x0000000800000000)
  region size 2048K, 0 young (0K), 0 survivors (0K)
 Metaspace       used 0K, committed 0K, reserved 0K
  class space    used 0K, committed 0K, reserved 0K

Heap Regions: E=young(eden), S=young(survivor), O=old, HS=humongous(starts), HC=humongous(continues), CS=collection set, F=free, TAMS=top-at-mark-start, PB=parsable bottom

Card table byte_map: [0x000001639f460000,0x000001639fc50000] _byte_map_base: 0x000001639bc45000

Marking Bits: (CMBitMap*) 0x000001638b49e110
 Bits: [0x000001639fc50000, 0x00000163a3b78000)

GC Heap History (0 events):
No events

Dll operation events (1 events):
Event: 0.027 Loaded shared library C:\Program Files\JetBrains\IntelliJ IDEA Community Edition 2024.3.5\jbr\bin\java.dll

Deoptimization events (0 events):
No events

Classes loaded (0 events):
No events

Classes unloaded (0 events):
No events

Classes redefined (0 events):
No events

Internal exceptions (0 events):
No events

ZGC Phase Switch (0 events):
No events

VM Operations (0 events):
No events

Memory protections (0 events):
No events

Nmethod flushes (0 events):
No events

Events (0 events):
No events


Dynamic libraries:
0x00007ff77c040000 - 0x00007ff77c04a000 	C:\Program Files\JetBrains\IntelliJ IDEA Community Edition 2024.3.5\jbr\bin\java.exe
0x00007fff4ac90000 - 0x00007fff4ae88000 	C:\windows\SYSTEM32\ntdll.dll
0x00007fff49cc0000 - 0x00007fff49d7f000 	C:\windows\System32\KERNEL32.DLL
0x00007fff48730000 - 0x00007fff48a26000 	C:\windows\System32\KERNELBASE.dll
0x00007fff48bc0000 - 0x00007fff48cc0000 	C:\windows\System32\ucrtbase.dll
0x00007fff2e050000 - 0x00007fff2e06b000 	C:\Program Files\JetBrains\IntelliJ IDEA Community Edition 2024.3.5\jbr\bin\VCRUNTIME140.dll
0x00007fff2ddf0000 - 0x00007fff2de08000 	C:\Program Files\JetBrains\IntelliJ IDEA Community Edition 2024.3.5\jbr\bin\jli.dll
0x00007fff49410000 - 0x00007fff495ad000 	C:\windows\System32\USER32.dll
0x00007fff48390000 - 0x00007fff483b2000 	C:\windows\System32\win32u.dll
0x00007fff4abf0000 - 0x00007fff4ac1c000 	C:\windows\System32\GDI32.dll
0x00007fff48a30000 - 0x00007fff48b45000 	C:\windows\System32\gdi32full.dll
0x00007fff34150000 - 0x00007fff343ea000 	C:\windows\WinSxS\amd64_microsoft.windows.common-controls_6595b64144ccf1df_6.0.19041.1110_none_60b5254171f9507e\COMCTL32.dll
0x00007fff485e0000 - 0x00007fff4867d000 	C:\windows\System32\msvcp_win.dll
0x00007fff4a030000 - 0x00007fff4a0ce000 	C:\windows\System32\msvcrt.dll
0x00007fff4a760000 - 0x00007fff4a790000 	C:\windows\System32\IMM32.DLL
0x00007fff2e040000 - 0x00007fff2e04c000 	C:\Program Files\JetBrains\IntelliJ IDEA Community Edition 2024.3.5\jbr\bin\vcruntime140_1.dll
0x00007fff072b0000 - 0x00007fff0733d000 	C:\Program Files\JetBrains\IntelliJ IDEA Community Edition 2024.3.5\jbr\bin\msvcp140.dll
0x00007ffee8680000 - 0x00007ffee9441000 	C:\Program Files\JetBrains\IntelliJ IDEA Community Edition 2024.3.5\jbr\bin\server\jvm.dll
0x00007fff4a280000 - 0x00007fff4a32f000 	C:\windows\System32\ADVAPI32.dll
0x00007fff4a330000 - 0x00007fff4a3cc000 	C:\windows\System32\sechost.dll
0x00007fff49f00000 - 0x00007fff4a026000 	C:\windows\System32\RPCRT4.dll
0x00007fff49e90000 - 0x00007fff49efb000 	C:\windows\System32\WS2_32.dll
0x00007fff47ee0000 - 0x00007fff47f2b000 	C:\windows\SYSTEM32\POWRPROF.dll
0x00007fff28ca0000 - 0x00007fff28cc7000 	C:\windows\SYSTEM32\WINMM.dll
0x00007fff41720000 - 0x00007fff4172a000 	C:\windows\SYSTEM32\VERSION.dll
0x00007fff47dd0000 - 0x00007fff47de2000 	C:\windows\SYSTEM32\UMPDC.dll
0x00007fff46b20000 - 0x00007fff46b32000 	C:\windows\SYSTEM32\kernel.appcore.dll
0x00007fff2e030000 - 0x00007fff2e03a000 	C:\Program Files\JetBrains\IntelliJ IDEA Community Edition 2024.3.5\jbr\bin\jimage.dll
0x00007fff46640000 - 0x00007fff46824000 	C:\windows\SYSTEM32\DBGHELP.DLL
0x00007fff2cbe0000 - 0x00007fff2cc14000 	C:\windows\SYSTEM32\dbgcore.DLL
0x00007fff48550000 - 0x00007fff485d2000 	C:\windows\System32\bcryptPrimitives.dll
0x00007fff2e010000 - 0x00007fff2e030000 	C:\Program Files\JetBrains\IntelliJ IDEA Community Edition 2024.3.5\jbr\bin\java.dll

dbghelp: loaded successfully - version: 4.0.5 - missing functions: none
symbol engine: initialized successfully - sym options: 0x614 - pdb path: .;C:\Program Files\JetBrains\IntelliJ IDEA Community Edition 2024.3.5\jbr\bin;C:\windows\SYSTEM32;C:\windows\WinSxS\amd64_microsoft.windows.common-controls_6595b64144ccf1df_6.0.19041.1110_none_60b5254171f9507e;C:\Program Files\JetBrains\IntelliJ IDEA Community Edition 2024.3.5\jbr\bin\server

VM Arguments:
java_command: git4idea.http.GitAskPassApp Username for 'https://git.csci.com.hk': 
java_class_path (initial): C:/Program Files/JetBrains/IntelliJ IDEA Community Edition 2024.3.5/plugins/vcs-git/lib/git4idea-rt.jar;C:/Program Files/JetBrains/IntelliJ IDEA Community Edition 2024.3.5/lib/externalProcess-rt.jar
Launcher Type: SUN_STANDARD

[Global flags]
     intx CICompilerCount                          = 12                                        {product} {ergonomic}
     uint ConcGCThreads                            = 3                                         {product} {ergonomic}
     uint G1ConcRefinementThreads                  = 13                                        {product} {ergonomic}
   size_t G1HeapRegionSize                         = 2097152                                   {product} {ergonomic}
    uintx GCDrainStackTargetSize                   = 64                                        {product} {ergonomic}
   size_t InitialHeapSize                          = 266338304                                 {product} {ergonomic}
   size_t MarkStackSize                            = 4194304                                   {product} {ergonomic}
   size_t MaxHeapSize                              = 4238344192                                {product} {ergonomic}
   size_t MinHeapDeltaBytes                        = 2097152                                   {product} {ergonomic}
   size_t MinHeapSize                              = 8388608                                   {product} {ergonomic}
    uintx NonNMethodCodeHeapSize                   = 7602480                                {pd product} {ergonomic}
    uintx NonProfiledCodeHeapSize                  = 122027880                              {pd product} {ergonomic}
    uintx ProfiledCodeHeapSize                     = 122027880                              {pd product} {ergonomic}
    uintx ReservedCodeCacheSize                    = 251658240                              {pd product} {ergonomic}
     bool SegmentedCodeCache                       = true                                      {product} {ergonomic}
   size_t SoftMaxHeapSize                          = 4238344192                             {manageable} {ergonomic}
     bool UseCompressedOops                        = true                           {product lp64_product} {ergonomic}
     bool UseG1GC                                  = true                                      {product} {ergonomic}
     bool UseLargePagesIndividualAllocation        = false                                  {pd product} {ergonomic}

Logging:
Log output configuration:
 #0: stdout all=warning uptime,level,tags foldmultilines=false
 #1: stderr all=off uptime,level,tags foldmultilines=false

Environment Variables:
JAVA_HOME=D:\JAVA\openjdk-20+36_windows-x64_bin\jdk-20
PATH=C:/Program Files/Git/mingw64/libexec/git-core;C:/Program Files/Git/mingw64/libexec/git-core;C:\Program Files\Git\mingw64\bin;C:\Program Files\Git\usr\bin;C:\Users\<USER>\bin;C:\Python313\Scripts\;C:\Python313\;c:\Users\<USER>\AppData\Local\Programs\cursor\resources\app\bin;C:\Program Files\Oracle Client for Microsoft Tools\;D:\JAVA\openjdk-20+36_windows-x64_bin\jdk-20\bin;C:\windows\system32;C:\windows;C:\windows\System32\Wbem;C:\windows\System32\WindowsPowerShell\v1.0\;C:\windows\System32\OpenSSH\;C:\Program Files\PuTTY\;C:\Program Files\Microsoft SQL Server\150\Tools\Binn\;C:\Program Files\Microsoft SQL Server\Client SDK\ODBC\170\Tools\Binn\;C:\Program Files\dotnet\;C:\Program Files\Microsoft SQL Server\130\Tools\Binn\;C:\Program Files (x86)\Microsoft SQL Server\110\DTS\Binn\;C:\Program Files (x86)\Microsoft SQL Server\120\DTS\Binn\;C:\Program Files (x86)\Microsoft SQL Server\130\DTS\Binn\;C:\Program Files (x86)\Microsoft SQL Server\140\DTS\Binn\;C:\Program Files (x86)\Microsoft SQL Server\150\DTS\Binn\;C:\Program Files (x86)\Devart\EntityDeveloper\Console;C:\ProgramData\chocolatey\bin;C:\Program Files\Azure Data Studio\bin;D:\maven\apache-maven-3.8.5\bin;C:\;C:\Program Files\Docker\Docker\resources\bin;C:\Program Files\nodejs\;C:\Program Files\Git\cmd;C:\Users\<USER>\AppData\Local\Microsoft\WindowsApps;C:\Program Files\Azure Data Studio\bin;C:\Users\<USER>\AppData\Local\Programs\Microsoft VS Code\bin;C:\Users\<USER>\.dotnet\tools;C:\Program Files\Docker\Docker\resources;C:\Users\<USER>\AppData\Roaming\npm;C:\Users\<USER>\AppData\Local\Programs\cursor\resources\app\bin
USERNAME=kachun.chau
DISPLAY=:0.0
LC_ALL=en_US.UTF-8
TERM=xterm-256color
TMPDIR=C:\Users\<USER>\AppData\Local\Temp
OS=Windows_NT
PROCESSOR_IDENTIFIER=Intel64 Family 6 Model 165 Stepping 5, GenuineIntel
TMP=C:\Users\<USER>\AppData\Local\Temp
TEMP=C:\Users\<USER>\AppData\Local\Temp




Periodic native trim disabled

JNI global refs:
JNI global refs: 0, weak refs: 0

JNI global refs memory usage: 0, weak refs: 0

Process memory usage:
Resident Set Size: 10960K (0% of 16548064K total physical memory with 4288676K free physical memory)

OOME stack traces (most recent first):
Classloader memory used:

---------------  S Y S T E M  ---------------

OS:
 Windows 10 , 64 bit Build 19041 (10.0.19041.2913)
OS uptime: 5 days 21:41 hours
Hyper-V role detected

CPU: total 16 (initial active 16) (8 cores per cpu, 2 threads per core) family 6 model 165 stepping 5 microcode 0xf6, cx8, cmov, fxsr, ht, mmx, 3dnowpref, sse, sse2, sse3, ssse3, sse4.1, sse4.2, popcnt, lzcnt, tsc, tscinvbit, avx, avx2, aes, erms, clmul, bmi1, bmi2, adx, fma, vzeroupper, clflush, clflushopt, hv, rdtscp, f16c
Processor Information for the first 16 processors :
  Max Mhz: 1992, Current Mhz: 1992, Mhz Limit: 1992

Memory: 4k page, system-wide physical 16160M (4188M free)
TotalPageFile size 65312M (AvailPageFile size 232M)
current process WorkingSet (physical memory assigned to process): 10M, peak: 10M
current process commit charge ("private bytes"): 70M, peak: 324M

vm_info: OpenJDK 64-Bit Server VM (21.0.6+8-b631.39) for windows-amd64 JRE (21.0.6+8-b631.39), built on 2025-02-04 by "builduser" with MS VC++ 16.10 / 16.11 (VS2019)

END.
