pipeline {
  agent {
    kubernetes {
      yaml '''
apiVersion: v1
kind: Pod
metadata:
  labels:
    jenkins: true
spec:
  containers:
  - name: kubectl
    image: lachlane<PERSON>son/k8s-kubectl
    command:
    - cat
    tty: true
  - name: docker
    image: docker:stable
    tty: true
    env:
      - name: DOCKER_HOST
        value: "unix:///var/run/docker-ci/docker.sock"
    volumeMounts:
      - mountPath: /var/run/docker-ci
        name: host
  volumes:
  - name: host
    hostPath:
      path: /var/run/docker-ci
'''
    }

  }
  stages {
    stage('Checkout') {
      steps {
        checkout(scm)
      }
    }

    stage('Docker Build') {
      steps {
        container('docker') {
          sh "docker build -t ${REGISTRY}/${DOCKERHUB_NAMESPACE}/${APP_NAME}:SNAPSHOT-${BRANCH_NAME}-${BUILD_NUMBER} -f conf/DockerfileProd ."
        }
      }
    }

    stage('Push Image') {
      steps {
        container('docker') {
          script {
            docker.withRegistry("https://${REGISTRY}", 'docker-password') {
              sh "docker push ${REGISTRY}/${DOCKERHUB_NAMESPACE}/${APP_NAME}:SNAPSHOT-${BRANCH_NAME}-${BUILD_NUMBER}"
            }
          }
        }
      }
    }

    stage('Deploy To Kubernetes') {
      agent none
      steps {
        container('kubectl') {
          withCredentials([kubeconfigFile(credentialsId: 'kubeconfig', variable: 'KUBECONFIG')]) {
            sh 'kubectl set image deployment/${APP_NAME} app=${REGISTRY}/${DOCKERHUB_NAMESPACE}/${APP_NAME}:SNAPSHOT-${BRANCH_NAME}-${BUILD_NUMBER} -n ${PROJECT_NAME}'
          }
        }
      }
    }
  }

  environment {
    REGISTRY = 'docker.paas.3311csci.com'
    DOCKERHUB_NAMESPACE = 'csci'
    APP_NAME = 'esg'
    PROJECT_NAME = "esg"
  }
}