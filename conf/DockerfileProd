ARG Environment=prod
FROM maven:3.8.1-openjdk-17 AS build
COPY src /usr/app/src
COPY pom.xml /usr/app
COPY conf/settings.xml /usr/share/maven/conf/settings.xml

WORKDIR /usr/app

RUN mvn package -DskipTests

FROM openjdk:17.0.1-jdk-bullseye
RUN echo "deb http://mirrors.ustc.edu.cn/debian/ bullseye main contrib non-free" > /etc/apt/sources.list \
    && echo "deb http://mirrors.ustc.edu.cn/debian-security bullseye-security main" >> /etc/apt/sources.list \
    && echo "deb http://mirrors.ustc.edu.cn/debian/ bullseye-updates main contrib non-free" >> /etc/apt/sources.list
ENV TZ=Asia/Shanghai
RUN apt-get update && apt-get install -y tzdata && \
    ln -sf /usr/share/zoneinfo/$TZ /etc/localtime && \
    echo $TZ > /etc/timezone
RUN apt-get install -y fonts-dejavu
COPY --from=build /usr/app/target/*.jar /usr/app/app.jar
WORKDIR /usr/app
EXPOSE 8091
ENTRYPOINT ["java","-jar","-Dspring.profiles.active=prod","app.jar"]