package com.csci.susdev.facade;

import com.csci.susdev.BaseTest;
import org.junit.jupiter.api.Test;
import org.springframework.test.context.ActiveProfiles;

import javax.annotation.Resource;

@ActiveProfiles("prod")
class SocialPerformanceFacadeTest extends BaseTest {

    @Resource
    private SocialPerformanceFacade socialPerformanceFacade;

    @Test
    void correctReportItem() {
        // 只需要执行一次就行了，已经执行过了
        // socialPerformanceFacade.correctReportItem();
    }
}