package com.csci.susdev.facade;

import com.csci.susdev.BaseTest;
import org.junit.jupiter.api.Test;
import org.springframework.test.context.ActiveProfiles;

import javax.annotation.Resource;

import static org.junit.jupiter.api.Assertions.*;

@ActiveProfiles("prod")
class AmbientSummaryFacadeTest extends BaseTest {

    @Resource
    AmbientSummaryFacade ambientSummaryFacade;

    @Test
    void summaryOrgData() {
        ambientSummaryFacade.summaryOrgData("1a8428fa-f392-4ce5-9265-feed8b07200e", 2022, 1);
    }

    @Test
    void add() {
        System.out.println(ambientSummaryFacade.add("1", "2"));
    }
}