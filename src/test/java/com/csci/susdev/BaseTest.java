package com.csci.susdev;

import com.csci.susdev.model.User;
import com.csci.susdev.service.UserService;
import com.csci.susdev.util.SpringContextUtil;
import com.csci.susdev.util.context.RequestContextManager;
import com.csci.susdev.util.context.impl.RequestContext;
import com.csci.susdev.util.context.model.UserInfo;
import org.apache.commons.codec.digest.DigestUtils;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.MockitoAnnotations;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.TestPropertySource;

import java.util.HashMap;

@TestPropertySource(properties = "app.scheduling.enable=false")
@SpringBootTest
public class BaseTest {

    @Test
    public void contextLoads() {
    }

    @BeforeEach
    public void init() {
        UserService userService = SpringContextUtil.getBean(UserService.class);
        User user = userService.getUserByUsername("tao_li");
        UserInfo userInfo = new UserInfo();
        userInfo.setId(user.getId());
        userInfo.setUsername(user.getUsername());
        userInfo.setName(user.getName());
        RequestContextManager.setCurrent(new RequestContext(new HashMap<>(), userInfo));
        MockitoAnnotations.initMocks(this);
    }

    @Test
    void test() {
        String md5 = DigestUtils.md5Hex("建築廢棄物-自身項目地盤/工地再利用 (*1)建築廢棄物-自身項目地盤/工地再利用 (*1)建築廢棄物-自身項目地盤/工地再利用 (*1)建築廢棄物-自身項目地盤/工地再利用 (*1)");
        System.out.println(md5);
        System.out.println(md5.length());
    }
}
