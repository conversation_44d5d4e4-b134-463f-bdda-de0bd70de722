package com.csci.susdev.util;

import com.csci.susdev.BaseTest;
import org.junit.jupiter.api.Test;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import javax.annotation.Resource;

import static org.junit.jupiter.api.Assertions.assertTrue;

class OnceTokenManagerTest extends BaseTest {

    /**
     * 日志记录对象
     */
    private static final Logger logger = LoggerFactory.getLogger(OnceTokenManagerTest.class);

    @Resource
    private OnceTokenManager onceTokenManager;

    @Test
    void generateOnceToken() {
        String onceToken = onceTokenManager.generateOnceToken();
        logger.info("onceToken: {}", onceToken);
    }

    @Test
    void checkOnceToken() throws InterruptedException {
        String onceToken = onceTokenManager.generateOnceToken();
        logger.info("onceToken: {}", onceToken);
        boolean result = onceTokenManager.checkOnceToken(onceToken);
        logger.info("result: {}", result);
        assertTrue(result);
        result = onceTokenManager.checkOnceToken(onceToken);
        logger.info("result: {}", result);
    }
}