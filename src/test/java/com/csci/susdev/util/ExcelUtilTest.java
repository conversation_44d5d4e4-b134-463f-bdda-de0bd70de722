package com.csci.susdev.util;

import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.support.ExcelTypeEnum;
import com.csci.susdev.excel.ExcelEventListener;
import com.csci.susdev.mapper.ProcedureMapper;
import com.csci.susdev.vo.TExcelVO;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;

import javax.annotation.Resource;

/**
 * @description:
 * @author: barry
 * @create: 2024-11-20 09:36
 */
@SpringBootTest
@ActiveProfiles("prod")
public class ExcelUtilTest {
    @Resource
    private ProcedureMapper procedureMapper;

    @Test
    public void test() {
//        String classPathTemplateFileName="/Users/<USER>/Desktop/环境绩效/碳盘查/模板文件-房屋建筑工程(2).xlsx";
        String classPathTemplateFileName="/Users/<USER>/Desktop/环境绩效/碳盘查/模板文件-办公场所.xlsx";

        EasyExcel.read(classPathTemplateFileName, TExcelVO.class, new ExcelEventListener()).excelType(ExcelTypeEnum.XLSX).sheet().doRead();

    }
}
