package com.csci.susdev.service;

import com.csci.susdev.BaseTest;
import com.csci.susdev.vo.BatchWithEmissionFactorVO;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;

import java.time.LocalDateTime;

class BatchServiceTest extends BaseTest {

    @Autowired
    BatchService batchService;

    @Test
    void saveBatch() {

        BatchWithEmissionFactorVO batchVO = new BatchWithEmissionFactorVO();
        // batchVO.setId();
        batchVO.setAreaId("3bb08041-e7b6-4dd0-8338-42fea65052e0");
        // batchVO.setAreaName();
        // batchVO.setIsActive();
        batchVO.setEffectiveDate(LocalDateTime.now());
        // batchVO.setEmissionFactors();
        batchService.saveBatchWithEmissionFactors(batchVO);

    }
}