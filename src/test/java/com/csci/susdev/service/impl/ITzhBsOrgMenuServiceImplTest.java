package com.csci.susdev.service.impl;

import com.csci.susdev.model.TzhBsOrgMenu;
import com.csci.susdev.service.ITzhBsOrgMenuService;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.dao.DuplicateKeyException;

import javax.annotation.Resource;

import java.util.UUID;

import static org.junit.jupiter.api.Assertions.*;
@SpringBootTest
@Slf4j
class ITzhBsOrgMenuServiceImplTest {

    @Resource
    private ITzhBsOrgMenuService tzhBsOrgMenuService;
    @Test
    void testSave() {
        TzhBsOrgMenu tzhBsOrgMenu = new TzhBsOrgMenu();
        String id = UUID.randomUUID().toString();
        String oid = UUID.randomUUID().toString();
        String pid = UUID.randomUUID().toString();
        System.out.println("id = " + id);
        tzhBsOrgMenu.setId(id);
        tzhBsOrgMenu.setOrganizationId(oid);
        tzhBsOrgMenu.setProtocolId(pid);
        tzhBsOrgMenu.setSeq( 123);

            tzhBsOrgMenuService.save(tzhBsOrgMenu);
            tzhBsOrgMenuService.save(tzhBsOrgMenu);

    }

    @Test
    void testEdit() {
    }

    @Test
    void testDelete() {
    }

    @Test
    void testGetAll() {
    }

    @Test
    void testDeleteById() {
    }

    @Test
    void save() {
    }

    @Test
    void edit() {
    }

    @Test
    void delete() {
    }

    @Test
    void getAll() {
    }

    @Test
    void deleteById() {
    }
}
