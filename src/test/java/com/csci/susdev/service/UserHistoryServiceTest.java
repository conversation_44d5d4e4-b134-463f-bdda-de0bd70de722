package com.csci.susdev.service;

import com.csci.common.util.CustomGsonBuilder;
import com.csci.susdev.BaseTest;
import com.csci.susdev.model.User;
import com.csci.susdev.model.UserHistory;
import com.csci.susdev.modelcovt.UserHistoryConverter;
import com.google.gson.Gson;
import org.junit.jupiter.api.Test;

import javax.annotation.Resource;

import static org.junit.jupiter.api.Assertions.*;

class UserHistoryServiceTest extends BaseTest {

    private final Gson gson = CustomGsonBuilder.createGson();

    @Resource
    private UserHistoryService userHistoryService;

    @Resource
    private UserService userService;

    @Test
    void insertSelective() {
        User user = userService.getUserByUsername("tao_li");
        userHistoryService.insertSelective(UserHistoryConverter.convert(user));
    }

    @Test
    void selectByExampleWithBLOBs() {
    }

    @Test
    void selectByExample() {
    }
}