package com.csci.cohl.model;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDateTime;

@Schema
@Data
@TableName(value = "dw_830_bipvdb_830ESG_total")
public class Dw830Bipvdb830esgTotal {


    @TableField(value = "總用電量")
    @Schema(description = "总用电量")
    private BigDecimal totalEleUsage;


    @Schema(description = "总发电量")
    private BigDecimal totalPowerGenFrom202304;


    @Schema(description = "自发自用")
    private BigDecimal selfUse;

    @TableField(value = "绿电占比",exist = false)
    @Schema(description = "绿电占比")
    private BigDecimal greenEleProportion;

    @TableField(value = "消纳比例",exist = false)
    @Schema(description = "消纳比例")
    private BigDecimal consumptionRatio;

    @TableField(value = "累计发电量")
    @Schema(description = "累計發電量")
    private BigDecimal sumPowerGeneration;

    @TableField(value = "累計收益")
    @Schema(description = "累計收益")
    private BigDecimal sumIncome;

    @TableField(value = "實時功率")
    @Schema(description = "實時功率")
    private BigDecimal realTimePower;

    @TableField(value = "裝機容量")
    @Schema(description = "裝機容量")
    private BigDecimal installedCapacity;


    @Schema(description = "功率容量比")
    private BigDecimal powerCapacityRatio;

    @TableField(value = "上網電量")
    @Schema(description = "上網電量")
    private BigDecimal onlineEleUsage;

    @TableField(value = "calculate_time")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime calculateTime;
}
