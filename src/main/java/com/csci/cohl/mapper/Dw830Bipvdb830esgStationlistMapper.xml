<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.csci.cohl.mapper.Dw830Bipvdb830esgStationlistMapper">
  <resultMap id="BaseResultMap" type="com.csci.cohl.model.Dw830Bipvdb830esgStationlist">
    <!--@mbg.generated-->
    <!--@Table dw_830_bipvdb_830ESG_Stationlist-->
    <result column="電站名稱" jdbcType="VARCHAR" property="powerStationName" />
    <result column="裝機容量" jdbcType="NUMERIC" property="installedCapacity" />
    <result column="當日發電量" jdbcType="FLOAT" property="powerGenOnTheDay" />
    <result column="累計發電量" jdbcType="FLOAT" property="powerGenTotal" />
    <result column="實時功率" jdbcType="FLOAT" property="realTimePower" />
    <result column="電站狀態" jdbcType="VARCHAR" property="powerStationStatus" />
    <result column="calculate_time" jdbcType="TIMESTAMP" property="calculateTime" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    電站名稱, 裝機容量, 當日發電量, 累計發電量, 實時功率, 電站狀態, calculate_time
  </sql>

  <select id="getPowerStationList" resultMap="BaseResultMap">
      select *
      from (
               SELECT s.*,
                      ROW_NUMBER() over (PARTITION by 電站名稱 order by s.calculate_time desc) as rn
            from dw_830_bipvdb_830ESG_Stationlist s) as t
      where t.rn = 1
      order by t.累計發電量 desc
  </select>
</mapper>
