package com.csci.cohl.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.csci.cohl.beans.vo.TzhBsRouteVO;
import com.csci.cohl.model.TzhBsRoute;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;

public interface BiTzhBsRouteMapper extends BaseMapper<TzhBsRoute> {

    @Select("""
            SELECT R.*, P.Name AS Protocol, P.name_sc AS ProtocolSC, P.name_en AS ProtocolEN 
            FROM Tzh_Bs_Route R
            LEFT JOIN ESG.dbo.t_protocol P ON R.ProtocolId = P.Id 
            WHERE  R.IsDeleted = 0 AND R.SiteName = #{siteName} AND P.name_en = #{protocol}
            ORDER BY SEQ
            """)
    List<TzhBsRouteVO> listRoute(@Param("siteName") String siteName, @Param("protocol") String protocol);

    @Select("""
            SELECT R.*, P.Name AS Protocol, P.name_sc AS ProtocolSC, P.name_en AS ProtocolEN 
            FROM Tzh_Bs_Route R
            LEFT JOIN t_protocol P ON R.ProtocolId = P.Id 
            WHERE  R.IsDeleted = 0 AND R.SiteId = #{siteId} AND P.name_en = #{protocol}
            ORDER BY SEQ
            """)
    List<TzhBsRouteVO> listRouteById(@Param("siteId") String siteId, @Param("protocol") String protocol);
}
