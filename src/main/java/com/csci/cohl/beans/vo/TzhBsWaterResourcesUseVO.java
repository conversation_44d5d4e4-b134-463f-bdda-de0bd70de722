package com.csci.cohl.beans.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.experimental.Accessors;

import java.math.BigDecimal;

@Data
@Accessors(chain = true)
@Schema(description = "查询水资源使用排行榜返回VO对象")
public class TzhBsWaterResourcesUseVO {

    @Schema(description ="省份")
    private String province;
    @Schema(description ="省份（繁体）")
    private String provinceSc;
    @Schema(description ="省份（英文）")
    private String provinceEn;
    @Schema(description ="水资源总量")
    private BigDecimal waterResourcesAmount;
    @Schema(description ="水资源总量占比")
    private BigDecimal amountProportion;

}
