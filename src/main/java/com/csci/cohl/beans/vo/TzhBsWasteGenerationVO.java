package com.csci.cohl.beans.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.experimental.Accessors;

import java.math.BigDecimal;

@Data
@Accessors(chain = true)
@Schema(description = "废弃物产生量分布统计返回VO对象")
public class TzhBsWasteGenerationVO {

    @Schema(description ="省份")
    private String province;
    @Schema(description ="有害废弃物总量")
    private BigDecimal hazardousWasteCarbonAmount;
    @Schema(description ="无害废弃物总量")
    private BigDecimal nonHazardousWasteCarbonAmount;
    @Schema(description ="废弃物占比")
    private BigDecimal proportionOfWaste;
    @Schema(description ="省份（繁体）")
    private String provinceSc;
    @Schema(description ="省份（英文）")
    private String provinceEn;
    @Schema(description ="废弃物总量")
    private BigDecimal wasteCarbonAmount;
}
