package com.csci.cohl.exception;


import javax.servlet.http.HttpServletRequest;

import com.csci.cohl.service.impl.TzhBsApilogServiceImpl;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.FieldError;
import org.springframework.web.bind.MethodArgumentNotValidException;
import org.springframework.web.bind.annotation.ControllerAdvice;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.ResponseBody;


import java.io.IOException;

@Slf4j
//@ControllerAdvice
public class BiGlobalExceptionHandler {

	@Autowired
	private TzhBsApilogServiceImpl digitalMapNorthernApilogService;


	/**
	 * 处理自定义的业务异常
	 * @param req
	 * @param e
	 * @return
	 */
    @ExceptionHandler(value = BizException.class)
    @ResponseBody
	public  ResultBody bizExceptionHandler(HttpServletRequest req, BizException e){
    	log.error("发生业务异常！原因是：{}",e.getErrorMsg());
    	return ResultBody.error(e.getErrorCode(),e.getErrorMsg());
    }

	/**
	 * 处理自定义的无权限异常
	 * @param req
	 * @param e
	 * @return
	 */
	@ExceptionHandler(value = NoRuleException.class)
	@ResponseBody
	public ResultBody noRuleExceptionHandler(HttpServletRequest req, NoRuleException e) throws IOException {
		digitalMapNorthernApilogService.log(e.getErrorMsg());
		log.error("发生无权限异常！原因是：{}",e.getErrorMsg());
		return ResultBody.error(e.getErrorCode(),e.getErrorMsg());
	}

	/**
	 * 处理空指针的异常
	 * @param req
	 * @param e
	 * @return
	 */
	@ExceptionHandler(value =NullPointerException.class)
	@ResponseBody
	public ResultBody exceptionHandler(HttpServletRequest req, NullPointerException e){
		log.error("发生空指针异常！原因是:",e);
		return ResultBody.error(CommonEnum.BODY_NOT_MATCH);
	}


    /**
        * 处理其他异常
     * @param req
     * @param e
     * @return
     */
    @ExceptionHandler(value =Exception.class)
	@ResponseBody
	public ResultBody exceptionHandler(HttpServletRequest req, Exception e){
    	log.error("未知异常！原因是:",e);
       	return ResultBody.error(CommonEnum.INTERNAL_SERVER_ERROR);
    }

	/**
	 * 数据验证处理
	 * @param ex
	 * @return
	 */
	@ExceptionHandler(value = MethodArgumentNotValidException.class)
	@ResponseBody
	public ResultBody handleBindException(MethodArgumentNotValidException ex) {
		FieldError fieldError = ex.getBindingResult().getFieldError();
		log.error("数据验证错误！原因是:",ex);
		return ResultBody.error(CommonEnum.BODY_NOT_MATCH.getResultCode(), fieldError.getDefaultMessage());
	}
}
