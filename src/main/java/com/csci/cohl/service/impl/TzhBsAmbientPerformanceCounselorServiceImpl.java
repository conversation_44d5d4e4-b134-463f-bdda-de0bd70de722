package com.csci.cohl.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.csci.cohl.beans.dto.*;
import com.csci.cohl.beans.vo.*;
import com.csci.cohl.exception.ResultBody;
import com.csci.cohl.mapper.BiTzhBsUserSiteMapper;
import com.csci.cohl.mapper.TzhBsAmbientPerformanceCounselorMapper;
import com.csci.cohl.mapper.TzhBsEmissionReductionStatisticsMapper;
import com.csci.cohl.model.TzhEmissionReduction;
import com.csci.cohl.service.ITzhBsAmbientPerformanceCounselorService;
import com.csci.cohl.service.ITzhBsEmissionReductionStatisticsService;
import com.csci.common.exception.ServiceException;
import com.csci.susdev.constant.CacheConstants;
import com.csci.susdev.util.StringUtil;
import com.csci.susdev.util.redis.RedisUtil;
import com.csci.tzh.mapper.TzhEmissionReductionHeadMapper;
import com.csci.tzh.mapper.TzhEmissionReductionMapper;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Slf4j
@Service
public class TzhBsAmbientPerformanceCounselorServiceImpl extends BaseServiceImpl implements ITzhBsAmbientPerformanceCounselorService {

    @Resource
    private BiTzhBsUserSiteMapper biTzhBsUserSiteMapper;
    @Resource
    private TzhBsAmbientPerformanceCounselorMapper tzhBsAmbientPerformanceCounselorMapper;
    @Resource
    private RedisUtil redisUtil;
    @Value("${app.tzhbs.redisKey}")
    private String tzhbsRedisKey;

    /**
     * 查询能源使用排行榜
     * <AUTHOR>
     * @date 2025/1/6 12:24
     * @param dto
     * @return com.csci.cohl.exception.ResultBody<java.util.List<com.csci.cohl.beans.vo.TzhBsEnergyUseVO>>
     */
    @Override
    public ResultBody<List<TzhBsEnergyUseVO>> getEnergyUseRanking(TzhBsEnergyUseDTO dto) {
        String username = (String) request.getAttribute("username");
        // 先看redis有没有数据，如果有则取缓存数据
        long startTime = System.currentTimeMillis();
        Object cacheObj = redisUtil.get(tzhbsRedisKey + CacheConstants.ENERGY_USE_RANKING + dto.getProtocol() + "#" + dto.getOrganizationId() + "#" + dto.getStartMonth() + "#" + dto.getEndMonth());
        long endTime = System.currentTimeMillis();
        log.info("【查询能源使用排行榜】获取redis:{}秒", ((endTime - (double) startTime) / 1000));
        startTime = System.currentTimeMillis();
        List<TzhBsEnergyUseVO> lstVo = StringUtil.cast(cacheObj);
        endTime = System.currentTimeMillis();
        log.info("【查询能源使用排行榜】获取redis后转换对象:{}秒", ((endTime - (double) startTime) / 1000));
        if (CollectionUtils.isEmpty(lstVo)) {
            // 查询能源使用排行榜
            startTime = System.currentTimeMillis();
            lstVo = tzhBsAmbientPerformanceCounselorMapper.getEnergyUseRanking(dto);
            endTime = System.currentTimeMillis();
            log.info("【查询能源使用排行榜】查询能源使用排行榜:{}秒", ((endTime - (double) startTime) / 1000));
            if (CollectionUtils.isNotEmpty(lstVo)) {
                startTime = System.currentTimeMillis();
                redisUtil.set(tzhbsRedisKey + CacheConstants.ENERGY_USE_RANKING + dto.getProtocol() + "#" + dto.getOrganizationId() + "#" + dto.getStartMonth() + "#" + dto.getEndMonth(), lstVo);
                endTime = System.currentTimeMillis();
                log.info("【查询能源使用排行榜】查询能源使用排行榜:{}秒", ((endTime - (double) startTime) / 1000));
            }
        }
        return ResultBody.success(lstVo);
    }

    /**
     * 废弃物产生量分布统计
     * <AUTHOR>
     * @date 2025/1/6 14:20
     * @param dto
     * @return com.csci.cohl.exception.ResultBody<java.util.List<com.csci.cohl.beans.vo.TzhBsWasteGenerationVO>>
     */
    @Override
    public ResultBody<List<TzhBsWasteGenerationVO>> getWasteGenerationDistributionStatistics(TzhBsWasteGenerationDTO dto) {
        String username = (String) request.getAttribute("username");
        // 前置校验
        // checkUserSite(username, dto.getOrganizationName(), dto.getOrganizationId());
        // 先看redis有没有数据，如果有则取缓存数据
        long startTime = System.currentTimeMillis();
        Object cacheObj = redisUtil.get(tzhbsRedisKey + CacheConstants.Waste_Generation_Distribution_Statistics + dto.getProtocol() + "#" + dto.getOrganizationId() + "#" + dto.getStartMonth() + "#" + dto.getEndMonth());
        long endTime = System.currentTimeMillis();
        log.info("【废弃物产生量分布统计】获取redis:{}秒", ((endTime - (double) startTime) / 1000));
        List<TzhBsWasteGenerationVO> lstVo = StringUtil.cast(cacheObj);
        if (CollectionUtils.isEmpty(lstVo)) {
            // 查询废弃物产生量分布统计
            lstVo = tzhBsAmbientPerformanceCounselorMapper.getWasteGenerationDistributionStatistics(dto);
            if (CollectionUtils.isNotEmpty(lstVo)) {
                redisUtil.set(tzhbsRedisKey + CacheConstants.Waste_Generation_Distribution_Statistics + dto.getProtocol() + "#" + dto.getOrganizationId() + "#" + dto.getStartMonth() + "#" + dto.getEndMonth(), lstVo);
            }
        }

        return ResultBody.success(lstVo);
    }

    /**
     * 查询碳排放（按年统计，1+5年）
     * <AUTHOR>
     * @date 2025/1/6 15:00
     * @param dto
     * @return com.csci.cohl.exception.ResultBody<java.util.List<com.csci.cohl.beans.vo.TzhBsCarbonEmissionYearVO>>
     */
    @Override
    public ResultBody<List<TzhBsCarbonEmissionYearVO>> getCarbonEmissionYearSummary(TzhBsCarbonEmissionYearDTO dto) {
        String username = (String) request.getAttribute("username");
        // 前置校验
        // checkUserSite(username, dto.getOrganizationName(), dto.getOrganizationId());
        // 先看redis有没有数据，如果有则取缓存数据
        Object cacheObj = redisUtil.get(tzhbsRedisKey + CacheConstants.CARBON_EMISSION_YEAR_SUMMARY + dto.getProtocol() + "#" + dto.getOrganizationId() + "#" + dto.getQueryYear());
        List<TzhBsCarbonEmissionYearVO> lstVo = StringUtil.cast(cacheObj);
        if (CollectionUtils.isEmpty(lstVo)) {
            // 查询碳排放（按年统计，1+5年）
            lstVo = tzhBsAmbientPerformanceCounselorMapper.getCarbonEmissionYearSummary(dto);
            if (CollectionUtils.isNotEmpty(lstVo)) {
                redisUtil.set(tzhbsRedisKey + CacheConstants.CARBON_EMISSION_YEAR_SUMMARY + dto.getProtocol() + "#" + dto.getOrganizationId() + "#" + dto.getQueryYear(), lstVo);
            }
        }
        return ResultBody.success(lstVo);
    }

    /**
     * 查询原材料使用占比统计
     * <AUTHOR>
     * @date 2025/1/6 15:00
     * @param dto
     * @return com.csci.cohl.exception.ResultBody<java.util.List<com.csci.cohl.beans.vo.TzhBsRawMaterialUseVO>>
     */
    @Override
    public ResultBody<List<TzhBsRawMaterialUseVO>> getRawMaterialUseProportion(TzhBsRawMaterialUseDTO dto) {
        String username = (String) request.getAttribute("username");
        checkUserSite(username, dto.getOrganizationName(), dto.getOrganizationId());
        // 先看redis有没有数据，如果有则取缓存数据
        Object cacheObj = redisUtil.get(tzhbsRedisKey + CacheConstants.RAW_MATERIAL_USE_PROPORTION + dto.getProtocol() + "#" + dto.getOrganizationId() + "#" + dto.getStartMonth() + "#" + dto.getEndMonth());
        List<TzhBsRawMaterialUseVO> lstVo = StringUtil.cast(cacheObj);
        if (CollectionUtils.isEmpty(lstVo)) {
            // 查询原材料使用占比统计
            lstVo = tzhBsAmbientPerformanceCounselorMapper.getRawMaterialUseProportion(dto);
            if (CollectionUtils.isNotEmpty(lstVo)) {
                TzhBsRawMaterialUseVO tzhBsRawMaterialUseVO = lstVo.get(0);
                convertAndPrint(tzhBsRawMaterialUseVO);
                redisUtil.set(tzhbsRedisKey + CacheConstants.RAW_MATERIAL_USE_PROPORTION + dto.getProtocol() + "#" + dto.getOrganizationId() + "#" + dto.getStartMonth() + "#" + dto.getEndMonth(), lstVo);
            }
        }
        return ResultBody.success(lstVo);
    }

    /**
     * 查询资源使用趋势统计
     * <AUTHOR>
     * @date 2025/1/6 15:12
     * @param dto
     * @return com.csci.cohl.exception.ResultBody<java.util.List<com.csci.cohl.beans.vo.TzhBsResourceUseTrendStatisticsVO>>
     */
    @Override
    public ResultBody<List<TzhBsResourceUseTrendStatisticsVO>> getResourceUseTrendStatistics(TzhBsResourceUseTrendStatisticsDTO dto) {
        String username = (String) request.getAttribute("username");
        // 前置校验
        // checkUserSite(username, dto.getOrganizationName(), dto.getOrganizationId());
        // 先看redis有没有数据，如果有则取缓存数据
        Object cacheObj = redisUtil.get(tzhbsRedisKey + CacheConstants.RESOURCE_USE_TREND_STATISTICS + dto.getProtocol() + "#" + dto.getOrganizationId() + "#" + dto.getQueryYear());
        List<TzhBsResourceUseTrendStatisticsVO> lstVo = StringUtil.cast(cacheObj);
        if (CollectionUtils.isEmpty(lstVo)) {
            // 查询资源使用趋势统计
            lstVo = tzhBsAmbientPerformanceCounselorMapper.getResourceUseTrendStatistics(dto);
            if (CollectionUtils.isNotEmpty(lstVo)) {
                redisUtil.set(tzhbsRedisKey + CacheConstants.RESOURCE_USE_TREND_STATISTICS + dto.getProtocol() + "#" + dto.getOrganizationId() + "#" + dto.getQueryYear(), lstVo);
            }
        }
        return ResultBody.success(lstVo);
    }

    /**
     * 查询各类别密度
     * <AUTHOR>
     * @date 2025/1/7 14:38
     * @param dto
     * @return com.csci.cohl.exception.ResultBody<com.csci.cohl.beans.vo.TzhBsCategoryDensityVO>
     */
    @Override
    public ResultBody<TzhBsCategoryDensityVO> getCategoryDensity(TzhBsCategoryDensityDTO dto) {
        String username = (String) request.getAttribute("username");
        // 前置校验
        // checkUserSite(username, dto.getOrganizationName(), dto.getOrganizationId());
        // 先看redis有没有数据，如果有则取缓存数据
        Object cacheObj = redisUtil.get(tzhbsRedisKey + CacheConstants.CATEGORY_DENSITY + dto.getProtocol() + "#" + dto.getOrganizationId() + "#" + dto.getStartMonth() + "#" + dto.getEndMonth());
        TzhBsCategoryDensityVO tzhBsCategoryDensityVO = StringUtil.cast(cacheObj);
        if (tzhBsCategoryDensityVO == null) {
            // 查询各类别密度
            tzhBsCategoryDensityVO = tzhBsAmbientPerformanceCounselorMapper.getCategoryDensity(dto);
            if (tzhBsCategoryDensityVO != null) {
                redisUtil.set(tzhbsRedisKey + CacheConstants.CATEGORY_DENSITY + dto.getProtocol() + "#" + dto.getOrganizationId() + "#" + dto.getStartMonth() + "#" + dto.getEndMonth(), tzhBsCategoryDensityVO);
            }
        }
        return ResultBody.success(tzhBsCategoryDensityVO);
    }

    /**
     * 查询地图各省份数据
     * <AUTHOR>
     * @date 2025/1/7 14:48
     * @param dto
     * @return com.csci.cohl.exception.ResultBody<java.util.List<com.csci.cohl.beans.vo.TzhBsMapProvinceDataVO>>
     */
    @Override
    public ResultBody<List<TzhBsMapProvinceDataVO>> getMapProvinceData(TzhBsMapProvinceDataDTO dto) {
        String username = (String) request.getAttribute("username");
        // 前置校验
        // checkUserSite(username, dto.getOrganizationName(), dto.getOrganizationId());
        // 先看redis有没有数据，如果有则取缓存数据
        Object cacheObj = redisUtil.get(tzhbsRedisKey + CacheConstants.MAP_PROVINCE_DATA + dto.getProtocol() + "#" + dto.getOrganizationId() + "#" + dto.getOrganizationName() + "#" + dto.getStartMonth() + "#" + dto.getEndMonth());
        List<TzhBsMapProvinceDataVO> lstVo = StringUtil.cast(cacheObj);
        if (CollectionUtils.isEmpty(lstVo)) {
            // 查询地图各省份数据
            if ("中国建筑国际集团".equals(dto.getOrganizationName()) || "中建国际投资".equals(dto.getOrganizationName())
                    || "第五平台".equals(dto.getOrganizationName()) || "中建国际资管".equals(dto.getOrganizationName())) {
                lstVo = tzhBsAmbientPerformanceCounselorMapper.getMapProvinceData(dto);
            }else if ("中國建築興業".equals(dto.getOrganizationName())) {
                lstVo = tzhBsAmbientPerformanceCounselorMapper.getMapWorldData(dto);
            }else {
                lstVo = tzhBsAmbientPerformanceCounselorMapper.getMapAreaData(dto);
            }
            if (CollectionUtils.isNotEmpty(lstVo)) {
                redisUtil.set(tzhbsRedisKey + CacheConstants.MAP_PROVINCE_DATA + dto.getProtocol() + "#" + dto.getOrganizationId() + "#" + dto.getOrganizationName() + "#" + dto.getStartMonth() + "#" + dto.getEndMonth(), lstVo);
            }
        }

        return ResultBody.success(lstVo);
    }

    /**
     * 查询用水与污染
     * <AUTHOR>
     * @date 2025/1/7 15:06
     * @param dto
     * @return com.csci.cohl.exception.ResultBody<com.csci.cohl.beans.vo.TzhBsWaterUseAndPollutionVO>
     */
    @Override
    public ResultBody<TzhBsWaterUseAndPollutionVO> getWaterUseAndPollution(TzhBsWaterUseAndPollutionDTO dto) {
        String username = (String) request.getAttribute("username");
        // 前置校验
        // checkUserSite(username, dto.getOrganizationName(), dto.getOrganizationId());
        // 先看redis有没有数据，如果有则取缓存数据
        Object cacheObj = redisUtil.get(tzhbsRedisKey + CacheConstants.WATER_USE_AND_POLLUTION + dto.getProtocol() + "#" + dto.getOrganizationId() + "#" + dto.getStartMonth() + "#" + dto.getEndMonth());
        TzhBsWaterUseAndPollutionVO tzhBsWaterUseAndPollutionVO = StringUtil.cast(cacheObj);
        if (tzhBsWaterUseAndPollutionVO == null) {
            // 查询用水与污染
            tzhBsWaterUseAndPollutionVO = tzhBsAmbientPerformanceCounselorMapper.getWaterUseAndPollution(dto);
            if (tzhBsWaterUseAndPollutionVO != null) {
                redisUtil.set(tzhbsRedisKey + CacheConstants.WATER_USE_AND_POLLUTION + dto.getProtocol() + "#" + dto.getOrganizationId() + "#" + dto.getStartMonth() + "#" + dto.getEndMonth(), tzhBsWaterUseAndPollutionVO);
            }
        }
        return ResultBody.success(tzhBsWaterUseAndPollutionVO);
    }

    /**
     * 查询项目分布
     * <AUTHOR>
     * @date 2025/1/7 15:12
     * @param dto
     * @return com.csci.cohl.exception.ResultBody<java.util.List<com.csci.cohl.beans.vo.TzhBsProjectDistributionVO>>
     */
    @Override
    public ResultBody<List<TzhBsProjectDistributionVO>> getProjectDistribution(TzhBsProjectDistributionDTO dto) {
        String username = (String) request.getAttribute("username");
        // 前置校验
        // checkUserSite(username, dto.getOrganizationName(), dto.getOrganizationId());
        // 先看redis有没有数据，如果有则取缓存数据
        Object cacheObj = redisUtil.get(tzhbsRedisKey + CacheConstants.PROJECT_DISTRIBUTION + dto.getProtocol() + "#" + dto.getOrganizationId() + "#" + dto.getStartMonth() + "#" + dto.getEndMonth());
        List<TzhBsProjectDistributionVO> lstVo = StringUtil.cast(cacheObj);
        if (CollectionUtils.isEmpty(lstVo)) {
            // 查询项目分布
            lstVo = tzhBsAmbientPerformanceCounselorMapper.getProjectDistribution(dto);
            if (CollectionUtils.isNotEmpty(lstVo)) {
                redisUtil.set(tzhbsRedisKey + CacheConstants.PROJECT_DISTRIBUTION + dto.getProtocol() + "#" + dto.getOrganizationId() + "#" + dto.getStartMonth() + "#" + dto.getEndMonth(), lstVo);
            }
        }
        return ResultBody.success(lstVo);
    }

    /**
     * 查询电力使用排行榜
     * <AUTHOR>
     * @date 2025/1/16 14:44
     * @param dto
     * @return com.csci.cohl.exception.ResultBody<java.util.List<com.csci.cohl.beans.vo.TzhBsElectricityUseVO>>
     */
    @Override
    public ResultBody<List<TzhBsElectricityUseVO>> getElectricityUseRanking(TzhBsElectricityUseDTO dto) {
        // 先看redis有没有数据，如果有则取缓存数据
        long startTime = System.currentTimeMillis();
        String username = (String) request.getAttribute("username");
        // 前置校验
        // checkUserSite(username, dto.getOrganizationName(), dto.getOrganizationId());
        // 先看redis有没有数据，如果有则取缓存数据
        Object cacheObj = redisUtil.get(tzhbsRedisKey + CacheConstants.ELECTRICITY_USE_RANKING + dto.getProtocol() + "#" + dto.getOrganizationId() + "#" + dto.getStartMonth() + "#" + dto.getEndMonth());
        List<TzhBsElectricityUseVO> lstVo = StringUtil.cast(cacheObj);
        if (CollectionUtils.isEmpty(lstVo)) {
            // 查询电力使用排行榜
            lstVo = tzhBsAmbientPerformanceCounselorMapper.getElectricityUseRanking(dto);
            if (CollectionUtils.isNotEmpty(lstVo)) {
                redisUtil.set(tzhbsRedisKey + CacheConstants.ELECTRICITY_USE_RANKING + dto.getProtocol() + "#" + dto.getOrganizationId() + "#" + dto.getStartMonth() + "#" + dto.getEndMonth(), lstVo);
            }
        }
        long endTime = System.currentTimeMillis();
        log.info("【查询电力使用排行榜】:{}秒", ((endTime - (double) startTime) / 1000));
        return ResultBody.success(lstVo);
    }

    /**
     * 查询水资源使用排行榜
     * <AUTHOR>
     * @date 2025/1/16 15:22
     * @param dto
     * @return com.csci.cohl.exception.ResultBody<java.util.List<com.csci.cohl.beans.vo.TzhBsWaterResourcesUseVO>>
     */
    @Override
    public ResultBody<List<TzhBsWaterResourcesUseVO>> getWaterResourcesUseRanking(TzhBsWaterResourcesUseDTO dto) {
        String username = (String) request.getAttribute("username");
        // 前置校验
        // checkUserSite(username, dto.getOrganizationName(), dto.getOrganizationId());
        // 先看redis有没有数据，如果有则取缓存数据
        Object cacheObj = redisUtil.get(tzhbsRedisKey + CacheConstants.WATER_RESOURCES_USE_RANKING + dto.getProtocol() + "#" + dto.getOrganizationId() + "#" + dto.getStartMonth() + "#" + dto.getEndMonth());
        List<TzhBsWaterResourcesUseVO> lstVo = StringUtil.cast(cacheObj);
        if (CollectionUtils.isEmpty(lstVo)) {
            // 查询水资源使用排行榜
            lstVo = tzhBsAmbientPerformanceCounselorMapper.getWaterResourcesUseRanking(dto);
            if (CollectionUtils.isNotEmpty(lstVo)) {
                redisUtil.get(tzhbsRedisKey + CacheConstants.WATER_RESOURCES_USE_RANKING + dto.getProtocol() + "#" + dto.getOrganizationId() + "#" + dto.getStartMonth() + "#" + dto.getEndMonth());
            }
        }
        return ResultBody.success(lstVo);
    }

    /**
     * 查询温室气体排放总量排行榜
     * <AUTHOR>
     * @date 2025/1/16 16:25
     * @param dto
     * @return com.csci.cohl.exception.ResultBody<java.util.List<com.csci.cohl.beans.vo.TzhBsWaterResourcesUseVO>>
     */
    @Override
    public ResultBody<List<TzhBsGreenhouseGasEmissionsTotalVO>> getGreenhouseGasEmissionsTotalRanking(TzhBsGreenhouseGasEmissionsTotalDTO dto) {
        String username = (String) request.getAttribute("username");
        // 前置校验
        // checkUserSite(username, dto.getOrganizationName(), dto.getOrganizationId());
        // 先看redis有没有数据，如果有则取缓存数据
        Object cacheObj = redisUtil.get(tzhbsRedisKey + CacheConstants.GREENHOUSE_GAS_EMISSIONS_TOTAL_RANKING + dto.getProtocol() + "#" + dto.getOrganizationId() + "#" + dto.getStartMonth() + "#" + dto.getEndMonth());
        List<TzhBsGreenhouseGasEmissionsTotalVO> lstVo = StringUtil.cast(cacheObj);
        if (CollectionUtils.isEmpty(lstVo)) {
            // 查询温室气体排放总量排行榜
            lstVo = tzhBsAmbientPerformanceCounselorMapper.getGreenhouseGasEmissionsTotalRanking(dto);
            if (CollectionUtils.isNotEmpty(lstVo)) {
                // 查询温室气体排放总量（按平台）
                List<TzhBsGreenhouseGasEmissionsDetailVO> lstDetailVo = tzhBsAmbientPerformanceCounselorMapper.getGreenhouseGasEmissionsTotalPlatform(dto);
                // 中海建筑及中建海龙
                Map<String, List<TzhBsGreenhouseGasEmissionsDetailVO>> collect = lstDetailVo.stream().collect(Collectors.groupingBy(obj -> obj.getOrganizationNo()));
                for (TzhBsGreenhouseGasEmissionsTotalVO emissionsVO : lstVo) {
                    if (collect.containsKey(emissionsVO.getOrganizationNo())) {
                        emissionsVO.setDetailVOList(collect.get(emissionsVO.getOrganizationNo()));
                        if ("001007".equals(emissionsVO.getOrganizationNo())) {
                            emissionsVO.setOrganizationName("中海建筑及中建海龙");
                            emissionsVO.getDetailVOList().stream().forEach(e -> {
                                e.setOrganizationName("中海建筑及中建海龙");
                            });

                        }
                    }
                }
                redisUtil.set(tzhbsRedisKey + CacheConstants.GREENHOUSE_GAS_EMISSIONS_TOTAL_RANKING + dto.getProtocol() + "#" + dto.getOrganizationId() + "#" + dto.getStartMonth() + "#" + dto.getEndMonth(), lstVo);
            }
        }

        return ResultBody.success(lstVo);
    }

    /**
     * 查询温室气体排放量总计
     * <AUTHOR>
     * @date 2025/1/18 13:07
     * @param dto
     * @return com.csci.cohl.exception.ResultBody<java.util.List<com.csci.cohl.beans.vo.TzhBsGreenhouseGasEmissionsVO>>
     */
    @Override
    public ResultBody<List<TzhBsGreenhouseGasEmissionsVO>> getGreenhouseGasEmissionsTotal(TzhBsGreenhouseGasEmissionsTotalDTO dto) {
        String username = (String) request.getAttribute("username");
        // 前置校验
        // checkUserSite(username, dto.getOrganizationName(), dto.getOrganizationId());
        // 先看redis有没有数据，如果有则取缓存数据
        Object cacheObj = redisUtil.get(tzhbsRedisKey + CacheConstants.GREENHOUSE_GAS_EMISSIONS_TOTAL + dto.getProtocol() + "#" + dto.getOrganizationId() + "#" + dto.getStartMonth() + "#" + dto.getEndMonth());
        List<TzhBsGreenhouseGasEmissionsVO> lstVo = StringUtil.cast(cacheObj);
        if (CollectionUtils.isEmpty(lstVo)) {
            // 查询温室气体排放量总计
            lstVo = tzhBsAmbientPerformanceCounselorMapper.getGreenhouseGasEmissionsTotal(dto);
            // 查询温室气体排放量明细
            if (CollectionUtils.isNotEmpty(lstVo)) {
                List<TzhBsGreenhouseGasEmissionsVO> lstDetail =  tzhBsAmbientPerformanceCounselorMapper.getGreenhouseGasEmissionsDetail(dto);
                Map<String, List<TzhBsGreenhouseGasEmissionsVO>> collect = lstDetail.stream().collect(Collectors.groupingBy(obj -> obj.getSubCategoryName().split(":")[0]));
                for (TzhBsGreenhouseGasEmissionsVO emissionsVO : lstVo) {
                    if (collect.containsKey(emissionsVO.getSubCategoryName())) {
                        emissionsVO.setDetailList(collect.get(emissionsVO.getSubCategoryName()));
                    }
                }
                redisUtil.set(tzhbsRedisKey + CacheConstants.GREENHOUSE_GAS_EMISSIONS_TOTAL + dto.getProtocol() + "#" + dto.getOrganizationId() + "#" + dto.getStartMonth() + "#" + dto.getEndMonth(), lstVo);
            }
        }

        return ResultBody.success(lstVo);
    }

    /**
     * 查询碳排放强度
     * <AUTHOR>
     * @date 2025/1/20 10:06
     * @param dto
     * @return com.csci.cohl.exception.ResultBody<java.util.List<com.csci.cohl.beans.vo.TzhBsCarbonEmissionYearVO>>
     */
    @Override
    public ResultBody<List<TzhBsCarbonEmissionYearVO>> getCarbonEmissionIntensity(TzhBsCarbonEmissionYearDTO dto) {
        String username = (String) request.getAttribute("username");
        // 前置校验
        // checkUserSite(username, dto.getOrganizationName(), dto.getOrganizationId());
        // 先看redis有没有数据，如果有则取缓存数据
        Object cacheObj = redisUtil.get(tzhbsRedisKey + CacheConstants.CARBON_EMISSION_INTENSITY + dto.getProtocol() + "#" + dto.getOrganizationId() + "#" + dto.getQueryYear());
        List<TzhBsCarbonEmissionYearVO> lstVo = StringUtil.cast(cacheObj);
        if (CollectionUtils.isEmpty(lstVo)) {
            // 查询碳排放强度
            lstVo = tzhBsAmbientPerformanceCounselorMapper.getCarbonEmissionIntensity(dto);
            if (CollectionUtils.isNotEmpty(lstVo)) {
                redisUtil.set(tzhbsRedisKey + CacheConstants.CARBON_EMISSION_INTENSITY + dto.getProtocol() + "#" + dto.getOrganizationId() + "#" + dto.getQueryYear(), lstVo);
            }
        }
        return ResultBody.success(lstVo);
    }

    @Override
    public String clearCache() {
        // 清理能源使用排行榜
        clearCacheEnergyUseRanking();
        // 清理废弃物产生量分布统计
        clearCacheWasteGenerationDistributionStatistics();
        // 清理查询碳排放（按年统计，1+5年）
        clearCacheCarbonEmissionYearSummary();
        // 清理查询原材料使用占比统计
        clearCacheRawMaterialUseProportion();
        // 清理查询资源使用趋势统计
        clearCacheResourceUseTrendStatistics();
        // 清理查询各类别密度
        clearCacheCategoryDensity();
        // 清理查询地图各省份数据
        clearCacheMapProvinceData();
        // 清理查询用水与污染
        clearCacheWaterUseAndPollution();
        // 清理查询项目分布
        clearCacheProjectDistribution();
        // 清理查询电力使用排行榜
        clearCacheElectricityUseRanking();
        // 清理查询水资源使用排行榜
        clearCacheWaterResourcesUseRanking();
        // 清理查询温室气体排放总量排行榜
        clearCacheGreenhouseGasEmissionsTotalRanking();
        // 清理查询温室气体排放量总计
        clearCacheGreenhouseGasEmissionsTotal();
        // 清理查询碳排放强度
        clearCacheCarbonEmissionIntensity();

        return "";
    }

    /**
     * 检查当前用户是否有查询该项目的权限
     * <AUTHOR>
     * @date 2025/1/6 15:36
     * @param username          用户名
     * @param organizationName  项目名
     * @param organizationId  项目id
     */
    void checkUserSite(String username, String organizationName, String organizationId) {
        if (StringUtil.isNotEmpty(organizationId)) {
            if(!biTzhBsUserSiteMapper.listSiteId(username).contains(organizationId)) {
                throw new ServiceException("無法查詢該項目");
            }
        }else {
            if(!biTzhBsUserSiteMapper.listSite(username).contains(organizationName)) {
                throw new ServiceException("無法查詢該項目");
            }
        }
    }

    public static void convertAndPrint(TzhBsRawMaterialUseVO tzhBsRawMaterialUseVO) {
        // 定义单位常量
        BigDecimal THOUSAND = new BigDecimal("1000");
        BigDecimal MILLION = new BigDecimal("1000000");
        BigDecimal TEN_BILLION = new BigDecimal("1000000000");
        BigDecimal rawMaterialUseTotalAmount = tzhBsRawMaterialUseVO.getRawMaterialUseTotalAmount();
        // 判断并转换
        if (rawMaterialUseTotalAmount.compareTo(TEN_BILLION) >= 0) {
            tzhBsRawMaterialUseVO.setConvertTotalAmount(rawMaterialUseTotalAmount.divide(TEN_BILLION));
            tzhBsRawMaterialUseVO.setConvertUnit("十億噸");
            tzhBsRawMaterialUseVO.setConvertUnitSc("十亿吨");
            tzhBsRawMaterialUseVO.setConvertUnitEn("Gt");
        } else if (rawMaterialUseTotalAmount.compareTo(MILLION) >= 0) {
            tzhBsRawMaterialUseVO.setConvertTotalAmount(rawMaterialUseTotalAmount.divide(MILLION));
            tzhBsRawMaterialUseVO.setConvertUnit("百萬噸");
            tzhBsRawMaterialUseVO.setConvertUnitSc("百万吨");
            tzhBsRawMaterialUseVO.setConvertUnitEn("Mt");
        } else if (rawMaterialUseTotalAmount.compareTo(THOUSAND) >= 0) {
            tzhBsRawMaterialUseVO.setConvertTotalAmount(rawMaterialUseTotalAmount.divide(THOUSAND));
            tzhBsRawMaterialUseVO.setConvertUnit("千噸");
            tzhBsRawMaterialUseVO.setConvertUnitSc("千吨");
            tzhBsRawMaterialUseVO.setConvertUnitEn("kt");
        } else {
            tzhBsRawMaterialUseVO.setConvertTotalAmount(rawMaterialUseTotalAmount);
            tzhBsRawMaterialUseVO.setConvertUnit("噸");
            tzhBsRawMaterialUseVO.setConvertUnitSc("吨");
            tzhBsRawMaterialUseVO.setConvertUnitEn("t");
        }
    }

    private void clearCacheCarbonEmissionIntensity() {
        List<String> key13 = redisUtil.getKey(tzhbsRedisKey + CacheConstants.CARBON_EMISSION_INTENSITY + "*");
        if (CollectionUtils.isNotEmpty(key13)) {
            TzhBsCarbonEmissionYearDTO dto = new TzhBsCarbonEmissionYearDTO();
            for (String s : key13) {
                redisUtil.del(s);
                if (s.contains("#")) {
                    String cacheKey = s.substring(s.lastIndexOf(":") + 1);
                    String[] split = cacheKey.split("#");
                    dto.setProtocol(split[0]);
                    dto.setOrganizationId(split[1]);
                    dto.setQueryYear(split[2]);
                    getCarbonEmissionIntensity(dto);
                }
            }
        }
    }

    private void clearCacheGreenhouseGasEmissionsTotal() {
        List<String> key12 = redisUtil.getKey(tzhbsRedisKey + CacheConstants.GREENHOUSE_GAS_EMISSIONS_TOTAL + "*");
        if (CollectionUtils.isNotEmpty(key12)) {
            TzhBsGreenhouseGasEmissionsTotalDTO dto = new TzhBsGreenhouseGasEmissionsTotalDTO();
            for (String s : key12) {
                redisUtil.del(s);
                if (s.contains("#")) {
                    String cacheKey = s.substring(s.lastIndexOf(":") + 1);
                    String[] split = cacheKey.split("#");
                    dto.setProtocol(split[0]);
                    dto.setOrganizationId(split[1]);
                    dto.setStartMonth(split[2]);
                    dto.setEndMonth(split[3]);
                    getGreenhouseGasEmissionsTotal(dto);
                }
            }
        }
    }

    private void clearCacheGreenhouseGasEmissionsTotalRanking() {
        List<String> key11 = redisUtil.getKey(tzhbsRedisKey + CacheConstants.GREENHOUSE_GAS_EMISSIONS_TOTAL_RANKING + "*");
        if (CollectionUtils.isNotEmpty(key11)) {
            TzhBsGreenhouseGasEmissionsTotalDTO dto = new TzhBsGreenhouseGasEmissionsTotalDTO();
            for (String s : key11) {
                redisUtil.del(s);
                if (s.contains("#")) {
                    String cacheKey = s.substring(s.lastIndexOf(":") + 1);
                    String[] split = cacheKey.split("#");
                    dto.setProtocol(split[0]);
                    dto.setOrganizationId(split[1]);
                    dto.setStartMonth(split[2]);
                    dto.setEndMonth(split[3]);
                    getGreenhouseGasEmissionsTotalRanking(dto);
                }
            }
        }
    }

    private void clearCacheWaterResourcesUseRanking() {
        List<String> key10 = redisUtil.getKey(tzhbsRedisKey + CacheConstants.WATER_RESOURCES_USE_RANKING + "*");
        if (CollectionUtils.isNotEmpty(key10)) {
            TzhBsWaterResourcesUseDTO dto = new TzhBsWaterResourcesUseDTO();
            for (String s : key10) {
                redisUtil.del(s);
                if (s.contains("#")) {
                    String cacheKey = s.substring(s.lastIndexOf(":") + 1);
                    String[] split = cacheKey.split("#");
                    dto.setProtocol(split[0]);
                    dto.setOrganizationId(split[1]);
                    dto.setStartMonth(split[2]);
                    dto.setEndMonth(split[3]);
                    getWaterResourcesUseRanking(dto);
                }
            }
        }
    }

    private void clearCacheElectricityUseRanking() {
        List<String> key9 = redisUtil.getKey(tzhbsRedisKey + CacheConstants.ELECTRICITY_USE_RANKING + "*");
        if (CollectionUtils.isNotEmpty(key9)) {
            TzhBsElectricityUseDTO dto = new TzhBsElectricityUseDTO();
            for (String s : key9) {
                redisUtil.del(s);
                if (s.contains("#")) {
                    String cacheKey = s.substring(s.lastIndexOf(":") + 1);
                    String[] split = cacheKey.split("#");
                    dto.setProtocol(split[0]);
                    dto.setOrganizationId(split[1]);
                    dto.setStartMonth(split[2]);
                    dto.setEndMonth(split[3]);
                    getElectricityUseRanking(dto);
                }
            }
        }
    }

    private void clearCacheProjectDistribution() {
        List<String> key8 = redisUtil.getKey(tzhbsRedisKey + CacheConstants.PROJECT_DISTRIBUTION + "*");
        if (CollectionUtils.isNotEmpty(key8)) {
            TzhBsProjectDistributionDTO dto = new TzhBsProjectDistributionDTO();
            for (String s : key8) {
                redisUtil.del(s);
                if (s.contains("#")) {
                    String cacheKey = s.substring(s.lastIndexOf(":") + 1);
                    String[] split = cacheKey.split("#");
                    dto.setProtocol(split[0]);
                    dto.setOrganizationId(split[1]);
                    dto.setStartMonth(split[2]);
                    dto.setEndMonth(split[3]);
                    getProjectDistribution(dto);
                }
            }
        }
    }

    private void clearCacheWaterUseAndPollution() {
        List<String> key7 = redisUtil.getKey(tzhbsRedisKey + CacheConstants.WATER_USE_AND_POLLUTION + "*");
        if (CollectionUtils.isNotEmpty(key7)) {
            TzhBsWaterUseAndPollutionDTO dto = new TzhBsWaterUseAndPollutionDTO();
            for (String s : key7) {
                redisUtil.del(s);
                if (s.contains("#")) {
                    String cacheKey = s.substring(s.lastIndexOf(":") + 1);
                    String[] split = cacheKey.split("#");
                    dto.setProtocol(split[0]);
                    dto.setOrganizationId(split[1]);
                    dto.setStartMonth(split[2]);
                    dto.setEndMonth(split[3]);
                    getWaterUseAndPollution(dto);
                }
            }
        }
    }

    private void clearCacheMapProvinceData() {
        List<String> key6 = redisUtil.getKey(tzhbsRedisKey + CacheConstants.MAP_PROVINCE_DATA + "*");
        if (CollectionUtils.isNotEmpty(key6)) {
            TzhBsMapProvinceDataDTO dto = new TzhBsMapProvinceDataDTO();
            for (String s : key6) {
                redisUtil.del(s);
                if (s.contains("#")) {
                    String cacheKey = s.substring(s.lastIndexOf(":") + 1);
                    String[] split = cacheKey.split("#");
                    dto.setProtocol(split[0]);
                    dto.setOrganizationId(split[1]);
                    dto.setOrganizationName(split[2]);
                    dto.setStartMonth(split[3]);
                    dto.setEndMonth(split[4]);
                    getMapProvinceData(dto);
                }
            }
        }
    }

    private void clearCacheCategoryDensity() {
        List<String> key5 = redisUtil.getKey(tzhbsRedisKey + CacheConstants.CATEGORY_DENSITY + "*");
        if (CollectionUtils.isNotEmpty(key5)) {
            TzhBsCategoryDensityDTO dto = new TzhBsCategoryDensityDTO();
            for (String s : key5) {
                redisUtil.del(s);
                if (s.contains("#")) {
                    String cacheKey = s.substring(s.lastIndexOf(":") + 1);
                    String[] split = cacheKey.split("#");
                    dto.setProtocol(split[0]);
                    dto.setOrganizationId(split[1]);
                    dto.setStartMonth(split[2]);
                    dto.setEndMonth(split[3]);
                    getCategoryDensity(dto);
                }
            }
        }
    }

    private void clearCacheResourceUseTrendStatistics() {
        List<String> key4 = redisUtil.getKey(tzhbsRedisKey + CacheConstants.RESOURCE_USE_TREND_STATISTICS + "*");
        if (CollectionUtils.isNotEmpty(key4)) {
            TzhBsResourceUseTrendStatisticsDTO dto = new TzhBsResourceUseTrendStatisticsDTO();
            for (String s : key4) {
                redisUtil.del(s);
                if (s.contains("#")) {
                    String cacheKey = s.substring(s.lastIndexOf(":") + 1);
                    String[] split = cacheKey.split("#");
                    dto.setProtocol(split[0]);
                    dto.setOrganizationId(split[1]);
                    dto.setQueryYear(split[2]);
                    getResourceUseTrendStatistics(dto);
                }
            }
        }
    }

    private void clearCacheRawMaterialUseProportion() {
        List<String> key3 = redisUtil.getKey(tzhbsRedisKey + CacheConstants.RAW_MATERIAL_USE_PROPORTION + "*");
        if (CollectionUtils.isNotEmpty(key3)) {
            TzhBsRawMaterialUseDTO dto = new TzhBsRawMaterialUseDTO();
            for (String s : key3) {
                redisUtil.del(s);
                if (s.contains("#")) {
                    String cacheKey = s.substring(s.lastIndexOf(":") + 1);
                    String[] split = cacheKey.split("#");
                    dto.setProtocol(split[0]);
                    dto.setOrganizationId(split[1]);
                    dto.setStartMonth(split[2]);
                    dto.setEndMonth(split[3]);
                    getRawMaterialUseProportion(dto);
                }
            }
        }
    }

    private void clearCacheCarbonEmissionYearSummary() {
        List<String> key2 = redisUtil.getKey(tzhbsRedisKey + CacheConstants.CARBON_EMISSION_YEAR_SUMMARY + "*");
        if (CollectionUtils.isNotEmpty(key2)) {
            TzhBsCarbonEmissionYearDTO dto = new TzhBsCarbonEmissionYearDTO();
            for (String s : key2) {
                redisUtil.del(s);
                if (s.contains("#")) {
                    String cacheKey = s.substring(s.lastIndexOf(":") + 1);
                    String[] split = cacheKey.split("#");
                    dto.setProtocol(split[0]);
                    dto.setOrganizationId(split[1]);
                    dto.setQueryYear(split[2]);
                    getCarbonEmissionYearSummary(dto);
                }
            }
        }
    }

    private void clearCacheWasteGenerationDistributionStatistics() {
        List<String> key1 = redisUtil.getKey(tzhbsRedisKey + CacheConstants.Waste_Generation_Distribution_Statistics + "*");
        if (CollectionUtils.isNotEmpty(key1)) {
            TzhBsWasteGenerationDTO dto = new TzhBsWasteGenerationDTO();
            for (String s : key1) {
                redisUtil.del(s);
                if (s.contains("#")) {
                    String cacheKey = s.substring(s.lastIndexOf(":") + 1);
                    String[] split = cacheKey.split("#");
                    dto.setProtocol(split[0]);
                    dto.setOrganizationId(split[1]);
                    dto.setStartMonth(split[2]);
                    dto.setEndMonth(split[3]);
                    getWasteGenerationDistributionStatistics(dto);
                }

            }
        }
    }

    private void clearCacheEnergyUseRanking() {
        List<String> key = redisUtil.getKey(tzhbsRedisKey + CacheConstants.ENERGY_USE_RANKING + "*");
        if (CollectionUtils.isNotEmpty(key)) {
            TzhBsEnergyUseDTO dto = new TzhBsEnergyUseDTO();
            for (String s : key) {
                redisUtil.del(s);
                if (s.contains("#")) {
                    String cacheKey = s.substring(s.lastIndexOf(":") + 1);
                    String[] split = cacheKey.split("#");
                    dto.setProtocol(split[0]);
                    dto.setOrganizationId(split[1]);
                    dto.setStartMonth(split[2]);
                    dto.setEndMonth(split[3]);
                    getEnergyUseRanking(dto);
                }
            }
        }
    }


}
