package com.csci.susdev.exception;

/**
 * Base exception for OCR-related operations
 * 
 * <AUTHOR>
 * @date 2025-06-20
 */
public class OcrException extends RuntimeException {
    
    private static final long serialVersionUID = 1L;
    
    private String errorCode;
    private String errorMessage;
    
    public OcrException(String message) {
        super(message);
        this.errorMessage = message;
        this.errorCode = "OCR_ERROR";
    }
    
    public OcrException(String errorCode, String message) {
        super(message);
        this.errorCode = errorCode;
        this.errorMessage = message;
    }
    
    public OcrException(String message, Throwable cause) {
        super(message, cause);
        this.errorMessage = message;
        this.errorCode = "OCR_ERROR";
    }
    
    public OcrException(String errorCode, String message, Throwable cause) {
        super(message, cause);
        this.errorCode = errorCode;
        this.errorMessage = message;
    }
    
    public String getErrorCode() {
        return errorCode;
    }
    
    public String getErrorMessage() {
        return errorMessage;
    }
}
