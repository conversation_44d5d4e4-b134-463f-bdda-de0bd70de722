package com.csci.susdev.exception;

/**
 * Error codes for OCR operations
 * 
 * <AUTHOR>
 * @date 2025-06-20
 */
public final class OcrErrorCodes {
    
    // File Processing Errors (1000-1999)
    public static final String FILE_PROCESSING_ERROR = "OCR_1000";
    public static final String UNSUPPORTED_FILE_TYPE = "OCR_1001";
    public static final String FILE_SIZE_EXCEEDED = "OCR_1002";
    public static final String INVALID_FILE = "OCR_1003";
    public static final String FILE_CONVERSION_ERROR = "OCR_1004";
    
    // OCR Service Errors (2000-2999)
    public static final String OCR_SERVICE_ERROR = "OCR_2000";
    public static final String OCR_SERVICE_TIMEOUT = "OCR_2001";
    public static final String OCR_SERVICE_UNAVAILABLE = "OCR_2002";
    public static final String OCR_RESPONSE_INVALID = "OCR_2003";
    
    // Document Analysis Errors (3000-3999)
    public static final String DOCUMENT_PARSING_ERROR = "OCR_3000";
    public static final String WATER_BILL_PARSING_ERROR = "OCR_3001";
    public static final String ELECTRICITY_BILL_PARSING_ERROR = "OCR_3002";
    public static final String TRAIN_TICKET_PARSING_ERROR = "OCR_3003";
    public static final String AIR_TICKET_PARSING_ERROR = "OCR_3004";
    
    // AI Analysis Errors (4000-4999)
    public static final String AI_ANALYSIS_ERROR = "OCR_4000";
    public static final String AI_SERVICE_ERROR = "OCR_4001";
    public static final String AI_RESPONSE_PARSING_ERROR = "OCR_4002";
    public static final String AI_CONVERSATION_ERROR = "OCR_4003";
    
    // Data Extraction Errors (5000-5999)
    public static final String DATA_EXTRACTION_ERROR = "OCR_5000";
    public static final String MISSING_REQUIRED_FIELD = "OCR_5001";
    public static final String INVALID_DATE_FORMAT = "OCR_5002";
    public static final String INVALID_NUMBER_FORMAT = "OCR_5003";
    
    // Validation Errors (6000-6999)
    public static final String VALIDATION_ERROR = "OCR_6000";
    public static final String EMPTY_FILE_ERROR = "OCR_6001";
    public static final String EMPTY_OCR_RESULT = "OCR_6002";
    
    private OcrErrorCodes() {
        // Utility class - prevent instantiation
    }
}
