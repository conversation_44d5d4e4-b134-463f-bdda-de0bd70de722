package com.csci.susdev.exception;

/**
 * Exception thrown when OCR analysis fails
 * 
 * <AUTHOR>
 * @date 2025-06-20
 */
public class OcrAnalysisException extends OcrException {
    
    private static final long serialVersionUID = 1L;
    
    public OcrAnalysisException(String message) {
        super("OCR_ANALYSIS_ERROR", message);
    }
    
    public OcrAnalysisException(String message, Throwable cause) {
        super("OCR_ANALYSIS_ERROR", message, cause);
    }
    
    /**
     * Exception for OCR service communication failures
     */
    public static class OcrServiceException extends OcrAnalysisException {
        public OcrServiceException(String message) {
            super("OCR服务调用失败: " + message);
        }
        
        public OcrServiceException(String message, Throwable cause) {
            super("OCR服务调用失败: " + message, cause);
        }
    }
    
    /**
     * Exception for document parsing failures
     */
    public static class DocumentParsingException extends OcrAnalysisException {
        public DocumentParsingException(String documentType) {
            super("无法解析" + documentType + "文档。文档格式可能不受支持或内容不清晰");
        }
        
        public DocumentParsingException(String documentType, Throwable cause) {
            super("无法解析" + documentType + "文档。文档格式可能不受支持或内容不清晰", cause);
        }
    }
    
    /**
     * Exception for AI analysis failures
     */
    public static class AiAnalysisException extends OcrAnalysisException {
        public AiAnalysisException(String message) {
            super("AI分析失败: " + message);
        }
        
        public AiAnalysisException(String message, Throwable cause) {
            super("AI分析失败: " + message, cause);
        }
    }
    
    /**
     * Exception for data extraction failures
     */
    public static class DataExtractionException extends OcrAnalysisException {
        public DataExtractionException(String fieldName) {
            super("无法提取必要字段: " + fieldName);
        }
        
        public DataExtractionException(String fieldName, Throwable cause) {
            super("无法提取必要字段: " + fieldName, cause);
        }
    }
}
