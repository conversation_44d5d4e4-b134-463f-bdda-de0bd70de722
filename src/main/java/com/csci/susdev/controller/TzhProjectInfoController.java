package com.csci.susdev.controller;

import com.csci.susdev.annotation.LogMethod;
import com.csci.susdev.constant.SusDevConsts;
import com.csci.susdev.model.ResultBean;
import com.csci.susdev.model.ResultPage;
import com.csci.susdev.service.TzhProjectInfoService;
import com.csci.susdev.service.UserService;
import com.csci.susdev.util.context.ContextUtils;
import com.csci.tzh.model.TzhProjectInfo;
import com.csci.tzh.qo.TzhProjectInfoQO;
import com.csci.tzh.vo.TzhProjectInfoVO;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.enums.ParameterIn;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;

@RestController
@RequestMapping(value = "/api/tzh/project-info", produces = "application/json")
@Tag(name = "項目基礎資訊 接口展示", description = "用于接口调试")
@LogMethod
public class TzhProjectInfoController {

    @Autowired
    private TzhProjectInfoService tzhProjectInfoService;

    @Resource
    private UserService userService;

    private class ResultPageOfTzhProjectInfo extends ResultPage<TzhProjectInfoVO>{
		public ResultPageOfTzhProjectInfo(List<?> page) {
			super(page);
		}
	}
    
    @GetMapping("/get")
    @Operation(description = "查詢 項目基礎資訊 接口數據列表")
    @Parameter(in = ParameterIn.HEADER, name = SusDevConsts.HEADER_TOKEN_KEY, schema = @Schema(type = "string"), required = true)
    @ApiResponse(responseCode = "200", content = @Content(schema = @Schema(implementation = ResultPageOfTzhProjectInfo.class)))
    public ResultBean<TzhProjectInfoVO> list(TzhProjectInfoQO qo) {
        return new ResultBean<>(tzhProjectInfoService.get(qo));
    }
    
    @PostMapping("/save")
    @Operation(description = "保存 項目基礎資訊 所有記錄, 如沒有記錄則保存")
    @Parameter(in = ParameterIn.HEADER, name = SusDevConsts.HEADER_TOKEN_KEY, schema = @Schema(type = "string"), required = true)
    public ResultBean<TzhProjectInfo> save(@RequestBody TzhProjectInfo model) {
        userService.checkIsReadOnly();

        return new ResultBean<>(tzhProjectInfoService.save(model));
    }
}
