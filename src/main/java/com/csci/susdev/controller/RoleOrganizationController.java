package com.csci.susdev.controller;

import com.csci.susdev.annotation.LogMethod;
import com.csci.susdev.constant.SusDevConsts;
import com.csci.susdev.model.ResultBean;
import com.csci.susdev.model.RoleOrganization;
import com.csci.susdev.model.UserOrganization;
import com.csci.susdev.service.RoleOrganizationService;
import com.csci.susdev.service.UserOrganizationService;
import com.csci.susdev.service.UserService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.enums.ParameterIn;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;

@RestController
@RequestMapping(value = "/api/roleOrganization", produces = "application/json")
@Tag(name = "角色组织关联 接口展示", description = "用于接口调试")
@LogMethod
public class RoleOrganizationController {

    @Autowired
    private RoleOrganizationService roleOrganizationService;

    @Resource
    private UserService userService;
    
    /**
     * 查找 角色组织关联 数据列表
     *
     * @param roleId
     * @return
     */
    @GetMapping("/list")
    @Operation(description = "分页查询 角色组织关联 统计数据列表")
    @Parameter(in = ParameterIn.HEADER, name = SusDevConsts.HEADER_TOKEN_KEY, schema = @Schema(type = "string"), required = true)
    @ApiResponse(responseCode = "200")
    public List<RoleOrganization> list(String roleId) {
        return roleOrganizationService.listRoleOrganization(roleId);
    }
    
    @PostMapping("/save")
    @Operation(description = "保存 角色组织关联 記錄")
    @Parameter(in = ParameterIn.HEADER, name = SusDevConsts.HEADER_TOKEN_KEY, schema = @Schema(type = "string"), required = true)
    public ResultBean<String> save(@RequestBody RoleOrganization roleOrganization) {
        userService.checkIsReadOnly();

        return new ResultBean<>(roleOrganizationService.saveRoleOrganization(roleOrganization));
    }
    
    @PostMapping("/savelist")
    @Operation(description = "保存 角色组织关联 記錄列表")
    @Parameter(in = ParameterIn.HEADER, name = SusDevConsts.HEADER_TOKEN_KEY, schema = @Schema(type = "string"), required = true)
    public ResultBean<List<String>> savelist(@RequestBody List<RoleOrganization> roleOrganizationLst) {
        userService.checkIsReadOnly();

        return new ResultBean<>(roleOrganizationService.saveRoleOrganizationList(roleOrganizationLst));
    }

    @DeleteMapping("/delete/{id}")
    @Operation(description = "刪除 角色组织关联 記錄")
    @Parameter(in = ParameterIn.HEADER, name = SusDevConsts.HEADER_TOKEN_KEY, schema = @Schema(type = "string"), required = true)
    public ResultBean<Integer> delete(@PathVariable String id) {
        userService.checkIsReadOnly();

        return new ResultBean<>(Integer.valueOf(roleOrganizationService.deleteRoleOrganization(id)));
    }

}
