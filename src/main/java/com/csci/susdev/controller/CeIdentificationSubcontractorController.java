package com.csci.susdev.controller;

import com.csci.susdev.annotation.LogMethod;
import com.csci.susdev.constant.SusDevConsts;
import com.csci.susdev.model.ResultBase;
import com.csci.susdev.model.ResultBean;
import com.csci.susdev.model.ResultPage;
import com.csci.susdev.modelcovt.CeIdentificationSubcontractorConverter;
import com.csci.susdev.qo.CeIdentificationSubcontractorQO;
import com.csci.susdev.service.CeIdentificationSubcontractorService;
import com.csci.susdev.service.UserService;
import com.csci.susdev.util.context.ContextUtils;
import com.csci.susdev.vo.IdVO;
import com.csci.susdev.vo.CeIdentificationSubcontractorVO;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.enums.ParameterIn;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;

@RestController
@RequestMapping("/api/CeIdentificationSubcontractor")
@Tag(name = "碳排識別分判商", description = "碳排識別分判商")
@LogMethod
public class CeIdentificationSubcontractorController {

    @Resource
    private CeIdentificationSubcontractorService ceIdentificationSubcontractorService;

    @Resource
    private UserService userService;

    @GetMapping("/list")
    @Operation(description = "分页查询数据")
    @Parameter(in = ParameterIn.HEADER, name = SusDevConsts.HEADER_TOKEN_KEY, schema = @Schema(type = "string"), required = true)
    public ResultPage<CeIdentificationSubcontractorVO> listCeIdentificationSubcontractor(CeIdentificationSubcontractorQO ceIdentificationSubcontractorQO) {
        return ceIdentificationSubcontractorService.listCeIdentificationSubcontractor(ceIdentificationSubcontractorQO);
    }

    @PostMapping("/save")
    @Operation(description = "保存记录")
    @Parameter(in = ParameterIn.HEADER, name = SusDevConsts.HEADER_TOKEN_KEY, schema = @Schema(type = "string"), required = true)
    public ResultBean<String> save(@RequestBody CeIdentificationSubcontractorVO ceIdentificationSubcontractorVO) {
        userService.checkIsReadOnly();

        return new ResultBean<>(ceIdentificationSubcontractorService.saveCeIdentificationSubcontractor(ceIdentificationSubcontractorVO));
    }

    @PostMapping("/duplicate")
    @Operation(description = "复制记录")
    @Parameter(in = ParameterIn.HEADER, name = SusDevConsts.HEADER_TOKEN_KEY, schema = @Schema(type = "string"), required = true)
    public ResultBean<CeIdentificationSubcontractorVO> duplicate(@RequestBody IdVO idVO) {
        userService.checkIsReadOnly();

        return new ResultBean<>(CeIdentificationSubcontractorConverter.convert(ceIdentificationSubcontractorService.duplicateCeIdentificationSubcontractor(idVO.getId())));
    }

    @GetMapping("/{id}")
    @Operation(description = "根据id查询记录")
    @Parameter(in = ParameterIn.HEADER, name = SusDevConsts.HEADER_TOKEN_KEY, schema = @Schema(type = "string"), required = true)
    public ResultBean<CeIdentificationSubcontractorVO> getCeIdentificationSubcontractorById(@PathVariable String id) {
        return new ResultBean<>(ceIdentificationSubcontractorService.getCeIdentificationSubcontractorById(id));
    }

    @DeleteMapping("/{id}")
    @Operation(description = "根据id删除记录")
    @Parameter(in = ParameterIn.HEADER, name = SusDevConsts.HEADER_TOKEN_KEY, schema = @Schema(type = "string"), required = true)
    public ResultBase deleteCeIdentificationSubcontractorById(@PathVariable String id) {
        userService.checkIsReadOnly();

        ceIdentificationSubcontractorService.deleteCeIdentificationSubcontractorById(id);
        return new ResultBase();
    }
}
