package com.csci.susdev.controller;

import com.csci.susdev.annotation.LogMethod;
import com.csci.susdev.constant.SusDevConsts;
import com.csci.susdev.exception.FileProcessingException;
import com.csci.susdev.exception.OcrAnalysisException;
import com.csci.susdev.model.ResultBean;
import com.csci.susdev.service.TiOcrService;
import com.csci.susdev.vo.TiOcrEnergyBillListVO;
import com.csci.susdev.vo.TiOcrBusinessTripListVO;
import com.fasterxml.jackson.core.JsonProcessingException;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.enums.ParameterIn;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestPart;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

@RestController
@RequestMapping(value = "/api/tiOcr", produces = "application/json")
@Tag(name = "TiOCR 接口展示", description = "用于接口调试")
@LogMethod
public class TiOcrController {

    @Autowired
    private TiOcrService tiOcrService;
    
    @PostMapping(value = "/analysisWaterBillInfo", produces = "application/json", consumes = MediaType.MULTIPART_FORM_DATA_VALUE)
    @Operation(description = "解析水费数据")
    @Parameter(in = ParameterIn.HEADER, name = SusDevConsts.HEADER_TOKEN_KEY, schema = @Schema(type = "string"), required = true)
    @ApiResponse(responseCode = "200")
    public ResultBean<TiOcrEnergyBillListVO> analysisWaterBillInfo(@RequestPart("file") MultipartFile file)
            throws FileProcessingException, OcrAnalysisException, JsonProcessingException {
        String json = tiOcrService.convertFileToStr(file);
        return new ResultBean<>(tiOcrService.analysisTiOcrEnergyBillInfoByAi(json, "水費單"));
    }

    @PostMapping(value = "/analysisElectricityBillInfo", produces = "application/json", consumes = MediaType.MULTIPART_FORM_DATA_VALUE)
    @Operation(description = "解析电费数据")
    @Parameter(in = ParameterIn.HEADER, name = SusDevConsts.HEADER_TOKEN_KEY, schema = @Schema(type = "string"), required = true)
    @ApiResponse(responseCode = "200")
    public ResultBean<TiOcrEnergyBillListVO> analysisElectricityBillInfo(@RequestPart("file") MultipartFile file)
            throws FileProcessingException, OcrAnalysisException, JsonProcessingException {
        String json = tiOcrService.convertFileToStr(file);
        return new ResultBean<>(tiOcrService.analysisTiOcrEnergyBillInfoByAi(json, "電費單"));
    }

    @PostMapping(value = "/analysisBusinessTripTrainInfo", produces = "application/json", consumes = MediaType.MULTIPART_FORM_DATA_VALUE)
    @Operation(description = "解析商务旅行-火车数据")
    @Parameter(in = ParameterIn.HEADER, name = SusDevConsts.HEADER_TOKEN_KEY, schema = @Schema(type = "string"), required = true)
    @ApiResponse(responseCode = "200")
    public ResultBean<TiOcrBusinessTripListVO> analysisBusinessTripTrainInfo(@RequestPart("file") MultipartFile file)
            throws FileProcessingException, OcrAnalysisException, JsonProcessingException {
        String json = tiOcrService.convertFileToStr(file);
        return new ResultBean<>(tiOcrService.analysisBusinessTripInfoByAi(json, "車票"));
    }

    @PostMapping(value = "/analysisBusinessTripAirInfo", produces = "application/json", consumes = MediaType.MULTIPART_FORM_DATA_VALUE)
    @Operation(description = "解析商务旅行-飞机数据")
    @Parameter(in = ParameterIn.HEADER, name = SusDevConsts.HEADER_TOKEN_KEY, schema = @Schema(type = "string"), required = true)
    @ApiResponse(responseCode = "200")
    public ResultBean<TiOcrBusinessTripListVO> analysisBusinessTripAirInfo(@RequestPart("file") MultipartFile file)
            throws FileProcessingException, OcrAnalysisException, JsonProcessingException {
        String json = tiOcrService.convertFileToStr(file);
        return new ResultBean<>(tiOcrService.analysisBusinessTripInfoByAi(json, "機票"));
    }
    

}
