package com.csci.susdev.controller;

import com.csci.susdev.annotation.LogMethod;
import com.csci.susdev.constant.SusDevConsts;
import com.csci.susdev.model.ResultBean;
import com.csci.susdev.service.TzhProjectDetailService;
import com.csci.susdev.service.UserService;
import com.csci.susdev.util.context.ContextUtils;
import com.csci.tzh.model.TzhProjectDetail;
import com.csci.tzh.qo.SiteNameQO;
import com.csci.tzh.vo.TzhProjectDetailVO;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.enums.ParameterIn;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;

@RestController
@RequestMapping(value = "/api/tzh/tzh-project-detail", produces = "application/json")
@Tag(name = "項目明細 接口展示", description = "用于接口调试")
@LogMethod
public class TzhProjectDetailController {

    @Autowired
    private TzhProjectDetailService service;

    @Resource
    private UserService userService;

    @GetMapping("/list")
    @Operation(description = "查詢 項目明細 接口數據列表")
    @Parameter(in = ParameterIn.HEADER, name = SusDevConsts.HEADER_TOKEN_KEY, schema = @Schema(type = "string"), required = true)
    @ApiResponse(responseCode = "200")
    public ResultBean<List<TzhProjectDetailVO>> list(SiteNameQO qo) {
        return new ResultBean<>(service.list(qo));
    }
    
    @PostMapping("/save")
    @Operation(description = "保存 項目明細 所有記錄")
    @Parameter(in = ParameterIn.HEADER, name = SusDevConsts.HEADER_TOKEN_KEY, schema = @Schema(type = "string"), required = true)
    public ResultBean<TzhProjectDetail> save(@RequestBody TzhProjectDetail model) {
        userService.checkIsReadOnly();

        return new ResultBean<>(service.save(model));
    }
}
