package com.csci.susdev.controller;

import cn.hutool.core.util.StrUtil;
import com.csci.common.exception.ServiceException;
import com.csci.susdev.annotation.LogMethod;
import com.csci.susdev.constant.SusDevConsts;
import com.csci.susdev.facade.WorkflowFacade;
import com.csci.susdev.model.*;
import com.csci.susdev.qo.CeBasicInfoQO;
import com.csci.susdev.service.*;
import com.csci.susdev.util.context.ContextUtils;
import com.csci.susdev.util.redis.RedisLockUtil;
import com.csci.susdev.vo.*;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.enums.ParameterIn;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.Objects;
import java.util.Optional;

import static com.csci.susdev.service.ServiceHelper.checkExist;

@RestController
@RequestMapping(value = "/api/ce-basic-info", produces = "application/json")
@Tag(name = "碳排基礎訊息 接口展示", description = "用于接口调试")
@LogMethod
public class CeBasicInfoController {

    /**
     * 日志记录对象
     */
    private static final Logger logger = LoggerFactory.getLogger(CeBasicInfoController.class);


    @Resource
    private UserService userService;

    @Resource
    private CeBasicInfoService ceBasicInfoService;

    @Resource
    private WorkflowFacade workflowFacade;

    @Resource
    private FormService formService;

    @Resource
    private WorkflowControlService workflowControlService;

    @Resource
    private WorkflowService workflowService;

    private String formId;

    @PostMapping("/get-or-init")
    @Operation(description = "获取表格")
    @Parameter(in = ParameterIn.HEADER, name = SusDevConsts.HEADER_TOKEN_KEY, schema = @Schema(type = "string"), required = true)
    public ResultBean<CeBasicInfoHeadVO> getOrInit(@RequestBody CeBasicInfoQO ceBasicInfoQO) {
        return new ResultBean<>(ceBasicInfoService.getOrInit(ceBasicInfoQO));
    }

    @GetMapping("/detail/list1")
    @Operation(description = "获取碳排基礎訊息明细列表")
    @Parameter(in = ParameterIn.HEADER, name = SusDevConsts.HEADER_TOKEN_KEY, schema = @Schema(type = "string"), required = true)
    public ResultList<CeBasicInfoDetailVO> listCeBasicInfoDetail(String headId) {
        return new ResultList<>(ceBasicInfoService.listCeBasicInfoDetail(headId));
    }

    @GetMapping("/detail/list2")
    @Operation(description = "获取碳排經營數據明细列表")
    @Parameter(in = ParameterIn.HEADER, name = SusDevConsts.HEADER_TOKEN_KEY, schema = @Schema(type = "string"), required = true)
    public ResultList<CeOperationDataDetailVO> listCeOperationDataDetail(String headId) {
        return new ResultList<>(ceBasicInfoService.listCeOperationDataDetail(headId));
    }

    @GetMapping("/detail/table-list1")
    @Operation(description = "获取碳排基礎訊息明细表格列表")
    @Parameter(in = ParameterIn.HEADER, name = SusDevConsts.HEADER_TOKEN_KEY, schema = @Schema(type = "string"), required = true)
    public ResultList<CeBasicInfoTableDataVO> listCeBasicInfoTable(String headId) {
        return new ResultList<>(ceBasicInfoService.listCeBasicInfoTable(headId));
    }

    @GetMapping("/detail/table-list2")
    @Operation(description = "获取碳排經營數據明细表格列表")
    @Parameter(in = ParameterIn.HEADER, name = SusDevConsts.HEADER_TOKEN_KEY, schema = @Schema(type = "string"), required = true)
    public ResultList<CeOperationDataTableDataVO> listCeOperationDataTable(String headId) {
        return new ResultList<>(ceBasicInfoService.listCeOperationDataTable(headId));
    }

    @PostMapping("/update")
    @Operation(description = "更新碳排基礎訊息")
    @Parameter(in = ParameterIn.HEADER, name = SusDevConsts.HEADER_TOKEN_KEY, schema = @Schema(type = "string"), required = true)
    public ResultBase updateCeBasicInfo(@RequestBody CeBasicInfoHeadVO ceBasicInfoHeadVO) {
        userService.checkIsReadOnly();

        ceBasicInfoService.updateCeBasicInfoWithDetail(ceBasicInfoHeadVO);
        return new ResultBase();
    }

    @PostMapping("/submit")
    @Operation(description = "提交碳排基礎訊息")
    @Parameter(in = ParameterIn.HEADER, name = SusDevConsts.HEADER_TOKEN_KEY, schema = @Schema(type = "string"), required = true)
    public ResultBase submit(@RequestBody IdVersionVO idVersionVO) {
        userService.checkIsReadOnly();

        ceBasicInfoService.submit(idVersionVO);
        return new ResultBase();
    }

    @PostMapping("/approve")
    @Operation(description = "审批碳排基礎訊息")
    @Parameter(in = ParameterIn.HEADER, name = SusDevConsts.HEADER_TOKEN_KEY, schema = @Schema(type = "string"), required = true)
    public ResultBase approve(@RequestBody ApproveParamVO approveParamVO) {
        userService.checkIsReadOnly();

        checkExist(approveParamVO.getId(), "碳排基礎訊息id不能为空");
        CeBasicInfoHead ceBasicInfoHead = ceBasicInfoService.selectByPrimaryKey(approveParamVO.getId());

        WorkflowControl workflowControl = workflowControlService.findWorkflowControlByBusinessId(approveParamVO.getId());

        if (Objects.isNull(workflowControl)) {
            workflowFacade.startWorkflow(ceBasicInfoHead.getOrganizationId(), getFormId(), approveParamVO.getId());
        } else {
            workflowFacade.approve(approveParamVO.getId(), approveParamVO.getRemark());
            // 审核完成，同步省级、市级、详细地址 到  【碳中和】-【信息配置】-【基本信息】的省、市、地盘地址字段
            ceBasicInfoService.syncBasicInfo(ceBasicInfoHead);
        }
        workflowControlService.sendReviewEmail(approveParamVO.getId());
        return new ResultBase();
    }
    @PostMapping("/reject")
    @Operation(description = "驳回碳排基礎訊息")
    @Parameter(in = ParameterIn.HEADER, name = SusDevConsts.HEADER_TOKEN_KEY, schema = @Schema(type = "string"), required = true)
    public ResultBase reject(@RequestBody ApproveParamVO approveParamVO) {
        userService.checkIsReadOnly();

        checkExist(approveParamVO.getId(), "碳排基礎訊息id不能为空");
        CeBasicInfoHead ceBasicInfoHead = ceBasicInfoService.selectByPrimaryKey(approveParamVO.getId());
        Integer year = workflowControlService.findFormYearByBusinessId(approveParamVO.getId());
        Integer month = workflowControlService.findFormMonthByBusinessId(approveParamVO.getId());
        if(Objects.isNull(year)) {
            throw new ServiceException("找不到相關表單年份");
        }
        if(Objects.isNull(month)) {
            throw new ServiceException("找不到相關表單月份");
        }

        Workflow workflow = workflowService.findWorkflowByOrgIdAndFormId(ceBasicInfoHead.getOrganizationId(),
                getFormId(), year, month);
        checkExist(workflow, "工作流未配置");
        workflowFacade.reject(approveParamVO.getId(), approveParamVO.getRemark());
        return new ResultBase();
    }

    @PostMapping("/recall")
    @Operation(description = "撤回碳排基礎訊息")
    @Parameter(in = ParameterIn.HEADER, name = SusDevConsts.HEADER_TOKEN_KEY, schema = @Schema(type = "string"), required = true)
    public ResultBase recall(@RequestBody ApproveParamVO approveParamVO) {
        userService.checkIsReadOnly();

        checkExist(approveParamVO.getId(), "碳排基礎訊息id不能为空");
        CeBasicInfoHead ceBasicInfoHead = ceBasicInfoService.selectByPrimaryKey(approveParamVO.getId());

        Integer year = workflowControlService.findFormYearByBusinessId(approveParamVO.getId());
        Integer month = workflowControlService.findFormMonthByBusinessId(approveParamVO.getId());
        if(Objects.isNull(year)) {
            throw new ServiceException("找不到相關表單年份");
        }
        if(Objects.isNull(month)) {
            throw new ServiceException("找不到相關表單月份");
        }

        Workflow workflow = workflowService.findWorkflowByOrgIdAndFormId(ceBasicInfoHead.getOrganizationId(),
                getFormId(), year, month);
        checkExist(workflow, "工作流未配置");
        workflowFacade.recall(approveParamVO.getId(), approveParamVO.getRemark());
        return new ResultBase();
    }

    @PostMapping("/terminate")
    @Operation(description = "终止碳排基礎訊息")
    @Parameter(in = ParameterIn.HEADER, name = SusDevConsts.HEADER_TOKEN_KEY, schema = @Schema(type = "string"), required = true)
    public ResultBase terminate(@RequestBody ApproveParamVO approveParamVO) {
        userService.checkIsReadOnly();

        checkExist(approveParamVO.getId(), "碳排基礎訊息id不能为空");
        CeBasicInfoHead ceBasicInfoHead = ceBasicInfoService.selectByPrimaryKey(approveParamVO.getId());

        Integer year = workflowControlService.findFormYearByBusinessId(approveParamVO.getId());
        Integer month = workflowControlService.findFormMonthByBusinessId(approveParamVO.getId());
        if(Objects.isNull(year)) {
            throw new ServiceException("找不到相關表單年份");
        }
        if(Objects.isNull(month)) {
            throw new ServiceException("找不到相關表單月份");
        }

        Workflow workflow = workflowService.findWorkflowByOrgIdAndFormId(ceBasicInfoHead.getOrganizationId(),
                getFormId(), year, month);
        checkExist(workflow, "工作流未配置");
        workflowFacade.terminate(approveParamVO.getId(), approveParamVO.getRemark());
        return new ResultBase();
    }

    private String getFormId() {
        if (StringUtils.isBlank(formId)) {
            Form form = formService.findFormByCode("ce-basic-info");
            formId = Optional.ofNullable(form).map(Form::getId).orElseThrow(() -> new ServiceException("未找到表单"));
        }
        return formId;
    }
}
