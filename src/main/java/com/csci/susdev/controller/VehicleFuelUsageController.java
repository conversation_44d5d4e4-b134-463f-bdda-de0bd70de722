package com.csci.susdev.controller;

import com.csci.susdev.annotation.LogMethod;
import com.csci.susdev.constant.SusDevConsts;
import com.csci.susdev.model.ResultBase;
import com.csci.susdev.model.ResultBean;
import com.csci.susdev.model.ResultPage;
import com.csci.susdev.modelcovt.VehicleFuelUsageConverter;
import com.csci.susdev.qo.SyncAmbientEnergyBillQO;
import com.csci.susdev.qo.VehicleFuelUsageQO;
import com.csci.susdev.service.UserService;
import com.csci.susdev.service.VehicleFuelUsageService;
import com.csci.susdev.util.context.ContextUtils;
import com.csci.susdev.vo.IdListVO;
import com.csci.susdev.vo.IdVO;
import com.csci.susdev.vo.VehicleFuelUsageBatchDeleteVO;
import com.csci.susdev.vo.VehicleFuelUsageVO;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.enums.ParameterIn;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.util.CollectionUtils;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;

@RestController
@RequestMapping("/api/vehicleFuelUsage")
@Tag(name = "车辆油耗", description = "车辆油耗")
@LogMethod
public class VehicleFuelUsageController {

    @Resource
    private VehicleFuelUsageService vehicleFuelUsageService;

    @Resource
    private UserService userService;

    @PostMapping("/list")
    @Operation(description = "分页查询车辆油耗列表数据")
    @Parameter(in = ParameterIn.HEADER, name = SusDevConsts.HEADER_TOKEN_KEY, schema = @Schema(type = "string"), required = true)
    public ResultPage<VehicleFuelUsageVO> listVehicleFuelUsage(@RequestBody VehicleFuelUsageQO vehicleFuelUsageQO) {
        return vehicleFuelUsageService.listVehicleFuelUsage(vehicleFuelUsageQO);
    }

    @PostMapping("/save")
    @Operation(description = "保存车辆油耗记录")
    @Parameter(in = ParameterIn.HEADER, name = SusDevConsts.HEADER_TOKEN_KEY, schema = @Schema(type = "string"), required = true)
    public ResultBean<String> save(@RequestBody VehicleFuelUsageVO vehicleFuelUsageVO) {
        userService.checkIsReadOnly();

        return new ResultBean<>(vehicleFuelUsageService.saveVehicleFuelUsage(vehicleFuelUsageVO));
    }

    @PostMapping("/duplicate")
    @Operation(description = "复制车辆油耗记录")
    @Parameter(in = ParameterIn.HEADER, name = SusDevConsts.HEADER_TOKEN_KEY, schema = @Schema(type = "string"), required = true)
    public ResultBean<VehicleFuelUsageVO> duplicate(@RequestBody IdVO idVO) {
        userService.checkIsReadOnly();

        return new ResultBean<>(VehicleFuelUsageConverter.convert(vehicleFuelUsageService.duplicateVehicleFuelUsage(idVO.getId())));
    }

    @PostMapping("/batchDuplicate")
    @Operation(description = "复制车辆油耗记录")
    @Parameter(in = ParameterIn.HEADER, name = SusDevConsts.HEADER_TOKEN_KEY, schema = @Schema(type = "string"), required = true)
    public ResultBase batchDuplicate(@RequestBody IdListVO idListVO) {
        userService.checkIsReadOnly();
        return new ResultBean<>(vehicleFuelUsageService.batchDuplicateVehicleFuelUsage(idListVO.getIdList()));
    }

    @GetMapping("/{id}")
    @Operation(description = "根据id查询车辆油耗记录")
    @Parameter(in = ParameterIn.HEADER, name = SusDevConsts.HEADER_TOKEN_KEY, schema = @Schema(type = "string"), required = true)
    public ResultBean<VehicleFuelUsageVO> getVehicleFuelUsageById(@PathVariable String id) {
        return new ResultBean<>(vehicleFuelUsageService.getVehicleFuelUsageById(id));
    }

    @DeleteMapping("/{id}")
    @Operation(description = "根据id删除车辆油耗记录")
    @Parameter(in = ParameterIn.HEADER, name = SusDevConsts.HEADER_TOKEN_KEY, schema = @Schema(type = "string"), required = true)
    public ResultBase deleteVehicleFuelUsageById(@PathVariable String id) {
        userService.checkIsReadOnly();

        vehicleFuelUsageService.deleteVehicleFuelUsageById(id);
        return new ResultBase();
    }

    @PostMapping("/batchDelete")
    @Operation(description = "批量删除车辆油耗记录")
    @Parameter(in = ParameterIn.HEADER, name = SusDevConsts.HEADER_TOKEN_KEY, schema = @Schema(type = "string"), required = true)
    public ResultBean<String> batchDelete(@RequestBody VehicleFuelUsageBatchDeleteVO vehicleFuelUsageBatchDeleteVO) {
        userService.checkIsReadOnly();

        return new ResultBean<>(vehicleFuelUsageService.batchDeleteVehicleFuelUsageById(vehicleFuelUsageBatchDeleteVO));
    }

    @PostMapping("/save/all")
    @Operation(description = "批量保存车辆用油记录")
    @Parameter(in = ParameterIn.HEADER, name = SusDevConsts.HEADER_TOKEN_KEY, schema = @Schema(type = "string"), required = true)
    public ResultBean<List<String>> saveAll(@RequestBody List<VehicleFuelUsageVO> vehicleFuelUsageVOList) {
        userService.checkIsReadOnly();

        return new ResultBean<>(vehicleFuelUsageService.saveVehicleFuelUsageList(vehicleFuelUsageVOList));
    }

    @PostMapping("/synchronization-data")
    @Operation(description = "同步数据")
    @Parameter(in = ParameterIn.HEADER, name = SusDevConsts.HEADER_TOKEN_KEY, schema = @Schema(type = "string"), required = true)
    public ResultBase synchronizationData(@RequestBody SyncAmbientEnergyBillQO syncAmbientEnergyBillQO) {
        userService.checkIsReadOnly();

        vehicleFuelUsageService.synchronizationData(syncAmbientEnergyBillQO);
        return new ResultBase();
    }
}
