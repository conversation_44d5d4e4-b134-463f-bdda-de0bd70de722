package com.csci.susdev.controller;

import com.csci.susdev.annotation.LogMethod;
import com.csci.susdev.constant.SusDevConsts;
import com.csci.susdev.model.ResultBean;
import com.csci.susdev.model.ResultPage;
import com.csci.susdev.service.TzhEmissionReductionService;
import com.csci.susdev.service.UserService;
import com.csci.susdev.util.context.ContextUtils;
import com.csci.tzh.dto.TzhEmissionReductionDTO;
import com.csci.tzh.model.TzhEmissionReduction;
import com.csci.tzh.qo.TzhEmissionReductionQO;
import com.csci.tzh.qo.TzhEmissionReductionSummaryQO;
import com.csci.tzh.vo.TzhEmissionReductionVO;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.enums.ParameterIn;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;

@RestController
@RequestMapping(value = "/api/tzh/tzh-emission-reduction", produces = "application/json")
@Tag(name = "減排填表 接口展示", description = "用于接口调试")
@LogMethod
public class TzhEmissionReductionController {

    @Autowired
    private TzhEmissionReductionService service;

    @Resource
    private UserService userService;

    private class ResultPageOfModel extends ResultPage<TzhEmissionReduction>{
		public ResultPageOfModel(List<?> page) {
			super(page);
		}
	}
    
    @GetMapping("/list")
    @Operation(description = "查詢 減排填表 接口數據列表")
    @Parameter(in = ParameterIn.HEADER, name = SusDevConsts.HEADER_TOKEN_KEY, schema = @Schema(type = "string"), required = true)
    @ApiResponse(responseCode = "200", content = @Content(schema = @Schema(implementation = ResultPageOfModel.class)))
    public List<TzhEmissionReductionVO> list(TzhEmissionReductionQO qo) {
        return service.list(qo);
    }

    @GetMapping("/summary")
    @Operation(description = "查詢 減排填表 接口數據")
    @Parameter(in = ParameterIn.HEADER, name = SusDevConsts.HEADER_TOKEN_KEY, schema = @Schema(type = "string"), required = true)
    public ResultBean<Map> list(TzhEmissionReductionSummaryQO qo) {
        return new ResultBean<>(service.summary(qo));
    }

    @PostMapping("/list/save")
    @Operation(description = "保存 減排填表 所有記錄")
    @Parameter(in = ParameterIn.HEADER, name = SusDevConsts.HEADER_TOKEN_KEY, schema = @Schema(type = "string"), required = true)
    public ResultBean<List<TzhEmissionReduction>> save(@RequestBody List<TzhEmissionReductionDTO> lstDto) {
        userService.checkIsReadOnly();

        return new ResultBean<>(service.saveList(lstDto));
    }
}
