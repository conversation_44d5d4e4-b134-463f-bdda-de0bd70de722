package com.csci.susdev.controller;

import com.csci.susdev.annotation.LogMethod;
import com.csci.susdev.constant.SusDevConsts;
import com.csci.susdev.model.ResultBase;
import com.csci.susdev.model.ResultBean;
import com.csci.susdev.model.ResultPage;
import com.csci.susdev.qo.CarbonEmissionLocationQO;
import com.csci.susdev.service.CarbonEmissionLocationService;
import com.csci.susdev.service.UserService;
import com.csci.susdev.vo.CarbonEmissionLocationVO;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.enums.ParameterIn;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;

@RestController
@RequestMapping(value = "/api/carbonEmissionLocation", produces = "application/json")
@Tag(name = "排放場景 接口", description = "用于接口调试")
@LogMethod
public class CarbonEmissionLocationController {

    @Resource
    private CarbonEmissionLocationService carbonEmissionLocationService;

    @Resource
    private UserService userService;

    @PostMapping("/save")
    @Operation(description = "保存记录")
    @Parameter(in = ParameterIn.HEADER, name = SusDevConsts.HEADER_TOKEN_KEY, schema = @Schema(type = "string"), required = true)
    public ResultBean<String> save(@RequestBody CarbonEmissionLocationVO carbonEmissionLocationVO) {
        userService.checkIsReadOnly();

        return new ResultBean<>(carbonEmissionLocationService.saveCarbonEmissionLocation(carbonEmissionLocationVO));
    }

    @PostMapping("/list")
    @Operation(description = "查询记录")
    @Parameter(in = ParameterIn.HEADER, name = SusDevConsts.HEADER_TOKEN_KEY, schema = @Schema(type = "string"), required = true)
    public ResultPage<CarbonEmissionLocationVO> listCarbonEmissionLocation(@RequestBody CarbonEmissionLocationQO carbonEmissionLocationQO) {
        return carbonEmissionLocationService.listCarbonEmissionLocation(carbonEmissionLocationQO);
    }

    @DeleteMapping("/{id}")
    @Operation(description = "删除记录")
    @Parameter(in = ParameterIn.HEADER, name = SusDevConsts.HEADER_TOKEN_KEY, schema = @Schema(type = "string"), required = true)
    public ResultBase deleteCarbonEmissionLocation(@PathVariable String id) {
        userService.checkIsReadOnly();

        carbonEmissionLocationService.deleteCarbonEmissionLocation(id);
        return ResultBase.success();
    }

    @GetMapping("/{id}")
    @Operation(description = "获取记录")
    @Parameter(in = ParameterIn.HEADER, name = SusDevConsts.HEADER_TOKEN_KEY, schema = @Schema(type = "string"), required = true)
    public ResultBean<CarbonEmissionLocationVO> getCarbonEmissionLocation(@PathVariable String id) {
        return new ResultBean<>(carbonEmissionLocationService.getCarbonEmissionLocation(id));
    }
}
