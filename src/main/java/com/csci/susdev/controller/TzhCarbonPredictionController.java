package com.csci.susdev.controller;

import com.csci.susdev.annotation.LogMethod;
import com.csci.susdev.constant.SusDevConsts;
import com.csci.susdev.model.*;
import com.csci.susdev.qo.*;
import com.csci.susdev.service.*;
import com.csci.susdev.util.context.ContextUtils;
import com.csci.tzh.vo.TzhCarbonPredictionVO;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.enums.ParameterIn;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.tags.Tag;

import java.io.IOException;
import java.util.List;

import javax.servlet.http.HttpServletResponse;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.io.InputStreamResource;
import org.springframework.core.io.Resource;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

@RestController
@RequestMapping(value = "/api/tzhcarbonprediction", produces = "application/json")
@Tag(name = "碳中和 - 減排預測表 接口展示", description = "用于接口调试")
@LogMethod
public class TzhCarbonPredictionController {

    @Autowired
    private TzhCarbonPredictionService tzhCarbonPredictionService;

    @javax.annotation.Resource
    private UserService userService;

    private class ResultBeanOfTzhCarbonPredictionVO extends ResultBean<TzhCarbonPredictionVO>{}
    
    private class ResultPageOfTzhCarbonPredictionVO extends ResultPage<TzhCarbonPredictionVO>{
		public ResultPageOfTzhCarbonPredictionVO(List<?> page) {
			super(page);
		}
	}

    @PostMapping("/export")
    @Operation(description = "導出 碳中和 - 減排預測表 數據")
    @Parameter(in = ParameterIn.HEADER, name = SusDevConsts.HEADER_TOKEN_KEY, schema = @Schema(type = "string"), required = true)
    public ResponseEntity<Resource> exportExcel(HttpServletResponse response, @RequestBody TzhCarbonPredictionExportQO tzhCarbonPredictionExportQO) throws IOException {
        InputStreamResource file = new InputStreamResource(tzhCarbonPredictionService.exportExcel(tzhCarbonPredictionExportQO));
        return ResponseEntity.ok()
            .header(HttpHeaders.CONTENT_DISPOSITION, "attachment; filename=" + java.net.URLEncoder.encode(tzhCarbonPredictionExportQO.getFilename(), "UTF-8") + ".xlsx")
            .contentType(MediaType.parseMediaType("application/vnd.ms-excel"))
            .body(file);
    }

    @PostMapping(value = "/import", produces = "application/json", consumes = MediaType.MULTIPART_FORM_DATA_VALUE)
    @Operation(description = "導入 碳中和 - 減排預測表 數據")
    @Parameter(in = ParameterIn.HEADER, name = SusDevConsts.HEADER_TOKEN_KEY, schema = @Schema(type = "string"), required = true)
    @ApiResponse(responseCode = "200", content = @Content(schema = @Schema(implementation = ResultBeanOfTzhCarbonPredictionVO.class)))
    public ResultBean<TzhCarbonPredictionVO> importExcel(@RequestPart("file") MultipartFile file, TzhCarbonPredictionHead tzhCarbonPredictionHead) throws IOException {
        userService.checkIsReadOnly();

        return new ResultBean<>(tzhCarbonPredictionService.importExcel(file, tzhCarbonPredictionHead, ""));
    }
    
    @GetMapping("/list")
    @Operation(description = "查詢 碳中和 - 減排預測表 接口數據列表")
    @Parameter(in = ParameterIn.HEADER, name = SusDevConsts.HEADER_TOKEN_KEY, schema = @Schema(type = "string"), required = true)
    @ApiResponse(responseCode = "200", content = @Content(schema = @Schema(implementation = ResultPageOfTzhCarbonPredictionVO.class)))
    public ResultPage<TzhCarbonPredictionVO> listTzhCarbonPrediction(TzhCarbonPredictionPageableQO tzhCarbonPredictionQO) {
        return tzhCarbonPredictionService.listTzhCarbonPrediction(tzhCarbonPredictionQO);
    }
    
    @GetMapping("/single")
    @Operation(description = "查詢 碳中和 - 減排預測表 接口數據")
    @Parameter(in = ParameterIn.HEADER, name = SusDevConsts.HEADER_TOKEN_KEY, schema = @Schema(type = "string"), required = true)
    @ApiResponse(responseCode = "200", content = @Content(schema = @Schema(implementation = ResultBeanOfTzhCarbonPredictionVO.class)))
    public ResultBean<TzhCarbonPredictionVO> getTzhCarbonPrediction(TzhCarbonPredictionQO tzhCarbonPredictionQO) {
        return new ResultBean<>(tzhCarbonPredictionService.getTzhCarbonPrediction(tzhCarbonPredictionQO));
    }

    @PostMapping("/submit")
    @Operation(description = "提交 碳中和 - 減排預測表 所有記錄")
    @Parameter(in = ParameterIn.HEADER, name = SusDevConsts.HEADER_TOKEN_KEY, schema = @Schema(type = "string"), required = true)
    public ResultBean<String> submit(@RequestBody TzhCarbonPredictionVO tzhCarbonPredictionVO) {
        userService.checkIsReadOnly();

        return new ResultBean<>(tzhCarbonPredictionService.submitTzhCarbonPrediction(tzhCarbonPredictionVO, ""));
    }
    
    @PostMapping("/save")
    @Operation(description = "保存 碳中和 - 減排預測表 所有記錄")
    @Parameter(in = ParameterIn.HEADER, name = SusDevConsts.HEADER_TOKEN_KEY, schema = @Schema(type = "string"), required = true)
    public ResultBean<String> save(@RequestBody TzhCarbonPredictionVO tzhCarbonPredictionVO) {
        userService.checkIsReadOnly();

        return new ResultBean<>(tzhCarbonPredictionService.saveTzhCarbonPredictionWithStatus(tzhCarbonPredictionVO, ""));
    }
}
