package com.csci.susdev.controller;

import com.csci.susdev.annotation.LogMethod;
import com.csci.susdev.constant.SusDevConsts;
import com.csci.susdev.model.*;
import com.csci.susdev.qo.*;
import com.csci.susdev.service.*;
import com.csci.susdev.util.context.ContextUtils;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.enums.ParameterIn;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.tags.Tag;

import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;

@RestController
@RequestMapping(value = "/api/permission", produces = "application/json")
@Tag(name = "權限 接口展示", description = "用于接口调试")
@LogMethod
public class PermissionController {


    @Resource
    private UserService userService;

    @Autowired
    private PermissionService permissionService;

    private class ResultPageOfPermission extends ResultPage<Permission>{
		public ResultPageOfPermission(List<?> page) {
			super(page);
		}
	}

    /**
     * 查找 權限 数据列表
     *
     * @param permissionQO
     * @return
     */
    @GetMapping("/list")
    @Operation(description = "分页查询 權限 统计数据列表")
    @Parameter(in = ParameterIn.HEADER, name = SusDevConsts.HEADER_TOKEN_KEY, schema = @Schema(type = "string"), required = true)
    @ApiResponse(responseCode = "200", content = @Content(schema = @Schema(implementation = ResultPageOfPermission.class)))
    public ResultPage<Permission> list(PermissionPageableQO permissionQO) {
        return permissionService.listPermission(permissionQO);
    }
    
    @PostMapping("/save")
    @Operation(description = "保存 權限 記錄")
    @Parameter(in = ParameterIn.HEADER, name = SusDevConsts.HEADER_TOKEN_KEY, schema = @Schema(type = "string"), required = true)
    public ResultBean<Permission> save(@RequestBody Permission permission) {
        userService.checkIsReadOnly();

        return new ResultBean<>(permissionService.savePermission(permission));
    }
    
    @PostMapping("/savelist")
    @Operation(description = "保存 權限 記錄列表")
    @Parameter(in = ParameterIn.HEADER, name = SusDevConsts.HEADER_TOKEN_KEY, schema = @Schema(type = "string"), required = true)
    public ResultBean<List<Permission>> savelist(@RequestBody List<Permission> permissionLst) {
        userService.checkIsReadOnly();

        return new ResultBean<>(permissionService.savePermissionList(permissionLst));
    }

    @DeleteMapping("/delete/{id}")
    @Operation(description = "刪除 權限 記錄")
    @Parameter(in = ParameterIn.HEADER, name = SusDevConsts.HEADER_TOKEN_KEY, schema = @Schema(type = "string"), required = true)
    public ResultBean<Integer> delete(@PathVariable String id) {
        userService.checkIsReadOnly();

        return new ResultBean<>(Integer.valueOf(permissionService.deletePermission(id)));
    }
}
