package com.csci.susdev.controller;

import com.csci.susdev.annotation.LogMethod;
import com.csci.susdev.constant.SusDevConsts;
import com.csci.susdev.model.ResultBase;
import com.csci.susdev.model.ResultBean;
import com.csci.susdev.model.ResultPage;
import com.csci.susdev.qo.FcFactorConversionQO;
import com.csci.susdev.service.FcFactorConversionService;
import com.csci.susdev.service.UserService;
import com.csci.susdev.vo.FcFactorConversionVO;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.enums.ParameterIn;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;

@RestController
@RequestMapping(value = "/api/fcFactorConversion", produces = "application/json")
@Tag(name = "因子管理-因子换算 接口", description = "用于接口调试")
@LogMethod
public class FcFactorConversionController {

    @Resource
    private FcFactorConversionService fcFactorConversionService;

    @Resource
    private UserService userService;

    @PostMapping("/save")
    @Operation(description = "保存记录")
    @Parameter(in = ParameterIn.HEADER, name = SusDevConsts.HEADER_TOKEN_KEY, schema = @Schema(type = "string"), required = true)
    public ResultBean<String> save(@RequestBody FcFactorConversionVO fcFactorConversionVO) {
        userService.checkIsReadOnly();

        return new ResultBean<>(fcFactorConversionService.saveFcFactorConversion(fcFactorConversionVO));
    }

    @PostMapping("/list")
    @Operation(description = "查询记录")
    @Parameter(in = ParameterIn.HEADER, name = SusDevConsts.HEADER_TOKEN_KEY, schema = @Schema(type = "string"), required = true)
    public ResultPage<FcFactorConversionVO> listFcFactorConversion(@RequestBody FcFactorConversionQO fcFactorConversionQO) {
        return fcFactorConversionService.listFcFactorConversion(fcFactorConversionQO);
    }

    @DeleteMapping("/{id}")
    @Operation(description = "删除记录")
    @Parameter(in = ParameterIn.HEADER, name = SusDevConsts.HEADER_TOKEN_KEY, schema = @Schema(type = "string"), required = true)
    public ResultBase deleteFcFactorConversion(@PathVariable String id) {
        userService.checkIsReadOnly();

        fcFactorConversionService.deleteFcFactorConversion(id);
        return ResultBase.success();
    }

    @GetMapping("/{id}")
    @Operation(description = "获取记录")
    @Parameter(in = ParameterIn.HEADER, name = SusDevConsts.HEADER_TOKEN_KEY, schema = @Schema(type = "string"), required = true)
    public ResultBean<FcFactorConversionVO> getFcFactorConversion(@PathVariable String id) {
        return new ResultBean<>(fcFactorConversionService.getFcFactorConversion(id));
    }

    @PostMapping("/save/all")
    @Operation(description = "批量保存因子换算记录")
    @Parameter(in = ParameterIn.HEADER, name = SusDevConsts.HEADER_TOKEN_KEY, schema = @Schema(type = "string"), required = true)
    public ResultBean<List<String>> saveFcFactorConversionList(@RequestBody List<FcFactorConversionVO> fcFactorConversionVOList) {
        userService.checkIsReadOnly();

        return new ResultBean<>(fcFactorConversionService.saveFcFactorConversionList(fcFactorConversionVOList));
    }

    @PostMapping("/export")
    @Operation(description = "导出查询记录")
    @Parameter(in = ParameterIn.HEADER, name = SusDevConsts.HEADER_TOKEN_KEY, schema = @Schema(type = "string"), required = true)
    public ResponseEntity<byte[]> exportFcFactorConversion(@RequestBody FcFactorConversionQO fcFactorConversionQO) {
        fcFactorConversionQO.setCurPage(1);
        fcFactorConversionQO.setPageSize(10000);
        HttpHeaders headers = new HttpHeaders();
        byte[] bytes  = fcFactorConversionService.exportFcFactorConversion(fcFactorConversionQO,headers);
        headers.setContentType(MediaType.APPLICATION_OCTET_STREAM);
        return ResponseEntity.ok()
                .headers(headers)
                .body(bytes);

    }
}
