package com.csci.susdev.controller;

import com.csci.susdev.annotation.LogMethod;
import com.csci.susdev.constant.SusDevConsts;
import com.csci.susdev.model.ResultBean;
import com.csci.susdev.service.TzhProtocolService;
import com.csci.tzh.model.TzhProtocol;
import com.csci.tzh.model.TzhProtocolCategory;
import com.csci.tzh.model.TzhProtocolSubCategory;
import com.csci.tzh.qo.ProtocolQO;
import com.csci.tzh.vo.TzhProtocolSubCategoryVO;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.enums.ParameterIn;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

@RestController
@RequestMapping(value = "/api/tzh/tzh-protocol", produces = "application/json")
@Tag(name = "減排協議 接口展示", description = "用于接口调试")
@LogMethod
public class TzhProtocolController {

    @Autowired
    private TzhProtocolService service;

    @GetMapping("/list")
    @Operation(description = "查詢 減排協議 接口數據列表")
    @Parameter(in = ParameterIn.HEADER, name = SusDevConsts.HEADER_TOKEN_KEY, schema = @Schema(type = "string"), required = true)
    @ApiResponse(responseCode = "200")
    public ResultBean<List<TzhProtocol>> list() {
        return new ResultBean<>(service.list());
    }

    @GetMapping("/category-detail/list")
    @Operation(description = "查詢 協議範圍明細 接口數據列表")
    @Parameter(in = ParameterIn.HEADER, name = SusDevConsts.HEADER_TOKEN_KEY, schema = @Schema(type = "string"), required = true)
    public ResultBean<List<TzhProtocolSubCategoryVO>> listCategoryDetail(ProtocolQO qo) {
        return new ResultBean<>(service.listSubCategoryDetail(qo.getProtocol()));
    }

    @GetMapping("/category/list")
    @Operation(description = "查詢 協議範圍大項 接口數據列表")
    @Parameter(in = ParameterIn.HEADER, name = SusDevConsts.HEADER_TOKEN_KEY, schema = @Schema(type = "string"), required = true)
    public ResultBean<List<TzhProtocolCategory>> listCategory(ProtocolQO qo) {
        return new ResultBean<>(service.listCategory(qo.getProtocol()));
    }

    @GetMapping("/sub-category/list")
    @Operation(description = "查詢 協議範圍細項 接口數據列表")
    @Parameter(in = ParameterIn.HEADER, name = SusDevConsts.HEADER_TOKEN_KEY, schema = @Schema(type = "string"), required = true)
    public ResultBean<List<TzhProtocolSubCategory>> listSubCategory(ProtocolQO qo) {
        return new ResultBean<>(service.listSubCategory(qo.getProtocol()));
    }
}
