package com.csci.susdev.controller;

import com.csci.susdev.annotation.LogMethod;
import com.csci.susdev.constant.SusDevConsts;
import com.csci.susdev.model.ResultBean;
import com.csci.susdev.model.ResultPage;
import com.csci.susdev.model.Role;
import com.csci.susdev.qo.RolePageableQO;
import com.csci.susdev.service.RoleService;
import com.csci.susdev.service.UserService;
import com.csci.susdev.util.context.ContextUtils;
import com.csci.susdev.vo.RoleSaveVO;
import com.csci.susdev.vo.RoleVO;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.enums.ParameterIn;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;

@RestController
@RequestMapping(value = "/api/role", produces = "application/json")
@Tag(name = "角色 接口展示", description = "用于接口调试")
@LogMethod
public class RoleController {

    @Autowired
    private RoleService roleService;

    @Resource
    private UserService userService;

    private class ResultPageOfRole extends ResultPage<Role>{
		public ResultPageOfRole(List<?> page) {
			super(page);
		}
	}

    /**
     * 查找 角色 数据列表
     *
     * @param roleQO
     * @return
     */
    @GetMapping("/list")
    @Operation(description = "分页查询 角色 统计数据列表")
    @Parameter(in = ParameterIn.HEADER, name = SusDevConsts.HEADER_TOKEN_KEY, schema = @Schema(type = "string"), required = true)
    @ApiResponse(responseCode = "200", content = @Content(schema = @Schema(implementation = ResultPageOfRole.class)))
    public ResultPage<RoleVO> list(RolePageableQO roleQO) throws Exception {
        if(ContextUtils.getCurrentUser().getUsername().equals("guest_test")) {
            throw  new Exception("沒有權限");
        }
        return roleService.listRole(roleQO);
    }
    
    @PostMapping("/save")
    @Operation(description = "保存 角色 記錄")
    @Parameter(in = ParameterIn.HEADER, name = SusDevConsts.HEADER_TOKEN_KEY, schema = @Schema(type = "string"), required = true)
    public ResultBean<String> save(@RequestBody RoleVO roleVO) {
        userService.checkIsReadOnly();

        return new ResultBean<>(roleService.saveRole(roleVO));
    }

    @DeleteMapping("/delete/{id}")
    @Operation(description = "刪除 權限操作關聯 記錄")
    @Parameter(in = ParameterIn.HEADER, name = SusDevConsts.HEADER_TOKEN_KEY, schema = @Schema(type = "string"), required = true)
    public ResultBean<Integer> delete(@PathVariable String id) {
        userService.checkIsReadOnly();

        return new ResultBean<>(roleService.deleteRole(id));
    }

    @GetMapping("/get/{id}")
    @Operation(description = "根据id查询角色信息")
    @Parameter(in = ParameterIn.HEADER, name = SusDevConsts.HEADER_TOKEN_KEY, schema = @Schema(type = "string"), required = true)
    public ResultBean<RoleVO> getRole(@PathVariable String id) {
        return new ResultBean<>(roleService.getRoleById(id));
    }

    @GetMapping("/get-role-list")
    @Operation(description = "根据username查询該用戶角色列表")
    @Parameter(in = ParameterIn.HEADER, name = SusDevConsts.HEADER_TOKEN_KEY, schema = @Schema(type = "string"), required = true)
    public ResultBean<List<Map<String, Object>>> getRoleList() {
        return new ResultBean<>(roleService.getRoleList());
    }
}
