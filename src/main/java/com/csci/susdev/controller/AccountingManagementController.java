package com.csci.susdev.controller;

import com.csci.susdev.annotation.LogMethod;
import com.csci.susdev.constant.SusDevConsts;
import com.csci.susdev.model.ResultBase;
import com.csci.susdev.model.ResultBean;
import com.csci.susdev.model.ResultPage;
import com.csci.susdev.qo.AccountingManagementQO;
import com.csci.susdev.service.AccountingManagementService;
import com.csci.susdev.service.UserService;
import com.csci.susdev.vo.AccountingManagementCalculationVO;
import com.csci.susdev.vo.AccountingManagementVO;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.enums.ParameterIn;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;

@RestController
@RequestMapping(value = "/api/accountingManagement", produces = "application/json")
@Tag(name = "产品管理-核算管理 接口", description = "用于接口调试")
@LogMethod
public class AccountingManagementController {

    @Resource
    private AccountingManagementService accountingManagementService;

    @Resource
    private UserService userService;

    @PostMapping("/save")
    @Operation(description = "保存记录")
    @Parameter(in = ParameterIn.HEADER, name = SusDevConsts.HEADER_TOKEN_KEY, schema = @Schema(type = "string"), required = true)
    public ResultBean<String> save(@RequestBody AccountingManagementVO accountingManagementVO) {
        userService.checkIsReadOnly();

        return new ResultBean<>(accountingManagementService.saveAccountingManagement(accountingManagementVO));
    }

    @PostMapping("/list")
    @Operation(description = "查询记录")
    @Parameter(in = ParameterIn.HEADER, name = SusDevConsts.HEADER_TOKEN_KEY, schema = @Schema(type = "string"), required = true)
    public ResultPage<AccountingManagementVO> listAccountingManagement(@RequestBody AccountingManagementQO accountingManagementQO) {
        return accountingManagementService.listAccountingManagement(accountingManagementQO);
    }

    @DeleteMapping("/{id}")
    @Operation(description = "删除记录")
    @Parameter(in = ParameterIn.HEADER, name = SusDevConsts.HEADER_TOKEN_KEY, schema = @Schema(type = "string"), required = true)
    public ResultBase deleteFcMaterialFactor(@PathVariable String id) {
        userService.checkIsReadOnly();

        accountingManagementService.deleteAccountingManagement(id);
        return ResultBase.success();
    }

    @GetMapping("/{id}")
    @Operation(description = "获取记录")
    @Parameter(in = ParameterIn.HEADER, name = SusDevConsts.HEADER_TOKEN_KEY, schema = @Schema(type = "string"), required = true)
    public ResultBean<AccountingManagementVO> getAccountingManagement(@PathVariable String id) {
        return new ResultBean<>(accountingManagementService.getAccountingManagement(id));
    }

    @PostMapping("/batchCalculation")
    @Operation(description = "批量计算")
    @Parameter(in = ParameterIn.HEADER, name = SusDevConsts.HEADER_TOKEN_KEY, schema = @Schema(type = "string"), required = true)
    public ResultBean<String> batchCalculation(@RequestBody AccountingManagementCalculationVO accountingManagementCalculationVO) {
        userService.checkIsReadOnly();

        return new ResultBean<>(accountingManagementService.batchCalculation(accountingManagementCalculationVO));
    }

    @PostMapping("/save/all")
    @Operation(description = "批量保存核算管理记录")
    @Parameter(in = ParameterIn.HEADER, name = SusDevConsts.HEADER_TOKEN_KEY, schema = @Schema(type = "string"), required = true)
    public ResultBean<List<String>> saveAccountingManagementList(@RequestBody List<AccountingManagementVO> accountingManagementVOList) {
        userService.checkIsReadOnly();

        return new ResultBean<>(accountingManagementService.saveAccountingManagementList(accountingManagementVOList));
    }

    @PostMapping("/export")
    @Operation(description = "导出查询记录")
    @Parameter(in = ParameterIn.HEADER, name = SusDevConsts.HEADER_TOKEN_KEY, schema = @Schema(type = "string"), required = true)
    public ResponseEntity<byte[]> exportAccountingManagement(@RequestBody AccountingManagementQO accountingManagementQO) {
        accountingManagementQO.setCurPage(1);
        accountingManagementQO.setPageSize(10000);
        HttpHeaders headers = new HttpHeaders();
        byte[] bytes  = accountingManagementService.exportAccountingManagement(accountingManagementQO,headers);
        headers.setContentType(MediaType.APPLICATION_OCTET_STREAM);
        return ResponseEntity.ok()
                .headers(headers)
                .body(bytes);

    }
}
