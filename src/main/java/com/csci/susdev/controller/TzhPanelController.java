package com.csci.susdev.controller;

import com.csci.susdev.annotation.LogMethod;
import com.csci.susdev.constant.SusDevConsts;
import com.csci.susdev.model.ResultBean;
import com.csci.susdev.model.ResultPage;
import com.csci.tzh.model.*;
import com.csci.tzh.qo.*;
import com.csci.tzh.vo.*;
import com.csci.susdev.service.*;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.enums.ParameterIn;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.tags.Tag;

import java.util.List;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.io.IOException;

import javax.servlet.http.HttpServletResponse;

import org.springframework.core.io.InputStreamResource;
import org.springframework.core.io.Resource;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.multipart.MultipartFile;

@RestController
@RequestMapping(value = "/api/tzh/pannel", produces = "application/json")
@Tag(name = "碳中和 - 數據看板 接口展示", description = "用于接口调试")
@LogMethod
public class TzhPanelController {

    @Autowired
    private TzhPanelService tzhPanelService;
    
    @GetMapping("/listcarbonamountbyscopemain")
    @Operation(description = "查詢 碳中和 - 溫室氣體排放量 及 溫室氣體排放分佈 接口數據列表")
    @Parameter(in = ParameterIn.HEADER, name = SusDevConsts.HEADER_TOKEN_KEY, schema = @Schema(type = "string"), required = true)
    @ApiResponse(responseCode = "200")
    public List<CarbonAmountByScopeMainVO> listCarbonAmountByScopeMain(TzhPanelQO tzhPanelQO) {
        return tzhPanelService.listCarbonAmountByScopeMain(tzhPanelQO);
    }
    
    @GetMapping("/listcarbonamountbyscopedetail")
    @Operation(description = "查詢 碳中和 - 溫室氣體排放分佈 明細 接口數據列表")
    @Parameter(in = ParameterIn.HEADER, name = SusDevConsts.HEADER_TOKEN_KEY, schema = @Schema(type = "string"), required = true)
    @ApiResponse(responseCode = "200")
    public List<CarbonAmountByScopeDetailVO> listCarbonAmountByScopeDetail(TzhPanelQO tzhPanelQO) {
        return tzhPanelService.listCarbonAmountByScopeDetail(tzhPanelQO);
    }
    
    @GetMapping("/listcarbonamountpercentagebyscopedetail")
    @Operation(description = "查詢 碳中和 - 溫室氣體排放類別 接口數據列表")
    @Parameter(in = ParameterIn.HEADER, name = SusDevConsts.HEADER_TOKEN_KEY, schema = @Schema(type = "string"), required = true)
    @ApiResponse(responseCode = "200")
    public List<CarbonAmountPercentageByScopeDetailVO> listCarbonAmountPercentageByScopeDetail(TzhPanelQO tzhPanelQO) {
        return tzhPanelService.listCarbonAmountPercentageByScopeDetail(tzhPanelQO);
    }
}
