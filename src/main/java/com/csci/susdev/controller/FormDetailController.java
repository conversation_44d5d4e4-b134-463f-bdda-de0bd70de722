package com.csci.susdev.controller;

import com.csci.susdev.annotation.LogMethod;
import com.csci.susdev.constant.SusDevConsts;
import com.csci.susdev.model.Form;
import com.csci.susdev.model.ResultBase;
import com.csci.susdev.model.ResultBean;
import com.csci.susdev.model.ResultPage;
import com.csci.susdev.qo.FormDetailQO;
import com.csci.susdev.service.FormDetailService;
import com.csci.susdev.service.UserService;
import com.csci.susdev.vo.FormDetailVO;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.enums.ParameterIn;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;

@RestController
@RequestMapping(value = "/api/formDetail", produces = "application/json")
@Tag(name = "表單明細 接口", description = "用于接口调试")
@LogMethod
public class FormDetailController {

    @Resource
    private FormDetailService formDetailService;

    @Resource
    private UserService userService;
    @PostMapping("/list")
    @Operation(description = "查询记录")
    @Parameter(in = ParameterIn.HEADER, name = SusDevConsts.HEADER_TOKEN_KEY, schema = @Schema(type = "string"), required = true)
    public ResultPage<FormDetailVO> listFormDetail(@RequestBody FormDetailQO formDetailQO) {
        return formDetailService.listFormDetail(formDetailQO);
    }

    @PostMapping("/save")
    @Operation(description = "保存 表单明细 記錄")
    @Parameter(in = ParameterIn.HEADER, name = SusDevConsts.HEADER_TOKEN_KEY, schema = @Schema(type = "string"), required = true)
    public ResultBean<String> save(@RequestBody FormDetailVO formDetailVO) {
        userService.checkIsReadOnly();

        return new ResultBean<>(formDetailService.saveFormDetail(formDetailVO));
    }

    @DeleteMapping("/delete/{id}")
    @Operation(description = "刪除 表单明细 記錄")
    @Parameter(in = ParameterIn.HEADER, name = SusDevConsts.HEADER_TOKEN_KEY, schema = @Schema(type = "string"), required = true)
    public ResultBase delete(@PathVariable String id) {
        userService.checkIsReadOnly();
        formDetailService.deleteFormDetail(id);
        return ResultBase.success();
    }
}
