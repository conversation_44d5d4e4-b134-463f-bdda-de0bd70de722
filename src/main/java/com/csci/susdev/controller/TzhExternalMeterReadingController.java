package com.csci.susdev.controller;

import com.csci.susdev.annotation.LogMethod;
import com.csci.susdev.model.ResultBean;
import com.csci.susdev.qo.TzhExternalMeterReadingQO;
import com.csci.susdev.service.TzhExternalMeterReadingService;
import com.csci.susdev.service.TzhExternalRequestPayloadService;
import com.csci.tzh.model.TzhExternalMeterReading;
import com.csci.tzh.model.TzhExternalRequestPayload;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.json.JsonMapper;
import com.fasterxml.jackson.datatype.jsr310.JavaTimeModule;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

@RestController
@RequestMapping(value = "/api/tzh/external/meter-reading", produces = "application/json")
@Tag(name = "工业用电(外部) 接口展示", description = "用于接口调试")
@LogMethod
public class TzhExternalMeterReadingController {

    @Autowired
    private TzhExternalMeterReadingService service;

    @Autowired
    private TzhExternalRequestPayloadService erpService;

    @RequestMapping(value = "/save", method = RequestMethod.POST, consumes = "application/json")
    @Operation(description = "保存 物聯網錶讀數(對外)")
    public ResultBean<TzhExternalMeterReading> save(@RequestBody String payload) throws Exception {

        TzhExternalRequestPayload erp = new TzhExternalRequestPayload();
        erp.setUrl("/api/tzh/external/meter-reading/save");
        erp.setPayload(payload);
        erpService.save(erp);

        ObjectMapper om = JsonMapper.builder()
                .addModule(new JavaTimeModule())
                .build();
        return new ResultBean<>(service.save(om.readValue(payload, TzhExternalMeterReadingQO.class)));
    }
}
