package com.csci.susdev.controller;

import com.csci.common.exception.ServiceException;
import com.csci.susdev.annotation.LogMethod;
import com.csci.susdev.constant.SusDevConsts;
import com.csci.susdev.facade.WorkflowFacade;
import com.csci.susdev.model.*;
import com.csci.susdev.qo.FfCmMobileQO;
import com.csci.susdev.service.*;
import com.csci.susdev.util.context.ContextUtils;
import com.csci.susdev.util.redis.RedisLockUtil;
import com.csci.susdev.vo.*;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.enums.ParameterIn;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.Objects;
import java.util.Optional;

import static com.csci.susdev.service.ServiceHelper.checkExist;

@RestController
@RequestMapping(value = "/api/ff-cm-mobile", produces = "application/json")
@Tag(name = "分判商移動源 接口展示", description = "用于接口调试")
@LogMethod
public class FfCmMobileController {

    /**
     * 日志记录对象
     */
    private static final Logger logger = LoggerFactory.getLogger(FfCmMobileController.class);

    @Resource
    private FfCmMobileService ffCmMobileService;

    @Resource
    private UserService userService;

    @Resource
    private WorkflowFacade workflowFacade;

    @Resource
    private FormService formService;

    @Resource
    private WorkflowControlService workflowControlService;

    @Resource
    private WorkflowService workflowService;

    private String formId;

    @PostMapping("/get-or-init")
    @Operation(description = "获取表格")
    @Parameter(in = ParameterIn.HEADER, name = SusDevConsts.HEADER_TOKEN_KEY, schema = @Schema(type = "string"), required = true)
    public ResultBean<FfCmMobileHeadVO> getOrInit(@RequestBody FfCmMobileQO ffCmMobileQO) {
        return new ResultBean<>(ffCmMobileService.getOrInit(ffCmMobileQO));
    }

    @GetMapping("/detail/list")
    @Operation(description = "获取分判商移動源明细列表")
    @Parameter(in = ParameterIn.HEADER, name = SusDevConsts.HEADER_TOKEN_KEY, schema = @Schema(type = "string"), required = true)
    public ResultList<FfCmMobileDetailVO> listFfCmMobileDetail(String headId) {
        return new ResultList<>(ffCmMobileService.listFfCmMobileDetail(headId));
    }

    @GetMapping("/detail/table-list")
    @Operation(description = "获取分判商移動源明细表格列表")
    @Parameter(in = ParameterIn.HEADER, name = SusDevConsts.HEADER_TOKEN_KEY, schema = @Schema(type = "string"), required = true)
    public ResultList<FfCmMobileTableDataVO> listFfCmMobileTable(String headId) {
        return new ResultList<>(ffCmMobileService.listFfCmMobileTable(headId));
    }

    @PostMapping("/update")
    @Operation(description = "更新分判商移動源")
    @Parameter(in = ParameterIn.HEADER, name = SusDevConsts.HEADER_TOKEN_KEY, schema = @Schema(type = "string"), required = true)
    public ResultBase updateFfCmMobile(@RequestBody FfCmMobileHeadVO ffCmMobileHeadVO) {
        userService.checkIsReadOnly();

        ffCmMobileService.updateFfCmMobileWithDetail(ffCmMobileHeadVO);
        return new ResultBase();
    }

    @PostMapping("/submit")
    @Operation(description = "提交分判商移動源")
    @Parameter(in = ParameterIn.HEADER, name = SusDevConsts.HEADER_TOKEN_KEY, schema = @Schema(type = "string"), required = true)
    public ResultBase submit(@RequestBody IdVersionVO idVersionVO) {
        userService.checkIsReadOnly();

        ffCmMobileService.submit(idVersionVO);
        return new ResultBase();
    }

    @PostMapping("/approve")
    @Operation(description = "审批分判商移動源")
    @Parameter(in = ParameterIn.HEADER, name = SusDevConsts.HEADER_TOKEN_KEY, schema = @Schema(type = "string"), required = true)
    public ResultBase approve(@RequestBody ApproveParamVO approveParamVO) {
        userService.checkIsReadOnly();

        checkExist(approveParamVO.getId(), "分判商移動源id不能为空");
        FfCmMobileHead ffCmMobileHead = ffCmMobileService.selectByPrimaryKey(approveParamVO.getId());

        WorkflowControl workflowControl = workflowControlService.findWorkflowControlByBusinessId(approveParamVO.getId());
        if (Objects.isNull(workflowControl)) {
            workflowFacade.startWorkflow(ffCmMobileHead.getOrganizationId(), getFormId(), approveParamVO.getId());
        } else {
            workflowFacade.approve(approveParamVO.getId(), approveParamVO.getRemark());
        }
        workflowControlService.sendReviewEmail(approveParamVO.getId());
        return new ResultBase();
    }

    @PostMapping("/reject")
    @Operation(description = "驳回分判商移動源")
    @Parameter(in = ParameterIn.HEADER, name = SusDevConsts.HEADER_TOKEN_KEY, schema = @Schema(type = "string"), required = true)
    public ResultBase reject(@RequestBody ApproveParamVO approveParamVO) {
        userService.checkIsReadOnly();

        checkExist(approveParamVO.getId(), "分判商移動源id不能为空");
        FfCmMobileHead ffCmMobileHead = ffCmMobileService.selectByPrimaryKey(approveParamVO.getId());

        Integer year = workflowControlService.findFormYearByBusinessId(approveParamVO.getId());
        Integer month = workflowControlService.findFormMonthByBusinessId(approveParamVO.getId());
        if(Objects.isNull(year)) {
            throw new ServiceException("找不到相關表單年份");
        }
        if(Objects.isNull(month)) {
            throw new ServiceException("找不到相關表單月份");
        }

        Workflow workflow = workflowService.findWorkflowByOrgIdAndFormId(ffCmMobileHead.getOrganizationId(),
                getFormId(), year, month);
        checkExist(workflow, "工作流未配置");

        workflowFacade.reject(approveParamVO.getId(), approveParamVO.getRemark());
        return new ResultBase();
    }

    @PostMapping("/recall")
    @Operation(description = "撤回分判商移動源")
    @Parameter(in = ParameterIn.HEADER, name = SusDevConsts.HEADER_TOKEN_KEY, schema = @Schema(type = "string"), required = true)
    public ResultBase recall(@RequestBody ApproveParamVO approveParamVO) {
        userService.checkIsReadOnly();

        checkExist(approveParamVO.getId(), "分判商移動源id不能为空");
        FfCmMobileHead ffCmMobileHead = ffCmMobileService.selectByPrimaryKey(approveParamVO.getId());

        Integer year = workflowControlService.findFormYearByBusinessId(approveParamVO.getId());
        Integer month = workflowControlService.findFormMonthByBusinessId(approveParamVO.getId());
        if(Objects.isNull(year)) {
            throw new ServiceException("找不到相關表單年份");
        }
        if(Objects.isNull(month)) {
            throw new ServiceException("找不到相關表單月份");
        }

        Workflow workflow = workflowService.findWorkflowByOrgIdAndFormId(ffCmMobileHead.getOrganizationId(),
                getFormId(), year, month);
        checkExist(workflow, "工作流未配置");

        workflowFacade.recall(approveParamVO.getId(), approveParamVO.getRemark());
        return new ResultBase();
    }

    @PostMapping("/terminate")
    @Operation(description = "终止分判商移動源")
    @Parameter(in = ParameterIn.HEADER, name = SusDevConsts.HEADER_TOKEN_KEY, schema = @Schema(type = "string"), required = true)
    public ResultBase terminate(@RequestBody ApproveParamVO approveParamVO) {
        userService.checkIsReadOnly();

        checkExist(approveParamVO.getId(), "分判商移動源id不能为空");
        FfCmMobileHead ffCmMobileHead = ffCmMobileService.selectByPrimaryKey(approveParamVO.getId());

        Integer year = workflowControlService.findFormYearByBusinessId(approveParamVO.getId());
        Integer month = workflowControlService.findFormMonthByBusinessId(approveParamVO.getId());
        if(Objects.isNull(year)) {
            throw new ServiceException("找不到相關表單年份");
        }
        if(Objects.isNull(month)) {
            throw new ServiceException("找不到相關表單月份");
        }

        Workflow workflow = workflowService.findWorkflowByOrgIdAndFormId(ffCmMobileHead.getOrganizationId(),
                getFormId(), year, month);
        checkExist(workflow, "工作流未配置");

        workflowFacade.terminate(approveParamVO.getId(), approveParamVO.getRemark());
        return new ResultBase();
    }

    private String getFormId() {
        if (StringUtils.isBlank(formId)) {
            Form form = formService.findFormByCode("ff-cm-mobile");
            formId = Optional.ofNullable(form).map(Form::getId).orElseThrow(() -> new ServiceException("未找到表单"));
        }
        return formId;
    }
}
