package com.csci.susdev.controller;

import com.csci.common.exception.ServiceException;
import com.csci.susdev.annotation.ExternalAuth;
import com.csci.susdev.login.LoginFacade;
import com.csci.susdev.model.ResultBase;
import com.csci.susdev.annotation.LogMethod;
import com.csci.susdev.constant.SusDevConsts;
import com.csci.susdev.model.ResultBean;
import com.csci.susdev.qo.*;
import com.csci.susdev.service.*;
import com.csci.susdev.util.context.ContextUtils;
import com.csci.susdev.util.context.model.UserInfo;
import com.csci.susdev.util.redis.RedisLockUtil;
import com.csci.susdev.vo.*;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;

import java.util.Map;

import static com.csci.susdev.service.ServiceHelper.checkExist;

@RestController
@RequestMapping(value = "/api/external", produces = "application/json")
@Tag(name = "第三方 接口展示", description = "用于接口调试")
@LogMethod
public class ExternalController {
    @Autowired
    HttpServletRequest request;

    @Resource
    private ConnectionConfigService connectionConfigService;

    @Resource
    private UserService userService;

    @Resource
    private LoginFacade loginFacade;

    @Resource
    private AmbientExtService ambientExtService;

    @Resource
    private CeBasicInfoService ceBasicInfoService;

    @Resource
    private CeIdentificationService ceIdentificationService;

    @Resource
    private EmissionReductionService emissionReductionService;

    @Resource
    private FfCmFixedService ffCmFixedService;

    @Resource
    private FfCmMobileService ffCmMobileService;

    @Resource
    private SocialPerformanceService socialPerformanceService;

    @Resource
    private SocialPerfTwoHeadService socialPerfTwoHeadService;

    @Resource
    private SocialPerfThreeHeadService socialPerfThreeHeadService;


    @ExternalAuth
    @PostMapping("/v1/login")
    @Operation(description = "第三方應用登录")
    ResultBean<Map<String, Object>> login() throws Exception {
        return loginFacade.externalAppLogin(request.getHeader(SusDevConsts.External_App_Id),
                request.getHeader(SusDevConsts.External_App_Key));
    }

    @PostMapping("/v1/get/ambient")
    @Operation(description = "獲取已提交的環境積效數據")
    public ResultBean<AmbientHeadExtVO> getAmbient(@RequestBody AmbientExtQO qo) {
        UserInfo user = ContextUtils.getCurrentUser();
        if (!ServiceHelper.isUserOrganizationExist(user.getId(), qo.getOrganizationId())) {
            throw new ServiceException("組織不存在，或是用戶沒有該組織的權限");
        }
        AmbientHeadExtVO ambientHeadExtVO = ambientExtService.findAmbientHeadExtVO(qo.getOrganizationId(), qo.getYear(), qo.getMonth());
        return new ResultBean<>(ambientHeadExtVO);
    }

    @PostMapping("/v1/update/ambient")
    @Operation(description = "提交環境積效數據")
    public ResultBase updateAmbient(@RequestBody AmbientHeadExtVO vo) {
        UserInfo user = ContextUtils.getCurrentUser();
        if (!ServiceHelper.isUserOrganizationExist(user.getId(), vo.getOrganizationId())) {
            throw new ServiceException("組織不存在，或是用戶沒有該組織的權限");
        }
        ambientExtService.insertAmbient(vo);
        return new ResultBase();
    }

    @PostMapping("/v1/updateCeBasicInfo")
    @Operation(description = "更新主表單")
    public ResultBase updateCeBasicInfo(@RequestBody CeBasicInfoHeadVO vo) {
        //TODO: 權限限制(組織)
        CeBasicInfoQO qo = new CeBasicInfoQO();
        qo.setOrganizationId(vo.getOrganizationId());
        qo.setYear(vo.getYear());
        qo.setMonth(vo.getMonth());
        //加锁
        String key = SusDevConsts.PROJECT_PREFIX + "CeBasicInfoHead:getOrInit"  + "-" + qo.getOrganizationId()  + "-" + qo.getYear() + "-" + qo.getMonth();
        boolean getLock = RedisLockUtil.lock(key);
        if (!getLock) {
            throw new ServiceException("获取锁失败，请稍后再尝试！");
        }
        try {
            ceBasicInfoService.getOrInit(qo);
            ceBasicInfoService.updateCeBasicInfoWithDetail(vo);
            return new ResultBase();
        } finally {
            //解锁
            RedisLockUtil.unlock(key);
        }
    }
    @PostMapping("/v1/updateCeIdentification")
    @Operation(description = "更新主表單")
    public ResultBase updateCeIdentification(@RequestBody CeIdentificationHeadVO vo) {
        //TODO: 權限限制(組織)
        CeIdentificationQO qo = new CeIdentificationQO();
        qo.setOrganizationId(vo.getOrganizationId());
        qo.setYear(vo.getYear());
        qo.setMonth(vo.getMonth());
        //加锁
        String key = SusDevConsts.PROJECT_PREFIX + "CeIdentificationHead:getOrInit"  + "-" + qo.getOrganizationId()  + "-" + qo.getYear() + "-" + qo.getMonth();
        boolean getLock = RedisLockUtil.lock(key);
        if (!getLock) {
            throw new ServiceException("获取锁失败，请稍后再尝试！");
        }
        try {
            ceIdentificationService.getOrInit(qo);
            ceIdentificationService.updateCeIdentificationWithDetail(vo);
            return new ResultBase();
        } finally {
            //解锁
            RedisLockUtil.unlock(key);
        }

    }
    @PostMapping("/v1/updateEmissionReduction")
    @Operation(description = "更新主表單")
    public ResultBase updateEmissionReduction(@RequestBody EmissionReductionHeadVO vo) {
        //TODO: 權限限制(組織)
        EmissionReductionQO qo = new EmissionReductionQO();
        qo.setOrganizationId(vo.getOrganizationId());
        qo.setYear(vo.getYear());
        qo.setMonth(vo.getMonth());
        //加锁
        String key = SusDevConsts.PROJECT_PREFIX + "EmissionReductionHead:getOrInit"  + "-" + qo.getOrganizationId()  + "-" + qo.getYear() + "-" + qo.getMonth();
        boolean getLock = RedisLockUtil.lock(key);
        if (!getLock) {
            throw new ServiceException("获取锁失败，请稍后再尝试！");
        }
        try {
            emissionReductionService.getOrInit(qo);
            emissionReductionService.updateEmissionReductionWithDetail(vo);
            return new ResultBase();
        } finally {
            //解锁
            RedisLockUtil.unlock(key);
        }
    }
    @PostMapping("/v1/updateFfCmFixed")
    @Operation(description = "更新主表單")
    public ResultBase updateFfCmFixed(@RequestBody FfCmFixedHeadVO vo) {
        //TODO: 權限限制(組織)
        FfCmFixedQO qo = new FfCmFixedQO();
        qo.setOrganizationId(vo.getOrganizationId());
        qo.setYear(vo.getYear());
        qo.setMonth(vo.getMonth());
        //加锁
        String key = SusDevConsts.PROJECT_PREFIX + "FfCmFixedHead:getOrInit"  + "-" + qo.getOrganizationId()  + "-" + qo.getYear() + "-" + qo.getMonth();
        boolean getLock = RedisLockUtil.lock(key);
        if (!getLock) {
            throw new ServiceException("获取锁失败，请稍后再尝试！");
        }
        try {
            ffCmFixedService.getOrInit(qo);
            ffCmFixedService.updateFfCmFixedWithDetail(vo);
            return new ResultBase();
        } finally {
            RedisLockUtil.unlock(key);
        }
    }
    @PostMapping("/v1/updateFfCmMobile")
    @Operation(description = "更新主表單")
    public ResultBase updateFfCmMobile(@RequestBody FfCmMobileHeadVO vo) {
        //TODO: 權限限制(組織)
        FfCmMobileQO qo = new FfCmMobileQO();
        qo.setOrganizationId(vo.getOrganizationId());
        qo.setYear(vo.getYear());
        qo.setMonth(vo.getMonth());
        //加锁
        String key = SusDevConsts.PROJECT_PREFIX + "FfCmMobileHead:getOrInit"  + "-" + qo.getOrganizationId()  + "-" + qo.getYear() + "-" + qo.getMonth();
        boolean getLock = RedisLockUtil.lock(key);
        if (!getLock) {
            throw new ServiceException("获取锁失败，请稍后再尝试！");
        }
        try {
            ffCmMobileService.getOrInit(qo);
            ffCmMobileService.updateFfCmMobileWithDetail(vo);
            return new ResultBase();
        } finally {
            RedisLockUtil.unlock(key);
        }
    }
    @PostMapping("/v1/updateSocialPerformance")
    @Operation(description = "更新主表單")
    public ResultBase updateSocialPerformance(@RequestBody SocialPerformanceHeadVO vo) {
        //TODO: 權限限制(組織)
        SocialPerformanceQO qo = new SocialPerformanceQO();
        qo.setOrganizationId(vo.getOrganizationId());
        qo.setYear(vo.getYear());
        qo.setMonth(vo.getMonth());
        //加锁
        String key = SusDevConsts.PROJECT_PREFIX + "SocialPerformanceHead:getOrInit"  + "-" + qo.getOrganizationId()  + "-" + qo.getYear() + "-" + qo.getMonth();
        boolean getLock = RedisLockUtil.lock(key);
        if (!getLock) {
            throw new ServiceException("获取锁失败，请稍后再尝试！");
        }
        try {
            socialPerformanceService.getOrInit(qo);
            socialPerformanceService.updateSocialPerfWithDetail(vo);
            return new ResultBase();
        } finally {
            RedisLockUtil.unlock(key);
        }
    }
    @PostMapping("/v1/updateSocialPerfTwo")
    @Operation(description = "更新主表單")
    public ResultBase updateSocialPerfTwo(@RequestBody SocialPerfTwoHeadVO vo) {
        //TODO: 權限限制(組織)
        //加锁
        String key = SusDevConsts.PROJECT_PREFIX + "SocialPerfTwoHead:getOrInit"  + "-" + vo.getOrganizationId()  + "-" + vo.getYear() + "-" + vo.getMonth();
        boolean getLock = RedisLockUtil.lock(key);
        if (!getLock) {
            throw new ServiceException("获取锁失败，请稍后再尝试！");
        }
        try {
            socialPerfTwoHeadService.getOrInitSocialPerfTwo(vo.getOrganizationId(), vo.getYear(), vo.getMonth());
            socialPerfTwoHeadService.updateSocialPerfTwo(vo);
            return new ResultBase();
        } finally {
            RedisLockUtil.unlock(key);
        }
    }
    @PostMapping("/v1/updateSocialPerfThree")
    @Operation(description = "更新社會積效三")
    public ResultBase updateSocialPerfThree(@RequestBody SocialPerfThreeHeadVO vo) {
        //TODO: 權限限制(組織)
        //加锁
        String key = SusDevConsts.PROJECT_PREFIX + "SocialPerfThreeHead:getOrInit"  + "-" + vo.getOrganizationId()  + "-" + vo.getYear() + "-" + vo.getMonth();
        boolean getLock = RedisLockUtil.lock(key);
        if (!getLock) {
            throw new ServiceException("获取锁失败，请稍后再尝试！");
        }
        try {
            socialPerfThreeHeadService.getOrInitSocialPerfThree(vo.getOrganizationId(), vo.getYear(), vo.getMonth());
            socialPerfThreeHeadService.updateSocialPerfThree(vo);
            return new ResultBase();
        } finally {
            RedisLockUtil.unlock(key);
        }
    }
}

