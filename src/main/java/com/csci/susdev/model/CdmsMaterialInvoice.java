package com.csci.susdev.model;

public class CdmsMaterialInvoice {

	/**
	 * This field was generated by MyBatis Generator. This field corresponds to the database column t_cdms_material_invoice.id
	 * @mbg.generated  Fri Jan 05 15:59:13 HKT 2024
	 */
	private String id;
	/**
	 * This field was generated by MyBatis Generator. This field corresponds to the database column t_cdms_material_invoice.organization_id
	 * @mbg.generated  Fri Jan 05 15:59:13 HKT 2024
	 */
	private String organizationId;
	/**
	 * This field was generated by MyBatis Generator. This field corresponds to the database column t_cdms_material_invoice.organization_name
	 * @mbg.generated  Fri Jan 05 15:59:13 HKT 2024
	 */
	private String organizationName;
	/**
	 * This field was generated by MyBatis Generator. This field corresponds to the database column t_cdms_material_invoice.record_year_month
	 * @mbg.generated  Fri Jan 05 15:59:13 HKT 2024
	 */
	private Integer recordYearMonth;
	/**
	 * This field was generated by MyBatis Generator. This field corresponds to the database column t_cdms_material_invoice.carbon_emission_location
	 * @mbg.generated  Fri Jan 05 15:59:13 HKT 2024
	 */
	private String carbonEmissionLocation;
	/**
	 * This field was generated by MyBatis Generator. This field corresponds to the database column t_cdms_material_invoice.material_code
	 * @mbg.generated  Fri Jan 05 15:59:13 HKT 2024
	 */
	private String materialCode;
	/**
	 * This field was generated by MyBatis Generator. This field corresponds to the database column t_cdms_material_invoice.classification
	 * @mbg.generated  Fri Jan 05 15:59:13 HKT 2024
	 */
	private String classification;
	/**
	 * This field was generated by MyBatis Generator. This field corresponds to the database column t_cdms_material_invoice.description
	 * @mbg.generated  Fri Jan 05 15:59:13 HKT 2024
	 */
	private String description;
	/**
	 * This field was generated by MyBatis Generator. This field corresponds to the database column t_cdms_material_invoice.pur_invoice_id
	 * @mbg.generated  Fri Jan 05 15:59:13 HKT 2024
	 */
	private String purInvoiceId;
	/**
	 * This field was generated by MyBatis Generator. This field corresponds to the database column t_cdms_material_invoice.invoice_file
	 * @mbg.generated  Fri Jan 05 15:59:13 HKT 2024
	 */
	private String invoiceFile;

	/**
	 * This method was generated by MyBatis Generator. This method returns the value of the database column t_cdms_material_invoice.id
	 * @return  the value of t_cdms_material_invoice.id
	 * @mbg.generated  Fri Jan 05 15:59:13 HKT 2024
	 */
	public String getId() {
		return id;
	}

	/**
	 * This method was generated by MyBatis Generator. This method sets the value of the database column t_cdms_material_invoice.id
	 * @param id  the value for t_cdms_material_invoice.id
	 * @mbg.generated  Fri Jan 05 15:59:13 HKT 2024
	 */
	public void setId(String id) {
		this.id = id == null ? null : id.trim();
	}

	/**
	 * This method was generated by MyBatis Generator. This method returns the value of the database column t_cdms_material_invoice.organization_id
	 * @return  the value of t_cdms_material_invoice.organization_id
	 * @mbg.generated  Fri Jan 05 15:59:13 HKT 2024
	 */
	public String getOrganizationId() {
		return organizationId;
	}

	/**
	 * This method was generated by MyBatis Generator. This method sets the value of the database column t_cdms_material_invoice.organization_id
	 * @param organizationId  the value for t_cdms_material_invoice.organization_id
	 * @mbg.generated  Fri Jan 05 15:59:13 HKT 2024
	 */
	public void setOrganizationId(String organizationId) {
		this.organizationId = organizationId == null ? null : organizationId.trim();
	}

	/**
	 * This method was generated by MyBatis Generator. This method returns the value of the database column t_cdms_material_invoice.organization_name
	 * @return  the value of t_cdms_material_invoice.organization_name
	 * @mbg.generated  Fri Jan 05 15:59:13 HKT 2024
	 */
	public String getOrganizationName() {
		return organizationName;
	}

	/**
	 * This method was generated by MyBatis Generator. This method sets the value of the database column t_cdms_material_invoice.organization_name
	 * @param organizationName  the value for t_cdms_material_invoice.organization_name
	 * @mbg.generated  Fri Jan 05 15:59:13 HKT 2024
	 */
	public void setOrganizationName(String organizationName) {
		this.organizationName = organizationName == null ? null : organizationName.trim();
	}

	/**
	 * This method was generated by MyBatis Generator. This method returns the value of the database column t_cdms_material_invoice.record_year_month
	 * @return  the value of t_cdms_material_invoice.record_year_month
	 * @mbg.generated  Fri Jan 05 15:59:13 HKT 2024
	 */
	public Integer getRecordYearMonth() {
		return recordYearMonth;
	}

	/**
	 * This method was generated by MyBatis Generator. This method sets the value of the database column t_cdms_material_invoice.record_year_month
	 * @param recordYearMonth  the value for t_cdms_material_invoice.record_year_month
	 * @mbg.generated  Fri Jan 05 15:59:13 HKT 2024
	 */
	public void setRecordYearMonth(Integer recordYearMonth) {
		this.recordYearMonth = recordYearMonth;
	}

	/**
	 * This method was generated by MyBatis Generator. This method returns the value of the database column t_cdms_material_invoice.carbon_emission_location
	 * @return  the value of t_cdms_material_invoice.carbon_emission_location
	 * @mbg.generated  Fri Jan 05 15:59:13 HKT 2024
	 */
	public String getCarbonEmissionLocation() {
		return carbonEmissionLocation;
	}

	/**
	 * This method was generated by MyBatis Generator. This method sets the value of the database column t_cdms_material_invoice.carbon_emission_location
	 * @param carbonEmissionLocation  the value for t_cdms_material_invoice.carbon_emission_location
	 * @mbg.generated  Fri Jan 05 15:59:13 HKT 2024
	 */
	public void setCarbonEmissionLocation(String carbonEmissionLocation) {
		this.carbonEmissionLocation = carbonEmissionLocation == null ? null : carbonEmissionLocation.trim();
	}

	/**
	 * This method was generated by MyBatis Generator. This method returns the value of the database column t_cdms_material_invoice.material_code
	 * @return  the value of t_cdms_material_invoice.material_code
	 * @mbg.generated  Fri Jan 05 15:59:13 HKT 2024
	 */
	public String getMaterialCode() {
		return materialCode;
	}

	/**
	 * This method was generated by MyBatis Generator. This method sets the value of the database column t_cdms_material_invoice.material_code
	 * @param materialCode  the value for t_cdms_material_invoice.material_code
	 * @mbg.generated  Fri Jan 05 15:59:13 HKT 2024
	 */
	public void setMaterialCode(String materialCode) {
		this.materialCode = materialCode == null ? null : materialCode.trim();
	}

	/**
	 * This method was generated by MyBatis Generator. This method returns the value of the database column t_cdms_material_invoice.classification
	 * @return  the value of t_cdms_material_invoice.classification
	 * @mbg.generated  Fri Jan 05 15:59:13 HKT 2024
	 */
	public String getClassification() {
		return classification;
	}

	/**
	 * This method was generated by MyBatis Generator. This method sets the value of the database column t_cdms_material_invoice.classification
	 * @param classification  the value for t_cdms_material_invoice.classification
	 * @mbg.generated  Fri Jan 05 15:59:13 HKT 2024
	 */
	public void setClassification(String classification) {
		this.classification = classification == null ? null : classification.trim();
	}

	/**
	 * This method was generated by MyBatis Generator. This method returns the value of the database column t_cdms_material_invoice.description
	 * @return  the value of t_cdms_material_invoice.description
	 * @mbg.generated  Fri Jan 05 15:59:13 HKT 2024
	 */
	public String getDescription() {
		return description;
	}

	/**
	 * This method was generated by MyBatis Generator. This method sets the value of the database column t_cdms_material_invoice.description
	 * @param description  the value for t_cdms_material_invoice.description
	 * @mbg.generated  Fri Jan 05 15:59:13 HKT 2024
	 */
	public void setDescription(String description) {
		this.description = description == null ? null : description.trim();
	}

	/**
	 * This method was generated by MyBatis Generator. This method returns the value of the database column t_cdms_material_invoice.pur_invoice_id
	 * @return  the value of t_cdms_material_invoice.pur_invoice_id
	 * @mbg.generated  Fri Jan 05 15:59:13 HKT 2024
	 */
	public String getPurInvoiceId() {
		return purInvoiceId;
	}

	/**
	 * This method was generated by MyBatis Generator. This method sets the value of the database column t_cdms_material_invoice.pur_invoice_id
	 * @param purInvoiceId  the value for t_cdms_material_invoice.pur_invoice_id
	 * @mbg.generated  Fri Jan 05 15:59:13 HKT 2024
	 */
	public void setPurInvoiceId(String purInvoiceId) {
		this.purInvoiceId = purInvoiceId == null ? null : purInvoiceId.trim();
	}

	/**
	 * This method was generated by MyBatis Generator. This method returns the value of the database column t_cdms_material_invoice.invoice_file
	 * @return  the value of t_cdms_material_invoice.invoice_file
	 * @mbg.generated  Fri Jan 05 15:59:13 HKT 2024
	 */
	public String getInvoiceFile() {
		return invoiceFile;
	}

	/**
	 * This method was generated by MyBatis Generator. This method sets the value of the database column t_cdms_material_invoice.invoice_file
	 * @param invoiceFile  the value for t_cdms_material_invoice.invoice_file
	 * @mbg.generated  Fri Jan 05 15:59:13 HKT 2024
	 */
	public void setInvoiceFile(String invoiceFile) {
		this.invoiceFile = invoiceFile == null ? null : invoiceFile.trim();
	}
}