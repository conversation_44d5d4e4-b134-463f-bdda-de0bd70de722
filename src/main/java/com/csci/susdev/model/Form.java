package com.csci.susdev.model;

import java.time.LocalDateTime;

public class Form {
    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_form.id
     *
     * @mbg.generated Fri Apr 12 15:51:26 HKT 2024
     */
    private String id;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_form.code
     *
     * @mbg.generated Fri Apr 12 15:51:26 HKT 2024
     */
    private String code;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_form.name
     *
     * @mbg.generated Fri Apr 12 15:51:26 HKT 2024
     */
    private String name;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_form.description
     *
     * @mbg.generated Fri Apr 12 15:51:26 HKT 2024
     */
    private String description;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_form.is_factor_related
     *
     * @mbg.generated Fri Apr 12 15:51:26 HKT 2024
     */
    private Boolean isFactorRelated;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_form.creation_time
     *
     * @mbg.generated Fri Apr 12 15:51:26 HKT 2024
     */
    private LocalDateTime creationTime;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_form.create_username
     *
     * @mbg.generated Fri Apr 12 15:51:26 HKT 2024
     */
    private String createUsername;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_form.create_user_id
     *
     * @mbg.generated Fri Apr 12 15:51:26 HKT 2024
     */
    private String createUserId;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_form.last_update_time
     *
     * @mbg.generated Fri Apr 12 15:51:26 HKT 2024
     */
    private LocalDateTime lastUpdateTime;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_form.last_update_username
     *
     * @mbg.generated Fri Apr 12 15:51:26 HKT 2024
     */
    private String lastUpdateUsername;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_form.last_update_user_id
     *
     * @mbg.generated Fri Apr 12 15:51:26 HKT 2024
     */
    private String lastUpdateUserId;

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column t_form.id
     *
     * @return the value of t_form.id
     *
     * @mbg.generated Fri Apr 12 15:51:26 HKT 2024
     */
    public String getId() {
        return id;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column t_form.id
     *
     * @param id the value for t_form.id
     *
     * @mbg.generated Fri Apr 12 15:51:26 HKT 2024
     */
    public void setId(String id) {
        this.id = id == null ? null : id.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column t_form.code
     *
     * @return the value of t_form.code
     *
     * @mbg.generated Fri Apr 12 15:51:26 HKT 2024
     */
    public String getCode() {
        return code;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column t_form.code
     *
     * @param code the value for t_form.code
     *
     * @mbg.generated Fri Apr 12 15:51:26 HKT 2024
     */
    public void setCode(String code) {
        this.code = code == null ? null : code.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column t_form.name
     *
     * @return the value of t_form.name
     *
     * @mbg.generated Fri Apr 12 15:51:26 HKT 2024
     */
    public String getName() {
        return name;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column t_form.name
     *
     * @param name the value for t_form.name
     *
     * @mbg.generated Fri Apr 12 15:51:26 HKT 2024
     */
    public void setName(String name) {
        this.name = name == null ? null : name.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column t_form.description
     *
     * @return the value of t_form.description
     *
     * @mbg.generated Fri Apr 12 15:51:26 HKT 2024
     */
    public String getDescription() {
        return description;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column t_form.description
     *
     * @param description the value for t_form.description
     *
     * @mbg.generated Fri Apr 12 15:51:26 HKT 2024
     */
    public void setDescription(String description) {
        this.description = description == null ? null : description.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column t_form.is_factor_related
     *
     * @return the value of t_form.is_factor_related
     *
     * @mbg.generated Fri Apr 12 15:51:26 HKT 2024
     */
    public Boolean getIsFactorRelated() {
        return isFactorRelated;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column t_form.is_factor_related
     *
     * @param isFactorRelated the value for t_form.is_factor_related
     *
     * @mbg.generated Fri Apr 12 15:51:26 HKT 2024
     */
    public void setIsFactorRelated(Boolean isFactorRelated) {
        this.isFactorRelated = isFactorRelated;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column t_form.creation_time
     *
     * @return the value of t_form.creation_time
     *
     * @mbg.generated Fri Apr 12 15:51:26 HKT 2024
     */
    public LocalDateTime getCreationTime() {
        return creationTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column t_form.creation_time
     *
     * @param creationTime the value for t_form.creation_time
     *
     * @mbg.generated Fri Apr 12 15:51:26 HKT 2024
     */
    public void setCreationTime(LocalDateTime creationTime) {
        this.creationTime = creationTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column t_form.create_username
     *
     * @return the value of t_form.create_username
     *
     * @mbg.generated Fri Apr 12 15:51:26 HKT 2024
     */
    public String getCreateUsername() {
        return createUsername;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column t_form.create_username
     *
     * @param createUsername the value for t_form.create_username
     *
     * @mbg.generated Fri Apr 12 15:51:26 HKT 2024
     */
    public void setCreateUsername(String createUsername) {
        this.createUsername = createUsername == null ? null : createUsername.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column t_form.create_user_id
     *
     * @return the value of t_form.create_user_id
     *
     * @mbg.generated Fri Apr 12 15:51:26 HKT 2024
     */
    public String getCreateUserId() {
        return createUserId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column t_form.create_user_id
     *
     * @param createUserId the value for t_form.create_user_id
     *
     * @mbg.generated Fri Apr 12 15:51:26 HKT 2024
     */
    public void setCreateUserId(String createUserId) {
        this.createUserId = createUserId == null ? null : createUserId.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column t_form.last_update_time
     *
     * @return the value of t_form.last_update_time
     *
     * @mbg.generated Fri Apr 12 15:51:26 HKT 2024
     */
    public LocalDateTime getLastUpdateTime() {
        return lastUpdateTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column t_form.last_update_time
     *
     * @param lastUpdateTime the value for t_form.last_update_time
     *
     * @mbg.generated Fri Apr 12 15:51:26 HKT 2024
     */
    public void setLastUpdateTime(LocalDateTime lastUpdateTime) {
        this.lastUpdateTime = lastUpdateTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column t_form.last_update_username
     *
     * @return the value of t_form.last_update_username
     *
     * @mbg.generated Fri Apr 12 15:51:26 HKT 2024
     */
    public String getLastUpdateUsername() {
        return lastUpdateUsername;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column t_form.last_update_username
     *
     * @param lastUpdateUsername the value for t_form.last_update_username
     *
     * @mbg.generated Fri Apr 12 15:51:26 HKT 2024
     */
    public void setLastUpdateUsername(String lastUpdateUsername) {
        this.lastUpdateUsername = lastUpdateUsername == null ? null : lastUpdateUsername.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column t_form.last_update_user_id
     *
     * @return the value of t_form.last_update_user_id
     *
     * @mbg.generated Fri Apr 12 15:51:26 HKT 2024
     */
    public String getLastUpdateUserId() {
        return lastUpdateUserId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column t_form.last_update_user_id
     *
     * @param lastUpdateUserId the value for t_form.last_update_user_id
     *
     * @mbg.generated Fri Apr 12 15:51:26 HKT 2024
     */
    public void setLastUpdateUserId(String lastUpdateUserId) {
        this.lastUpdateUserId = lastUpdateUserId == null ? null : lastUpdateUserId.trim();
    }
}