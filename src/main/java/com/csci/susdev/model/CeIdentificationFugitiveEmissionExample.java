package com.csci.susdev.model;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;

public class CeIdentificationFugitiveEmissionExample {
    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database table t_ce_identification_fugitive_emission
     *
     * @mbg.generated Wed Apr 02 16:23:01 CST 2025
     */
    protected String orderByClause;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database table t_ce_identification_fugitive_emission
     *
     * @mbg.generated Wed Apr 02 16:23:01 CST 2025
     */
    protected boolean distinct;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database table t_ce_identification_fugitive_emission
     *
     * @mbg.generated Wed Apr 02 16:23:01 CST 2025
     */
    protected List<Criteria> oredCriteria;

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table t_ce_identification_fugitive_emission
     *
     * @mbg.generated Wed Apr 02 16:23:01 CST 2025
     */
    public CeIdentificationFugitiveEmissionExample() {
        oredCriteria = new ArrayList<>();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table t_ce_identification_fugitive_emission
     *
     * @mbg.generated Wed Apr 02 16:23:01 CST 2025
     */
    public void setOrderByClause(String orderByClause) {
        this.orderByClause = orderByClause;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table t_ce_identification_fugitive_emission
     *
     * @mbg.generated Wed Apr 02 16:23:01 CST 2025
     */
    public String getOrderByClause() {
        return orderByClause;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table t_ce_identification_fugitive_emission
     *
     * @mbg.generated Wed Apr 02 16:23:01 CST 2025
     */
    public void setDistinct(boolean distinct) {
        this.distinct = distinct;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table t_ce_identification_fugitive_emission
     *
     * @mbg.generated Wed Apr 02 16:23:01 CST 2025
     */
    public boolean isDistinct() {
        return distinct;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table t_ce_identification_fugitive_emission
     *
     * @mbg.generated Wed Apr 02 16:23:01 CST 2025
     */
    public List<Criteria> getOredCriteria() {
        return oredCriteria;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table t_ce_identification_fugitive_emission
     *
     * @mbg.generated Wed Apr 02 16:23:01 CST 2025
     */
    public void or(Criteria criteria) {
        oredCriteria.add(criteria);
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table t_ce_identification_fugitive_emission
     *
     * @mbg.generated Wed Apr 02 16:23:01 CST 2025
     */
    public Criteria or() {
        Criteria criteria = createCriteriaInternal();
        oredCriteria.add(criteria);
        return criteria;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table t_ce_identification_fugitive_emission
     *
     * @mbg.generated Wed Apr 02 16:23:01 CST 2025
     */
    public Criteria createCriteria() {
        Criteria criteria = createCriteriaInternal();
        if (oredCriteria.size() == 0) {
            oredCriteria.add(criteria);
        }
        return criteria;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table t_ce_identification_fugitive_emission
     *
     * @mbg.generated Wed Apr 02 16:23:01 CST 2025
     */
    protected Criteria createCriteriaInternal() {
        Criteria criteria = new Criteria();
        return criteria;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table t_ce_identification_fugitive_emission
     *
     * @mbg.generated Wed Apr 02 16:23:01 CST 2025
     */
    public void clear() {
        oredCriteria.clear();
        orderByClause = null;
        distinct = false;
    }

    /**
     * This class was generated by MyBatis Generator.
     * This class corresponds to the database table t_ce_identification_fugitive_emission
     *
     * @mbg.generated Wed Apr 02 16:23:01 CST 2025
     */
    protected abstract static class GeneratedCriteria {
        protected List<Criterion> criteria;

        protected GeneratedCriteria() {
            super();
            criteria = new ArrayList<>();
        }

        public boolean isValid() {
            return criteria.size() > 0;
        }

        public List<Criterion> getAllCriteria() {
            return criteria;
        }

        public List<Criterion> getCriteria() {
            return criteria;
        }

        protected void addCriterion(String condition) {
            if (condition == null) {
                throw new RuntimeException("Value for condition cannot be null");
            }
            criteria.add(new Criterion(condition));
        }

        protected void addCriterion(String condition, Object value, String property) {
            if (value == null) {
                throw new RuntimeException("Value for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value));
        }

        protected void addCriterion(String condition, Object value1, Object value2, String property) {
            if (value1 == null || value2 == null) {
                throw new RuntimeException("Between values for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value1, value2));
        }

        public Criteria andIdIsNull() {
            addCriterion("id is null");
            return (Criteria) this;
        }

        public Criteria andIdIsNotNull() {
            addCriterion("id is not null");
            return (Criteria) this;
        }

        public Criteria andIdEqualTo(String value) {
            addCriterion("id =", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotEqualTo(String value) {
            addCriterion("id <>", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThan(String value) {
            addCriterion("id >", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThanOrEqualTo(String value) {
            addCriterion("id >=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThan(String value) {
            addCriterion("id <", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThanOrEqualTo(String value) {
            addCriterion("id <=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLike(String value) {
            addCriterion("id like", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotLike(String value) {
            addCriterion("id not like", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdIn(List<String> values) {
            addCriterion("id in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotIn(List<String> values) {
            addCriterion("id not in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdBetween(String value1, String value2) {
            addCriterion("id between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotBetween(String value1, String value2) {
            addCriterion("id not between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andCeIdentificationHeadIdIsNull() {
            addCriterion("ce_identification_head_id is null");
            return (Criteria) this;
        }

        public Criteria andCeIdentificationHeadIdIsNotNull() {
            addCriterion("ce_identification_head_id is not null");
            return (Criteria) this;
        }

        public Criteria andCeIdentificationHeadIdEqualTo(String value) {
            addCriterion("ce_identification_head_id =", value, "ceIdentificationHeadId");
            return (Criteria) this;
        }

        public Criteria andCeIdentificationHeadIdNotEqualTo(String value) {
            addCriterion("ce_identification_head_id <>", value, "ceIdentificationHeadId");
            return (Criteria) this;
        }

        public Criteria andCeIdentificationHeadIdGreaterThan(String value) {
            addCriterion("ce_identification_head_id >", value, "ceIdentificationHeadId");
            return (Criteria) this;
        }

        public Criteria andCeIdentificationHeadIdGreaterThanOrEqualTo(String value) {
            addCriterion("ce_identification_head_id >=", value, "ceIdentificationHeadId");
            return (Criteria) this;
        }

        public Criteria andCeIdentificationHeadIdLessThan(String value) {
            addCriterion("ce_identification_head_id <", value, "ceIdentificationHeadId");
            return (Criteria) this;
        }

        public Criteria andCeIdentificationHeadIdLessThanOrEqualTo(String value) {
            addCriterion("ce_identification_head_id <=", value, "ceIdentificationHeadId");
            return (Criteria) this;
        }

        public Criteria andCeIdentificationHeadIdLike(String value) {
            addCriterion("ce_identification_head_id like", value, "ceIdentificationHeadId");
            return (Criteria) this;
        }

        public Criteria andCeIdentificationHeadIdNotLike(String value) {
            addCriterion("ce_identification_head_id not like", value, "ceIdentificationHeadId");
            return (Criteria) this;
        }

        public Criteria andCeIdentificationHeadIdIn(List<String> values) {
            addCriterion("ce_identification_head_id in", values, "ceIdentificationHeadId");
            return (Criteria) this;
        }

        public Criteria andCeIdentificationHeadIdNotIn(List<String> values) {
            addCriterion("ce_identification_head_id not in", values, "ceIdentificationHeadId");
            return (Criteria) this;
        }

        public Criteria andCeIdentificationHeadIdBetween(String value1, String value2) {
            addCriterion("ce_identification_head_id between", value1, value2, "ceIdentificationHeadId");
            return (Criteria) this;
        }

        public Criteria andCeIdentificationHeadIdNotBetween(String value1, String value2) {
            addCriterion("ce_identification_head_id not between", value1, value2, "ceIdentificationHeadId");
            return (Criteria) this;
        }

        public Criteria andFugitiveModuleIsNull() {
            addCriterion("fugitive_module is null");
            return (Criteria) this;
        }

        public Criteria andFugitiveModuleIsNotNull() {
            addCriterion("fugitive_module is not null");
            return (Criteria) this;
        }

        public Criteria andFugitiveModuleEqualTo(String value) {
            addCriterion("fugitive_module =", value, "fugitiveModule");
            return (Criteria) this;
        }

        public Criteria andFugitiveModuleNotEqualTo(String value) {
            addCriterion("fugitive_module <>", value, "fugitiveModule");
            return (Criteria) this;
        }

        public Criteria andFugitiveModuleGreaterThan(String value) {
            addCriterion("fugitive_module >", value, "fugitiveModule");
            return (Criteria) this;
        }

        public Criteria andFugitiveModuleGreaterThanOrEqualTo(String value) {
            addCriterion("fugitive_module >=", value, "fugitiveModule");
            return (Criteria) this;
        }

        public Criteria andFugitiveModuleLessThan(String value) {
            addCriterion("fugitive_module <", value, "fugitiveModule");
            return (Criteria) this;
        }

        public Criteria andFugitiveModuleLessThanOrEqualTo(String value) {
            addCriterion("fugitive_module <=", value, "fugitiveModule");
            return (Criteria) this;
        }

        public Criteria andFugitiveModuleLike(String value) {
            addCriterion("fugitive_module like", value, "fugitiveModule");
            return (Criteria) this;
        }

        public Criteria andFugitiveModuleNotLike(String value) {
            addCriterion("fugitive_module not like", value, "fugitiveModule");
            return (Criteria) this;
        }

        public Criteria andFugitiveModuleIn(List<String> values) {
            addCriterion("fugitive_module in", values, "fugitiveModule");
            return (Criteria) this;
        }

        public Criteria andFugitiveModuleNotIn(List<String> values) {
            addCriterion("fugitive_module not in", values, "fugitiveModule");
            return (Criteria) this;
        }

        public Criteria andFugitiveModuleBetween(String value1, String value2) {
            addCriterion("fugitive_module between", value1, value2, "fugitiveModule");
            return (Criteria) this;
        }

        public Criteria andFugitiveModuleNotBetween(String value1, String value2) {
            addCriterion("fugitive_module not between", value1, value2, "fugitiveModule");
            return (Criteria) this;
        }

        public Criteria andConfirmationItemOneIsNull() {
            addCriterion("confirmation_item_one is null");
            return (Criteria) this;
        }

        public Criteria andConfirmationItemOneIsNotNull() {
            addCriterion("confirmation_item_one is not null");
            return (Criteria) this;
        }

        public Criteria andConfirmationItemOneEqualTo(String value) {
            addCriterion("confirmation_item_one =", value, "confirmationItemOne");
            return (Criteria) this;
        }

        public Criteria andConfirmationItemOneNotEqualTo(String value) {
            addCriterion("confirmation_item_one <>", value, "confirmationItemOne");
            return (Criteria) this;
        }

        public Criteria andConfirmationItemOneGreaterThan(String value) {
            addCriterion("confirmation_item_one >", value, "confirmationItemOne");
            return (Criteria) this;
        }

        public Criteria andConfirmationItemOneGreaterThanOrEqualTo(String value) {
            addCriterion("confirmation_item_one >=", value, "confirmationItemOne");
            return (Criteria) this;
        }

        public Criteria andConfirmationItemOneLessThan(String value) {
            addCriterion("confirmation_item_one <", value, "confirmationItemOne");
            return (Criteria) this;
        }

        public Criteria andConfirmationItemOneLessThanOrEqualTo(String value) {
            addCriterion("confirmation_item_one <=", value, "confirmationItemOne");
            return (Criteria) this;
        }

        public Criteria andConfirmationItemOneLike(String value) {
            addCriterion("confirmation_item_one like", value, "confirmationItemOne");
            return (Criteria) this;
        }

        public Criteria andConfirmationItemOneNotLike(String value) {
            addCriterion("confirmation_item_one not like", value, "confirmationItemOne");
            return (Criteria) this;
        }

        public Criteria andConfirmationItemOneIn(List<String> values) {
            addCriterion("confirmation_item_one in", values, "confirmationItemOne");
            return (Criteria) this;
        }

        public Criteria andConfirmationItemOneNotIn(List<String> values) {
            addCriterion("confirmation_item_one not in", values, "confirmationItemOne");
            return (Criteria) this;
        }

        public Criteria andConfirmationItemOneBetween(String value1, String value2) {
            addCriterion("confirmation_item_one between", value1, value2, "confirmationItemOne");
            return (Criteria) this;
        }

        public Criteria andConfirmationItemOneNotBetween(String value1, String value2) {
            addCriterion("confirmation_item_one not between", value1, value2, "confirmationItemOne");
            return (Criteria) this;
        }

        public Criteria andConfirmationItemTwoIsNull() {
            addCriterion("confirmation_item_two is null");
            return (Criteria) this;
        }

        public Criteria andConfirmationItemTwoIsNotNull() {
            addCriterion("confirmation_item_two is not null");
            return (Criteria) this;
        }

        public Criteria andConfirmationItemTwoEqualTo(String value) {
            addCriterion("confirmation_item_two =", value, "confirmationItemTwo");
            return (Criteria) this;
        }

        public Criteria andConfirmationItemTwoNotEqualTo(String value) {
            addCriterion("confirmation_item_two <>", value, "confirmationItemTwo");
            return (Criteria) this;
        }

        public Criteria andConfirmationItemTwoGreaterThan(String value) {
            addCriterion("confirmation_item_two >", value, "confirmationItemTwo");
            return (Criteria) this;
        }

        public Criteria andConfirmationItemTwoGreaterThanOrEqualTo(String value) {
            addCriterion("confirmation_item_two >=", value, "confirmationItemTwo");
            return (Criteria) this;
        }

        public Criteria andConfirmationItemTwoLessThan(String value) {
            addCriterion("confirmation_item_two <", value, "confirmationItemTwo");
            return (Criteria) this;
        }

        public Criteria andConfirmationItemTwoLessThanOrEqualTo(String value) {
            addCriterion("confirmation_item_two <=", value, "confirmationItemTwo");
            return (Criteria) this;
        }

        public Criteria andConfirmationItemTwoLike(String value) {
            addCriterion("confirmation_item_two like", value, "confirmationItemTwo");
            return (Criteria) this;
        }

        public Criteria andConfirmationItemTwoNotLike(String value) {
            addCriterion("confirmation_item_two not like", value, "confirmationItemTwo");
            return (Criteria) this;
        }

        public Criteria andConfirmationItemTwoIn(List<String> values) {
            addCriterion("confirmation_item_two in", values, "confirmationItemTwo");
            return (Criteria) this;
        }

        public Criteria andConfirmationItemTwoNotIn(List<String> values) {
            addCriterion("confirmation_item_two not in", values, "confirmationItemTwo");
            return (Criteria) this;
        }

        public Criteria andConfirmationItemTwoBetween(String value1, String value2) {
            addCriterion("confirmation_item_two between", value1, value2, "confirmationItemTwo");
            return (Criteria) this;
        }

        public Criteria andConfirmationItemTwoNotBetween(String value1, String value2) {
            addCriterion("confirmation_item_two not between", value1, value2, "confirmationItemTwo");
            return (Criteria) this;
        }

        public Criteria andConfirmationItemThreeIsNull() {
            addCriterion("confirmation_item_three is null");
            return (Criteria) this;
        }

        public Criteria andConfirmationItemThreeIsNotNull() {
            addCriterion("confirmation_item_three is not null");
            return (Criteria) this;
        }

        public Criteria andConfirmationItemThreeEqualTo(String value) {
            addCriterion("confirmation_item_three =", value, "confirmationItemThree");
            return (Criteria) this;
        }

        public Criteria andConfirmationItemThreeNotEqualTo(String value) {
            addCriterion("confirmation_item_three <>", value, "confirmationItemThree");
            return (Criteria) this;
        }

        public Criteria andConfirmationItemThreeGreaterThan(String value) {
            addCriterion("confirmation_item_three >", value, "confirmationItemThree");
            return (Criteria) this;
        }

        public Criteria andConfirmationItemThreeGreaterThanOrEqualTo(String value) {
            addCriterion("confirmation_item_three >=", value, "confirmationItemThree");
            return (Criteria) this;
        }

        public Criteria andConfirmationItemThreeLessThan(String value) {
            addCriterion("confirmation_item_three <", value, "confirmationItemThree");
            return (Criteria) this;
        }

        public Criteria andConfirmationItemThreeLessThanOrEqualTo(String value) {
            addCriterion("confirmation_item_three <=", value, "confirmationItemThree");
            return (Criteria) this;
        }

        public Criteria andConfirmationItemThreeLike(String value) {
            addCriterion("confirmation_item_three like", value, "confirmationItemThree");
            return (Criteria) this;
        }

        public Criteria andConfirmationItemThreeNotLike(String value) {
            addCriterion("confirmation_item_three not like", value, "confirmationItemThree");
            return (Criteria) this;
        }

        public Criteria andConfirmationItemThreeIn(List<String> values) {
            addCriterion("confirmation_item_three in", values, "confirmationItemThree");
            return (Criteria) this;
        }

        public Criteria andConfirmationItemThreeNotIn(List<String> values) {
            addCriterion("confirmation_item_three not in", values, "confirmationItemThree");
            return (Criteria) this;
        }

        public Criteria andConfirmationItemThreeBetween(String value1, String value2) {
            addCriterion("confirmation_item_three between", value1, value2, "confirmationItemThree");
            return (Criteria) this;
        }

        public Criteria andConfirmationItemThreeNotBetween(String value1, String value2) {
            addCriterion("confirmation_item_three not between", value1, value2, "confirmationItemThree");
            return (Criteria) this;
        }

        public Criteria andInstructionsIsNull() {
            addCriterion("\"instructions \" is null");
            return (Criteria) this;
        }

        public Criteria andInstructionsIsNotNull() {
            addCriterion("\"instructions \" is not null");
            return (Criteria) this;
        }

        public Criteria andInstructionsEqualTo(String value) {
            addCriterion("\"instructions \" =", value, "instructions");
            return (Criteria) this;
        }

        public Criteria andInstructionsNotEqualTo(String value) {
            addCriterion("\"instructions \" <>", value, "instructions");
            return (Criteria) this;
        }

        public Criteria andInstructionsGreaterThan(String value) {
            addCriterion("\"instructions \" >", value, "instructions");
            return (Criteria) this;
        }

        public Criteria andInstructionsGreaterThanOrEqualTo(String value) {
            addCriterion("\"instructions \" >=", value, "instructions");
            return (Criteria) this;
        }

        public Criteria andInstructionsLessThan(String value) {
            addCriterion("\"instructions \" <", value, "instructions");
            return (Criteria) this;
        }

        public Criteria andInstructionsLessThanOrEqualTo(String value) {
            addCriterion("\"instructions \" <=", value, "instructions");
            return (Criteria) this;
        }

        public Criteria andInstructionsLike(String value) {
            addCriterion("\"instructions \" like", value, "instructions");
            return (Criteria) this;
        }

        public Criteria andInstructionsNotLike(String value) {
            addCriterion("\"instructions \" not like", value, "instructions");
            return (Criteria) this;
        }

        public Criteria andInstructionsIn(List<String> values) {
            addCriterion("\"instructions \" in", values, "instructions");
            return (Criteria) this;
        }

        public Criteria andInstructionsNotIn(List<String> values) {
            addCriterion("\"instructions \" not in", values, "instructions");
            return (Criteria) this;
        }

        public Criteria andInstructionsBetween(String value1, String value2) {
            addCriterion("\"instructions \" between", value1, value2, "instructions");
            return (Criteria) this;
        }

        public Criteria andInstructionsNotBetween(String value1, String value2) {
            addCriterion("\"instructions \" not between", value1, value2, "instructions");
            return (Criteria) this;
        }

        public Criteria andInputValueIsNull() {
            addCriterion("input_value is null");
            return (Criteria) this;
        }

        public Criteria andInputValueIsNotNull() {
            addCriterion("input_value is not null");
            return (Criteria) this;
        }

        public Criteria andInputValueEqualTo(String value) {
            addCriterion("input_value =", value, "inputValue");
            return (Criteria) this;
        }

        public Criteria andInputValueNotEqualTo(String value) {
            addCriterion("input_value <>", value, "inputValue");
            return (Criteria) this;
        }

        public Criteria andInputValueGreaterThan(String value) {
            addCriterion("input_value >", value, "inputValue");
            return (Criteria) this;
        }

        public Criteria andInputValueGreaterThanOrEqualTo(String value) {
            addCriterion("input_value >=", value, "inputValue");
            return (Criteria) this;
        }

        public Criteria andInputValueLessThan(String value) {
            addCriterion("input_value <", value, "inputValue");
            return (Criteria) this;
        }

        public Criteria andInputValueLessThanOrEqualTo(String value) {
            addCriterion("input_value <=", value, "inputValue");
            return (Criteria) this;
        }

        public Criteria andInputValueLike(String value) {
            addCriterion("input_value like", value, "inputValue");
            return (Criteria) this;
        }

        public Criteria andInputValueNotLike(String value) {
            addCriterion("input_value not like", value, "inputValue");
            return (Criteria) this;
        }

        public Criteria andInputValueIn(List<String> values) {
            addCriterion("input_value in", values, "inputValue");
            return (Criteria) this;
        }

        public Criteria andInputValueNotIn(List<String> values) {
            addCriterion("input_value not in", values, "inputValue");
            return (Criteria) this;
        }

        public Criteria andInputValueBetween(String value1, String value2) {
            addCriterion("input_value between", value1, value2, "inputValue");
            return (Criteria) this;
        }

        public Criteria andInputValueNotBetween(String value1, String value2) {
            addCriterion("input_value not between", value1, value2, "inputValue");
            return (Criteria) this;
        }

        public Criteria andSeqIsNull() {
            addCriterion("seq is null");
            return (Criteria) this;
        }

        public Criteria andSeqIsNotNull() {
            addCriterion("seq is not null");
            return (Criteria) this;
        }

        public Criteria andSeqEqualTo(Integer value) {
            addCriterion("seq =", value, "seq");
            return (Criteria) this;
        }

        public Criteria andSeqNotEqualTo(Integer value) {
            addCriterion("seq <>", value, "seq");
            return (Criteria) this;
        }

        public Criteria andSeqGreaterThan(Integer value) {
            addCriterion("seq >", value, "seq");
            return (Criteria) this;
        }

        public Criteria andSeqGreaterThanOrEqualTo(Integer value) {
            addCriterion("seq >=", value, "seq");
            return (Criteria) this;
        }

        public Criteria andSeqLessThan(Integer value) {
            addCriterion("seq <", value, "seq");
            return (Criteria) this;
        }

        public Criteria andSeqLessThanOrEqualTo(Integer value) {
            addCriterion("seq <=", value, "seq");
            return (Criteria) this;
        }

        public Criteria andSeqIn(List<Integer> values) {
            addCriterion("seq in", values, "seq");
            return (Criteria) this;
        }

        public Criteria andSeqNotIn(List<Integer> values) {
            addCriterion("seq not in", values, "seq");
            return (Criteria) this;
        }

        public Criteria andSeqBetween(Integer value1, Integer value2) {
            addCriterion("seq between", value1, value2, "seq");
            return (Criteria) this;
        }

        public Criteria andSeqNotBetween(Integer value1, Integer value2) {
            addCriterion("seq not between", value1, value2, "seq");
            return (Criteria) this;
        }

        public Criteria andCreationTimeIsNull() {
            addCriterion("creation_time is null");
            return (Criteria) this;
        }

        public Criteria andCreationTimeIsNotNull() {
            addCriterion("creation_time is not null");
            return (Criteria) this;
        }

        public Criteria andCreationTimeEqualTo(LocalDateTime value) {
            addCriterion("creation_time =", value, "creationTime");
            return (Criteria) this;
        }

        public Criteria andCreationTimeNotEqualTo(LocalDateTime value) {
            addCriterion("creation_time <>", value, "creationTime");
            return (Criteria) this;
        }

        public Criteria andCreationTimeGreaterThan(LocalDateTime value) {
            addCriterion("creation_time >", value, "creationTime");
            return (Criteria) this;
        }

        public Criteria andCreationTimeGreaterThanOrEqualTo(LocalDateTime value) {
            addCriterion("creation_time >=", value, "creationTime");
            return (Criteria) this;
        }

        public Criteria andCreationTimeLessThan(LocalDateTime value) {
            addCriterion("creation_time <", value, "creationTime");
            return (Criteria) this;
        }

        public Criteria andCreationTimeLessThanOrEqualTo(LocalDateTime value) {
            addCriterion("creation_time <=", value, "creationTime");
            return (Criteria) this;
        }

        public Criteria andCreationTimeIn(List<LocalDateTime> values) {
            addCriterion("creation_time in", values, "creationTime");
            return (Criteria) this;
        }

        public Criteria andCreationTimeNotIn(List<LocalDateTime> values) {
            addCriterion("creation_time not in", values, "creationTime");
            return (Criteria) this;
        }

        public Criteria andCreationTimeBetween(LocalDateTime value1, LocalDateTime value2) {
            addCriterion("creation_time between", value1, value2, "creationTime");
            return (Criteria) this;
        }

        public Criteria andCreationTimeNotBetween(LocalDateTime value1, LocalDateTime value2) {
            addCriterion("creation_time not between", value1, value2, "creationTime");
            return (Criteria) this;
        }

        public Criteria andCreateUsernameIsNull() {
            addCriterion("create_username is null");
            return (Criteria) this;
        }

        public Criteria andCreateUsernameIsNotNull() {
            addCriterion("create_username is not null");
            return (Criteria) this;
        }

        public Criteria andCreateUsernameEqualTo(String value) {
            addCriterion("create_username =", value, "createUsername");
            return (Criteria) this;
        }

        public Criteria andCreateUsernameNotEqualTo(String value) {
            addCriterion("create_username <>", value, "createUsername");
            return (Criteria) this;
        }

        public Criteria andCreateUsernameGreaterThan(String value) {
            addCriterion("create_username >", value, "createUsername");
            return (Criteria) this;
        }

        public Criteria andCreateUsernameGreaterThanOrEqualTo(String value) {
            addCriterion("create_username >=", value, "createUsername");
            return (Criteria) this;
        }

        public Criteria andCreateUsernameLessThan(String value) {
            addCriterion("create_username <", value, "createUsername");
            return (Criteria) this;
        }

        public Criteria andCreateUsernameLessThanOrEqualTo(String value) {
            addCriterion("create_username <=", value, "createUsername");
            return (Criteria) this;
        }

        public Criteria andCreateUsernameLike(String value) {
            addCriterion("create_username like", value, "createUsername");
            return (Criteria) this;
        }

        public Criteria andCreateUsernameNotLike(String value) {
            addCriterion("create_username not like", value, "createUsername");
            return (Criteria) this;
        }

        public Criteria andCreateUsernameIn(List<String> values) {
            addCriterion("create_username in", values, "createUsername");
            return (Criteria) this;
        }

        public Criteria andCreateUsernameNotIn(List<String> values) {
            addCriterion("create_username not in", values, "createUsername");
            return (Criteria) this;
        }

        public Criteria andCreateUsernameBetween(String value1, String value2) {
            addCriterion("create_username between", value1, value2, "createUsername");
            return (Criteria) this;
        }

        public Criteria andCreateUsernameNotBetween(String value1, String value2) {
            addCriterion("create_username not between", value1, value2, "createUsername");
            return (Criteria) this;
        }

        public Criteria andCreateUserIdIsNull() {
            addCriterion("create_user_id is null");
            return (Criteria) this;
        }

        public Criteria andCreateUserIdIsNotNull() {
            addCriterion("create_user_id is not null");
            return (Criteria) this;
        }

        public Criteria andCreateUserIdEqualTo(String value) {
            addCriterion("create_user_id =", value, "createUserId");
            return (Criteria) this;
        }

        public Criteria andCreateUserIdNotEqualTo(String value) {
            addCriterion("create_user_id <>", value, "createUserId");
            return (Criteria) this;
        }

        public Criteria andCreateUserIdGreaterThan(String value) {
            addCriterion("create_user_id >", value, "createUserId");
            return (Criteria) this;
        }

        public Criteria andCreateUserIdGreaterThanOrEqualTo(String value) {
            addCriterion("create_user_id >=", value, "createUserId");
            return (Criteria) this;
        }

        public Criteria andCreateUserIdLessThan(String value) {
            addCriterion("create_user_id <", value, "createUserId");
            return (Criteria) this;
        }

        public Criteria andCreateUserIdLessThanOrEqualTo(String value) {
            addCriterion("create_user_id <=", value, "createUserId");
            return (Criteria) this;
        }

        public Criteria andCreateUserIdLike(String value) {
            addCriterion("create_user_id like", value, "createUserId");
            return (Criteria) this;
        }

        public Criteria andCreateUserIdNotLike(String value) {
            addCriterion("create_user_id not like", value, "createUserId");
            return (Criteria) this;
        }

        public Criteria andCreateUserIdIn(List<String> values) {
            addCriterion("create_user_id in", values, "createUserId");
            return (Criteria) this;
        }

        public Criteria andCreateUserIdNotIn(List<String> values) {
            addCriterion("create_user_id not in", values, "createUserId");
            return (Criteria) this;
        }

        public Criteria andCreateUserIdBetween(String value1, String value2) {
            addCriterion("create_user_id between", value1, value2, "createUserId");
            return (Criteria) this;
        }

        public Criteria andCreateUserIdNotBetween(String value1, String value2) {
            addCriterion("create_user_id not between", value1, value2, "createUserId");
            return (Criteria) this;
        }

        public Criteria andLastUpdateTimeIsNull() {
            addCriterion("last_update_time is null");
            return (Criteria) this;
        }

        public Criteria andLastUpdateTimeIsNotNull() {
            addCriterion("last_update_time is not null");
            return (Criteria) this;
        }

        public Criteria andLastUpdateTimeEqualTo(LocalDateTime value) {
            addCriterion("last_update_time =", value, "lastUpdateTime");
            return (Criteria) this;
        }

        public Criteria andLastUpdateTimeNotEqualTo(LocalDateTime value) {
            addCriterion("last_update_time <>", value, "lastUpdateTime");
            return (Criteria) this;
        }

        public Criteria andLastUpdateTimeGreaterThan(LocalDateTime value) {
            addCriterion("last_update_time >", value, "lastUpdateTime");
            return (Criteria) this;
        }

        public Criteria andLastUpdateTimeGreaterThanOrEqualTo(LocalDateTime value) {
            addCriterion("last_update_time >=", value, "lastUpdateTime");
            return (Criteria) this;
        }

        public Criteria andLastUpdateTimeLessThan(LocalDateTime value) {
            addCriterion("last_update_time <", value, "lastUpdateTime");
            return (Criteria) this;
        }

        public Criteria andLastUpdateTimeLessThanOrEqualTo(LocalDateTime value) {
            addCriterion("last_update_time <=", value, "lastUpdateTime");
            return (Criteria) this;
        }

        public Criteria andLastUpdateTimeIn(List<LocalDateTime> values) {
            addCriterion("last_update_time in", values, "lastUpdateTime");
            return (Criteria) this;
        }

        public Criteria andLastUpdateTimeNotIn(List<LocalDateTime> values) {
            addCriterion("last_update_time not in", values, "lastUpdateTime");
            return (Criteria) this;
        }

        public Criteria andLastUpdateTimeBetween(LocalDateTime value1, LocalDateTime value2) {
            addCriterion("last_update_time between", value1, value2, "lastUpdateTime");
            return (Criteria) this;
        }

        public Criteria andLastUpdateTimeNotBetween(LocalDateTime value1, LocalDateTime value2) {
            addCriterion("last_update_time not between", value1, value2, "lastUpdateTime");
            return (Criteria) this;
        }

        public Criteria andLastUpdateUsernameIsNull() {
            addCriterion("last_update_username is null");
            return (Criteria) this;
        }

        public Criteria andLastUpdateUsernameIsNotNull() {
            addCriterion("last_update_username is not null");
            return (Criteria) this;
        }

        public Criteria andLastUpdateUsernameEqualTo(String value) {
            addCriterion("last_update_username =", value, "lastUpdateUsername");
            return (Criteria) this;
        }

        public Criteria andLastUpdateUsernameNotEqualTo(String value) {
            addCriterion("last_update_username <>", value, "lastUpdateUsername");
            return (Criteria) this;
        }

        public Criteria andLastUpdateUsernameGreaterThan(String value) {
            addCriterion("last_update_username >", value, "lastUpdateUsername");
            return (Criteria) this;
        }

        public Criteria andLastUpdateUsernameGreaterThanOrEqualTo(String value) {
            addCriterion("last_update_username >=", value, "lastUpdateUsername");
            return (Criteria) this;
        }

        public Criteria andLastUpdateUsernameLessThan(String value) {
            addCriterion("last_update_username <", value, "lastUpdateUsername");
            return (Criteria) this;
        }

        public Criteria andLastUpdateUsernameLessThanOrEqualTo(String value) {
            addCriterion("last_update_username <=", value, "lastUpdateUsername");
            return (Criteria) this;
        }

        public Criteria andLastUpdateUsernameLike(String value) {
            addCriterion("last_update_username like", value, "lastUpdateUsername");
            return (Criteria) this;
        }

        public Criteria andLastUpdateUsernameNotLike(String value) {
            addCriterion("last_update_username not like", value, "lastUpdateUsername");
            return (Criteria) this;
        }

        public Criteria andLastUpdateUsernameIn(List<String> values) {
            addCriterion("last_update_username in", values, "lastUpdateUsername");
            return (Criteria) this;
        }

        public Criteria andLastUpdateUsernameNotIn(List<String> values) {
            addCriterion("last_update_username not in", values, "lastUpdateUsername");
            return (Criteria) this;
        }

        public Criteria andLastUpdateUsernameBetween(String value1, String value2) {
            addCriterion("last_update_username between", value1, value2, "lastUpdateUsername");
            return (Criteria) this;
        }

        public Criteria andLastUpdateUsernameNotBetween(String value1, String value2) {
            addCriterion("last_update_username not between", value1, value2, "lastUpdateUsername");
            return (Criteria) this;
        }

        public Criteria andLastUpdateUserIdIsNull() {
            addCriterion("last_update_user_id is null");
            return (Criteria) this;
        }

        public Criteria andLastUpdateUserIdIsNotNull() {
            addCriterion("last_update_user_id is not null");
            return (Criteria) this;
        }

        public Criteria andLastUpdateUserIdEqualTo(String value) {
            addCriterion("last_update_user_id =", value, "lastUpdateUserId");
            return (Criteria) this;
        }

        public Criteria andLastUpdateUserIdNotEqualTo(String value) {
            addCriterion("last_update_user_id <>", value, "lastUpdateUserId");
            return (Criteria) this;
        }

        public Criteria andLastUpdateUserIdGreaterThan(String value) {
            addCriterion("last_update_user_id >", value, "lastUpdateUserId");
            return (Criteria) this;
        }

        public Criteria andLastUpdateUserIdGreaterThanOrEqualTo(String value) {
            addCriterion("last_update_user_id >=", value, "lastUpdateUserId");
            return (Criteria) this;
        }

        public Criteria andLastUpdateUserIdLessThan(String value) {
            addCriterion("last_update_user_id <", value, "lastUpdateUserId");
            return (Criteria) this;
        }

        public Criteria andLastUpdateUserIdLessThanOrEqualTo(String value) {
            addCriterion("last_update_user_id <=", value, "lastUpdateUserId");
            return (Criteria) this;
        }

        public Criteria andLastUpdateUserIdLike(String value) {
            addCriterion("last_update_user_id like", value, "lastUpdateUserId");
            return (Criteria) this;
        }

        public Criteria andLastUpdateUserIdNotLike(String value) {
            addCriterion("last_update_user_id not like", value, "lastUpdateUserId");
            return (Criteria) this;
        }

        public Criteria andLastUpdateUserIdIn(List<String> values) {
            addCriterion("last_update_user_id in", values, "lastUpdateUserId");
            return (Criteria) this;
        }

        public Criteria andLastUpdateUserIdNotIn(List<String> values) {
            addCriterion("last_update_user_id not in", values, "lastUpdateUserId");
            return (Criteria) this;
        }

        public Criteria andLastUpdateUserIdBetween(String value1, String value2) {
            addCriterion("last_update_user_id between", value1, value2, "lastUpdateUserId");
            return (Criteria) this;
        }

        public Criteria andLastUpdateUserIdNotBetween(String value1, String value2) {
            addCriterion("last_update_user_id not between", value1, value2, "lastUpdateUserId");
            return (Criteria) this;
        }

        public Criteria andLastUpdateVersionIsNull() {
            addCriterion("last_update_version is null");
            return (Criteria) this;
        }

        public Criteria andLastUpdateVersionIsNotNull() {
            addCriterion("last_update_version is not null");
            return (Criteria) this;
        }

        public Criteria andLastUpdateVersionEqualTo(Integer value) {
            addCriterion("last_update_version =", value, "lastUpdateVersion");
            return (Criteria) this;
        }

        public Criteria andLastUpdateVersionNotEqualTo(Integer value) {
            addCriterion("last_update_version <>", value, "lastUpdateVersion");
            return (Criteria) this;
        }

        public Criteria andLastUpdateVersionGreaterThan(Integer value) {
            addCriterion("last_update_version >", value, "lastUpdateVersion");
            return (Criteria) this;
        }

        public Criteria andLastUpdateVersionGreaterThanOrEqualTo(Integer value) {
            addCriterion("last_update_version >=", value, "lastUpdateVersion");
            return (Criteria) this;
        }

        public Criteria andLastUpdateVersionLessThan(Integer value) {
            addCriterion("last_update_version <", value, "lastUpdateVersion");
            return (Criteria) this;
        }

        public Criteria andLastUpdateVersionLessThanOrEqualTo(Integer value) {
            addCriterion("last_update_version <=", value, "lastUpdateVersion");
            return (Criteria) this;
        }

        public Criteria andLastUpdateVersionIn(List<Integer> values) {
            addCriterion("last_update_version in", values, "lastUpdateVersion");
            return (Criteria) this;
        }

        public Criteria andLastUpdateVersionNotIn(List<Integer> values) {
            addCriterion("last_update_version not in", values, "lastUpdateVersion");
            return (Criteria) this;
        }

        public Criteria andLastUpdateVersionBetween(Integer value1, Integer value2) {
            addCriterion("last_update_version between", value1, value2, "lastUpdateVersion");
            return (Criteria) this;
        }

        public Criteria andLastUpdateVersionNotBetween(Integer value1, Integer value2) {
            addCriterion("last_update_version not between", value1, value2, "lastUpdateVersion");
            return (Criteria) this;
        }

        public Criteria andIsDeletedIsNull() {
            addCriterion("is_deleted is null");
            return (Criteria) this;
        }

        public Criteria andIsDeletedIsNotNull() {
            addCriterion("is_deleted is not null");
            return (Criteria) this;
        }

        public Criteria andIsDeletedEqualTo(Boolean value) {
            addCriterion("is_deleted =", value, "isDeleted");
            return (Criteria) this;
        }

        public Criteria andIsDeletedNotEqualTo(Boolean value) {
            addCriterion("is_deleted <>", value, "isDeleted");
            return (Criteria) this;
        }

        public Criteria andIsDeletedGreaterThan(Boolean value) {
            addCriterion("is_deleted >", value, "isDeleted");
            return (Criteria) this;
        }

        public Criteria andIsDeletedGreaterThanOrEqualTo(Boolean value) {
            addCriterion("is_deleted >=", value, "isDeleted");
            return (Criteria) this;
        }

        public Criteria andIsDeletedLessThan(Boolean value) {
            addCriterion("is_deleted <", value, "isDeleted");
            return (Criteria) this;
        }

        public Criteria andIsDeletedLessThanOrEqualTo(Boolean value) {
            addCriterion("is_deleted <=", value, "isDeleted");
            return (Criteria) this;
        }

        public Criteria andIsDeletedIn(List<Boolean> values) {
            addCriterion("is_deleted in", values, "isDeleted");
            return (Criteria) this;
        }

        public Criteria andIsDeletedNotIn(List<Boolean> values) {
            addCriterion("is_deleted not in", values, "isDeleted");
            return (Criteria) this;
        }

        public Criteria andIsDeletedBetween(Boolean value1, Boolean value2) {
            addCriterion("is_deleted between", value1, value2, "isDeleted");
            return (Criteria) this;
        }

        public Criteria andIsDeletedNotBetween(Boolean value1, Boolean value2) {
            addCriterion("is_deleted not between", value1, value2, "isDeleted");
            return (Criteria) this;
        }

        public Criteria andControlColIsNull() {
            addCriterion("control_col is null");
            return (Criteria) this;
        }

        public Criteria andControlColIsNotNull() {
            addCriterion("control_col is not null");
            return (Criteria) this;
        }

        public Criteria andControlColEqualTo(String value) {
            addCriterion("control_col =", value, "controlCol");
            return (Criteria) this;
        }

        public Criteria andControlColNotEqualTo(String value) {
            addCriterion("control_col <>", value, "controlCol");
            return (Criteria) this;
        }

        public Criteria andControlColGreaterThan(String value) {
            addCriterion("control_col >", value, "controlCol");
            return (Criteria) this;
        }

        public Criteria andControlColGreaterThanOrEqualTo(String value) {
            addCriterion("control_col >=", value, "controlCol");
            return (Criteria) this;
        }

        public Criteria andControlColLessThan(String value) {
            addCriterion("control_col <", value, "controlCol");
            return (Criteria) this;
        }

        public Criteria andControlColLessThanOrEqualTo(String value) {
            addCriterion("control_col <=", value, "controlCol");
            return (Criteria) this;
        }

        public Criteria andControlColLike(String value) {
            addCriterion("control_col like", value, "controlCol");
            return (Criteria) this;
        }

        public Criteria andControlColNotLike(String value) {
            addCriterion("control_col not like", value, "controlCol");
            return (Criteria) this;
        }

        public Criteria andControlColIn(List<String> values) {
            addCriterion("control_col in", values, "controlCol");
            return (Criteria) this;
        }

        public Criteria andControlColNotIn(List<String> values) {
            addCriterion("control_col not in", values, "controlCol");
            return (Criteria) this;
        }

        public Criteria andControlColBetween(String value1, String value2) {
            addCriterion("control_col between", value1, value2, "controlCol");
            return (Criteria) this;
        }

        public Criteria andControlColNotBetween(String value1, String value2) {
            addCriterion("control_col not between", value1, value2, "controlCol");
            return (Criteria) this;
        }
    }

    /**
     * This class was generated by MyBatis Generator.
     * This class corresponds to the database table t_ce_identification_fugitive_emission
     *
     * @mbg.generated do_not_delete_during_merge Wed Apr 02 16:23:01 CST 2025
     */
    public static class Criteria extends GeneratedCriteria {
        protected Criteria() {
            super();
        }
    }

    /**
     * This class was generated by MyBatis Generator.
     * This class corresponds to the database table t_ce_identification_fugitive_emission
     *
     * @mbg.generated Wed Apr 02 16:23:01 CST 2025
     */
    public static class Criterion {
        private String condition;

        private Object value;

        private Object secondValue;

        private boolean noValue;

        private boolean singleValue;

        private boolean betweenValue;

        private boolean listValue;

        private String typeHandler;

        public String getCondition() {
            return condition;
        }

        public Object getValue() {
            return value;
        }

        public Object getSecondValue() {
            return secondValue;
        }

        public boolean isNoValue() {
            return noValue;
        }

        public boolean isSingleValue() {
            return singleValue;
        }

        public boolean isBetweenValue() {
            return betweenValue;
        }

        public boolean isListValue() {
            return listValue;
        }

        public String getTypeHandler() {
            return typeHandler;
        }

        protected Criterion(String condition) {
            super();
            this.condition = condition;
            this.typeHandler = null;
            this.noValue = true;
        }

        protected Criterion(String condition, Object value, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.typeHandler = typeHandler;
            if (value instanceof List<?>) {
                this.listValue = true;
            } else {
                this.singleValue = true;
            }
        }

        protected Criterion(String condition, Object value) {
            this(condition, value, null);
        }

        protected Criterion(String condition, Object value, Object secondValue, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.secondValue = secondValue;
            this.typeHandler = typeHandler;
            this.betweenValue = true;
        }

        protected Criterion(String condition, Object value, Object secondValue) {
            this(condition, value, secondValue, null);
        }
    }
}