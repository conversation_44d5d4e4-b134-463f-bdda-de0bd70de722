package com.csci.susdev.model;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;

public class SubmissionMonthConfigExample {
    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database table t_submission_month_config
     *
     * @mbg.generated Thu Apr 18 11:23:09 HKT 2024
     */
    protected String orderByClause;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database table t_submission_month_config
     *
     * @mbg.generated Thu Apr 18 11:23:09 HKT 2024
     */
    protected boolean distinct;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database table t_submission_month_config
     *
     * @mbg.generated Thu Apr 18 11:23:09 HKT 2024
     */
    protected List<Criteria> oredCriteria;

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table t_submission_month_config
     *
     * @mbg.generated Thu Apr 18 11:23:09 HKT 2024
     */
    public SubmissionMonthConfigExample() {
        oredCriteria = new ArrayList<>();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table t_submission_month_config
     *
     * @mbg.generated Thu Apr 18 11:23:09 HKT 2024
     */
    public void setOrderByClause(String orderByClause) {
        this.orderByClause = orderByClause;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table t_submission_month_config
     *
     * @mbg.generated Thu Apr 18 11:23:09 HKT 2024
     */
    public String getOrderByClause() {
        return orderByClause;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table t_submission_month_config
     *
     * @mbg.generated Thu Apr 18 11:23:09 HKT 2024
     */
    public void setDistinct(boolean distinct) {
        this.distinct = distinct;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table t_submission_month_config
     *
     * @mbg.generated Thu Apr 18 11:23:09 HKT 2024
     */
    public boolean isDistinct() {
        return distinct;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table t_submission_month_config
     *
     * @mbg.generated Thu Apr 18 11:23:09 HKT 2024
     */
    public List<Criteria> getOredCriteria() {
        return oredCriteria;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table t_submission_month_config
     *
     * @mbg.generated Thu Apr 18 11:23:09 HKT 2024
     */
    public void or(Criteria criteria) {
        oredCriteria.add(criteria);
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table t_submission_month_config
     *
     * @mbg.generated Thu Apr 18 11:23:09 HKT 2024
     */
    public Criteria or() {
        Criteria criteria = createCriteriaInternal();
        oredCriteria.add(criteria);
        return criteria;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table t_submission_month_config
     *
     * @mbg.generated Thu Apr 18 11:23:09 HKT 2024
     */
    public Criteria createCriteria() {
        Criteria criteria = createCriteriaInternal();
        if (oredCriteria.size() == 0) {
            oredCriteria.add(criteria);
        }
        return criteria;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table t_submission_month_config
     *
     * @mbg.generated Thu Apr 18 11:23:09 HKT 2024
     */
    protected Criteria createCriteriaInternal() {
        Criteria criteria = new Criteria();
        return criteria;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table t_submission_month_config
     *
     * @mbg.generated Thu Apr 18 11:23:09 HKT 2024
     */
    public void clear() {
        oredCriteria.clear();
        orderByClause = null;
        distinct = false;
    }

    /**
     * This class was generated by MyBatis Generator.
     * This class corresponds to the database table t_submission_month_config
     *
     * @mbg.generated Thu Apr 18 11:23:09 HKT 2024
     */
    protected abstract static class GeneratedCriteria {
        protected List<Criterion> criteria;

        protected GeneratedCriteria() {
            super();
            criteria = new ArrayList<>();
        }

        public boolean isValid() {
            return criteria.size() > 0;
        }

        public List<Criterion> getAllCriteria() {
            return criteria;
        }

        public List<Criterion> getCriteria() {
            return criteria;
        }

        protected void addCriterion(String condition) {
            if (condition == null) {
                throw new RuntimeException("Value for condition cannot be null");
            }
            criteria.add(new Criterion(condition));
        }

        protected void addCriterion(String condition, Object value, String property) {
            if (value == null) {
                throw new RuntimeException("Value for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value));
        }

        protected void addCriterion(String condition, Object value1, Object value2, String property) {
            if (value1 == null || value2 == null) {
                throw new RuntimeException("Between values for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value1, value2));
        }

        public Criteria andIdIsNull() {
            addCriterion("id is null");
            return (Criteria) this;
        }

        public Criteria andIdIsNotNull() {
            addCriterion("id is not null");
            return (Criteria) this;
        }

        public Criteria andIdEqualTo(String value) {
            addCriterion("id =", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotEqualTo(String value) {
            addCriterion("id <>", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThan(String value) {
            addCriterion("id >", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThanOrEqualTo(String value) {
            addCriterion("id >=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThan(String value) {
            addCriterion("id <", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThanOrEqualTo(String value) {
            addCriterion("id <=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLike(String value) {
            addCriterion("id like", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotLike(String value) {
            addCriterion("id not like", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdIn(List<String> values) {
            addCriterion("id in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotIn(List<String> values) {
            addCriterion("id not in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdBetween(String value1, String value2) {
            addCriterion("id between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotBetween(String value1, String value2) {
            addCriterion("id not between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andYearIsNull() {
            addCriterion("year is null");
            return (Criteria) this;
        }

        public Criteria andYearIsNotNull() {
            addCriterion("year is not null");
            return (Criteria) this;
        }

        public Criteria andYearEqualTo(Integer value) {
            addCriterion("year =", value, "year");
            return (Criteria) this;
        }

        public Criteria andYearNotEqualTo(Integer value) {
            addCriterion("year <>", value, "year");
            return (Criteria) this;
        }

        public Criteria andYearGreaterThan(Integer value) {
            addCriterion("year >", value, "year");
            return (Criteria) this;
        }

        public Criteria andYearGreaterThanOrEqualTo(Integer value) {
            addCriterion("year >=", value, "year");
            return (Criteria) this;
        }

        public Criteria andYearLessThan(Integer value) {
            addCriterion("year <", value, "year");
            return (Criteria) this;
        }

        public Criteria andYearLessThanOrEqualTo(Integer value) {
            addCriterion("year <=", value, "year");
            return (Criteria) this;
        }

        public Criteria andYearIn(List<Integer> values) {
            addCriterion("year in", values, "year");
            return (Criteria) this;
        }

        public Criteria andYearNotIn(List<Integer> values) {
            addCriterion("year not in", values, "year");
            return (Criteria) this;
        }

        public Criteria andYearBetween(Integer value1, Integer value2) {
            addCriterion("year between", value1, value2, "year");
            return (Criteria) this;
        }

        public Criteria andYearNotBetween(Integer value1, Integer value2) {
            addCriterion("year not between", value1, value2, "year");
            return (Criteria) this;
        }

        public Criteria andMonth1IsNull() {
            addCriterion("month1 is null");
            return (Criteria) this;
        }

        public Criteria andMonth1IsNotNull() {
            addCriterion("month1 is not null");
            return (Criteria) this;
        }

        public Criteria andMonth1EqualTo(Boolean value) {
            addCriterion("month1 =", value, "month1");
            return (Criteria) this;
        }

        public Criteria andMonth1NotEqualTo(Boolean value) {
            addCriterion("month1 <>", value, "month1");
            return (Criteria) this;
        }

        public Criteria andMonth1GreaterThan(Boolean value) {
            addCriterion("month1 >", value, "month1");
            return (Criteria) this;
        }

        public Criteria andMonth1GreaterThanOrEqualTo(Boolean value) {
            addCriterion("month1 >=", value, "month1");
            return (Criteria) this;
        }

        public Criteria andMonth1LessThan(Boolean value) {
            addCriterion("month1 <", value, "month1");
            return (Criteria) this;
        }

        public Criteria andMonth1LessThanOrEqualTo(Boolean value) {
            addCriterion("month1 <=", value, "month1");
            return (Criteria) this;
        }

        public Criteria andMonth1In(List<Boolean> values) {
            addCriterion("month1 in", values, "month1");
            return (Criteria) this;
        }

        public Criteria andMonth1NotIn(List<Boolean> values) {
            addCriterion("month1 not in", values, "month1");
            return (Criteria) this;
        }

        public Criteria andMonth1Between(Boolean value1, Boolean value2) {
            addCriterion("month1 between", value1, value2, "month1");
            return (Criteria) this;
        }

        public Criteria andMonth1NotBetween(Boolean value1, Boolean value2) {
            addCriterion("month1 not between", value1, value2, "month1");
            return (Criteria) this;
        }

        public Criteria andMonth2IsNull() {
            addCriterion("month2 is null");
            return (Criteria) this;
        }

        public Criteria andMonth2IsNotNull() {
            addCriterion("month2 is not null");
            return (Criteria) this;
        }

        public Criteria andMonth2EqualTo(Boolean value) {
            addCriterion("month2 =", value, "month2");
            return (Criteria) this;
        }

        public Criteria andMonth2NotEqualTo(Boolean value) {
            addCriterion("month2 <>", value, "month2");
            return (Criteria) this;
        }

        public Criteria andMonth2GreaterThan(Boolean value) {
            addCriterion("month2 >", value, "month2");
            return (Criteria) this;
        }

        public Criteria andMonth2GreaterThanOrEqualTo(Boolean value) {
            addCriterion("month2 >=", value, "month2");
            return (Criteria) this;
        }

        public Criteria andMonth2LessThan(Boolean value) {
            addCriterion("month2 <", value, "month2");
            return (Criteria) this;
        }

        public Criteria andMonth2LessThanOrEqualTo(Boolean value) {
            addCriterion("month2 <=", value, "month2");
            return (Criteria) this;
        }

        public Criteria andMonth2In(List<Boolean> values) {
            addCriterion("month2 in", values, "month2");
            return (Criteria) this;
        }

        public Criteria andMonth2NotIn(List<Boolean> values) {
            addCriterion("month2 not in", values, "month2");
            return (Criteria) this;
        }

        public Criteria andMonth2Between(Boolean value1, Boolean value2) {
            addCriterion("month2 between", value1, value2, "month2");
            return (Criteria) this;
        }

        public Criteria andMonth2NotBetween(Boolean value1, Boolean value2) {
            addCriterion("month2 not between", value1, value2, "month2");
            return (Criteria) this;
        }

        public Criteria andMonth3IsNull() {
            addCriterion("month3 is null");
            return (Criteria) this;
        }

        public Criteria andMonth3IsNotNull() {
            addCriterion("month3 is not null");
            return (Criteria) this;
        }

        public Criteria andMonth3EqualTo(Boolean value) {
            addCriterion("month3 =", value, "month3");
            return (Criteria) this;
        }

        public Criteria andMonth3NotEqualTo(Boolean value) {
            addCriterion("month3 <>", value, "month3");
            return (Criteria) this;
        }

        public Criteria andMonth3GreaterThan(Boolean value) {
            addCriterion("month3 >", value, "month3");
            return (Criteria) this;
        }

        public Criteria andMonth3GreaterThanOrEqualTo(Boolean value) {
            addCriterion("month3 >=", value, "month3");
            return (Criteria) this;
        }

        public Criteria andMonth3LessThan(Boolean value) {
            addCriterion("month3 <", value, "month3");
            return (Criteria) this;
        }

        public Criteria andMonth3LessThanOrEqualTo(Boolean value) {
            addCriterion("month3 <=", value, "month3");
            return (Criteria) this;
        }

        public Criteria andMonth3In(List<Boolean> values) {
            addCriterion("month3 in", values, "month3");
            return (Criteria) this;
        }

        public Criteria andMonth3NotIn(List<Boolean> values) {
            addCriterion("month3 not in", values, "month3");
            return (Criteria) this;
        }

        public Criteria andMonth3Between(Boolean value1, Boolean value2) {
            addCriterion("month3 between", value1, value2, "month3");
            return (Criteria) this;
        }

        public Criteria andMonth3NotBetween(Boolean value1, Boolean value2) {
            addCriterion("month3 not between", value1, value2, "month3");
            return (Criteria) this;
        }

        public Criteria andMonth4IsNull() {
            addCriterion("month4 is null");
            return (Criteria) this;
        }

        public Criteria andMonth4IsNotNull() {
            addCriterion("month4 is not null");
            return (Criteria) this;
        }

        public Criteria andMonth4EqualTo(Boolean value) {
            addCriterion("month4 =", value, "month4");
            return (Criteria) this;
        }

        public Criteria andMonth4NotEqualTo(Boolean value) {
            addCriterion("month4 <>", value, "month4");
            return (Criteria) this;
        }

        public Criteria andMonth4GreaterThan(Boolean value) {
            addCriterion("month4 >", value, "month4");
            return (Criteria) this;
        }

        public Criteria andMonth4GreaterThanOrEqualTo(Boolean value) {
            addCriterion("month4 >=", value, "month4");
            return (Criteria) this;
        }

        public Criteria andMonth4LessThan(Boolean value) {
            addCriterion("month4 <", value, "month4");
            return (Criteria) this;
        }

        public Criteria andMonth4LessThanOrEqualTo(Boolean value) {
            addCriterion("month4 <=", value, "month4");
            return (Criteria) this;
        }

        public Criteria andMonth4In(List<Boolean> values) {
            addCriterion("month4 in", values, "month4");
            return (Criteria) this;
        }

        public Criteria andMonth4NotIn(List<Boolean> values) {
            addCriterion("month4 not in", values, "month4");
            return (Criteria) this;
        }

        public Criteria andMonth4Between(Boolean value1, Boolean value2) {
            addCriterion("month4 between", value1, value2, "month4");
            return (Criteria) this;
        }

        public Criteria andMonth4NotBetween(Boolean value1, Boolean value2) {
            addCriterion("month4 not between", value1, value2, "month4");
            return (Criteria) this;
        }

        public Criteria andMonth5IsNull() {
            addCriterion("month5 is null");
            return (Criteria) this;
        }

        public Criteria andMonth5IsNotNull() {
            addCriterion("month5 is not null");
            return (Criteria) this;
        }

        public Criteria andMonth5EqualTo(Boolean value) {
            addCriterion("month5 =", value, "month5");
            return (Criteria) this;
        }

        public Criteria andMonth5NotEqualTo(Boolean value) {
            addCriterion("month5 <>", value, "month5");
            return (Criteria) this;
        }

        public Criteria andMonth5GreaterThan(Boolean value) {
            addCriterion("month5 >", value, "month5");
            return (Criteria) this;
        }

        public Criteria andMonth5GreaterThanOrEqualTo(Boolean value) {
            addCriterion("month5 >=", value, "month5");
            return (Criteria) this;
        }

        public Criteria andMonth5LessThan(Boolean value) {
            addCriterion("month5 <", value, "month5");
            return (Criteria) this;
        }

        public Criteria andMonth5LessThanOrEqualTo(Boolean value) {
            addCriterion("month5 <=", value, "month5");
            return (Criteria) this;
        }

        public Criteria andMonth5In(List<Boolean> values) {
            addCriterion("month5 in", values, "month5");
            return (Criteria) this;
        }

        public Criteria andMonth5NotIn(List<Boolean> values) {
            addCriterion("month5 not in", values, "month5");
            return (Criteria) this;
        }

        public Criteria andMonth5Between(Boolean value1, Boolean value2) {
            addCriterion("month5 between", value1, value2, "month5");
            return (Criteria) this;
        }

        public Criteria andMonth5NotBetween(Boolean value1, Boolean value2) {
            addCriterion("month5 not between", value1, value2, "month5");
            return (Criteria) this;
        }

        public Criteria andMonth6IsNull() {
            addCriterion("month6 is null");
            return (Criteria) this;
        }

        public Criteria andMonth6IsNotNull() {
            addCriterion("month6 is not null");
            return (Criteria) this;
        }

        public Criteria andMonth6EqualTo(Boolean value) {
            addCriterion("month6 =", value, "month6");
            return (Criteria) this;
        }

        public Criteria andMonth6NotEqualTo(Boolean value) {
            addCriterion("month6 <>", value, "month6");
            return (Criteria) this;
        }

        public Criteria andMonth6GreaterThan(Boolean value) {
            addCriterion("month6 >", value, "month6");
            return (Criteria) this;
        }

        public Criteria andMonth6GreaterThanOrEqualTo(Boolean value) {
            addCriterion("month6 >=", value, "month6");
            return (Criteria) this;
        }

        public Criteria andMonth6LessThan(Boolean value) {
            addCriterion("month6 <", value, "month6");
            return (Criteria) this;
        }

        public Criteria andMonth6LessThanOrEqualTo(Boolean value) {
            addCriterion("month6 <=", value, "month6");
            return (Criteria) this;
        }

        public Criteria andMonth6In(List<Boolean> values) {
            addCriterion("month6 in", values, "month6");
            return (Criteria) this;
        }

        public Criteria andMonth6NotIn(List<Boolean> values) {
            addCriterion("month6 not in", values, "month6");
            return (Criteria) this;
        }

        public Criteria andMonth6Between(Boolean value1, Boolean value2) {
            addCriterion("month6 between", value1, value2, "month6");
            return (Criteria) this;
        }

        public Criteria andMonth6NotBetween(Boolean value1, Boolean value2) {
            addCriterion("month6 not between", value1, value2, "month6");
            return (Criteria) this;
        }

        public Criteria andMonth7IsNull() {
            addCriterion("month7 is null");
            return (Criteria) this;
        }

        public Criteria andMonth7IsNotNull() {
            addCriterion("month7 is not null");
            return (Criteria) this;
        }

        public Criteria andMonth7EqualTo(Boolean value) {
            addCriterion("month7 =", value, "month7");
            return (Criteria) this;
        }

        public Criteria andMonth7NotEqualTo(Boolean value) {
            addCriterion("month7 <>", value, "month7");
            return (Criteria) this;
        }

        public Criteria andMonth7GreaterThan(Boolean value) {
            addCriterion("month7 >", value, "month7");
            return (Criteria) this;
        }

        public Criteria andMonth7GreaterThanOrEqualTo(Boolean value) {
            addCriterion("month7 >=", value, "month7");
            return (Criteria) this;
        }

        public Criteria andMonth7LessThan(Boolean value) {
            addCriterion("month7 <", value, "month7");
            return (Criteria) this;
        }

        public Criteria andMonth7LessThanOrEqualTo(Boolean value) {
            addCriterion("month7 <=", value, "month7");
            return (Criteria) this;
        }

        public Criteria andMonth7In(List<Boolean> values) {
            addCriterion("month7 in", values, "month7");
            return (Criteria) this;
        }

        public Criteria andMonth7NotIn(List<Boolean> values) {
            addCriterion("month7 not in", values, "month7");
            return (Criteria) this;
        }

        public Criteria andMonth7Between(Boolean value1, Boolean value2) {
            addCriterion("month7 between", value1, value2, "month7");
            return (Criteria) this;
        }

        public Criteria andMonth7NotBetween(Boolean value1, Boolean value2) {
            addCriterion("month7 not between", value1, value2, "month7");
            return (Criteria) this;
        }

        public Criteria andMonth8IsNull() {
            addCriterion("month8 is null");
            return (Criteria) this;
        }

        public Criteria andMonth8IsNotNull() {
            addCriterion("month8 is not null");
            return (Criteria) this;
        }

        public Criteria andMonth8EqualTo(Boolean value) {
            addCriterion("month8 =", value, "month8");
            return (Criteria) this;
        }

        public Criteria andMonth8NotEqualTo(Boolean value) {
            addCriterion("month8 <>", value, "month8");
            return (Criteria) this;
        }

        public Criteria andMonth8GreaterThan(Boolean value) {
            addCriterion("month8 >", value, "month8");
            return (Criteria) this;
        }

        public Criteria andMonth8GreaterThanOrEqualTo(Boolean value) {
            addCriterion("month8 >=", value, "month8");
            return (Criteria) this;
        }

        public Criteria andMonth8LessThan(Boolean value) {
            addCriterion("month8 <", value, "month8");
            return (Criteria) this;
        }

        public Criteria andMonth8LessThanOrEqualTo(Boolean value) {
            addCriterion("month8 <=", value, "month8");
            return (Criteria) this;
        }

        public Criteria andMonth8In(List<Boolean> values) {
            addCriterion("month8 in", values, "month8");
            return (Criteria) this;
        }

        public Criteria andMonth8NotIn(List<Boolean> values) {
            addCriterion("month8 not in", values, "month8");
            return (Criteria) this;
        }

        public Criteria andMonth8Between(Boolean value1, Boolean value2) {
            addCriterion("month8 between", value1, value2, "month8");
            return (Criteria) this;
        }

        public Criteria andMonth8NotBetween(Boolean value1, Boolean value2) {
            addCriterion("month8 not between", value1, value2, "month8");
            return (Criteria) this;
        }

        public Criteria andMonth9IsNull() {
            addCriterion("month9 is null");
            return (Criteria) this;
        }

        public Criteria andMonth9IsNotNull() {
            addCriterion("month9 is not null");
            return (Criteria) this;
        }

        public Criteria andMonth9EqualTo(Boolean value) {
            addCriterion("month9 =", value, "month9");
            return (Criteria) this;
        }

        public Criteria andMonth9NotEqualTo(Boolean value) {
            addCriterion("month9 <>", value, "month9");
            return (Criteria) this;
        }

        public Criteria andMonth9GreaterThan(Boolean value) {
            addCriterion("month9 >", value, "month9");
            return (Criteria) this;
        }

        public Criteria andMonth9GreaterThanOrEqualTo(Boolean value) {
            addCriterion("month9 >=", value, "month9");
            return (Criteria) this;
        }

        public Criteria andMonth9LessThan(Boolean value) {
            addCriterion("month9 <", value, "month9");
            return (Criteria) this;
        }

        public Criteria andMonth9LessThanOrEqualTo(Boolean value) {
            addCriterion("month9 <=", value, "month9");
            return (Criteria) this;
        }

        public Criteria andMonth9In(List<Boolean> values) {
            addCriterion("month9 in", values, "month9");
            return (Criteria) this;
        }

        public Criteria andMonth9NotIn(List<Boolean> values) {
            addCriterion("month9 not in", values, "month9");
            return (Criteria) this;
        }

        public Criteria andMonth9Between(Boolean value1, Boolean value2) {
            addCriterion("month9 between", value1, value2, "month9");
            return (Criteria) this;
        }

        public Criteria andMonth9NotBetween(Boolean value1, Boolean value2) {
            addCriterion("month9 not between", value1, value2, "month9");
            return (Criteria) this;
        }

        public Criteria andMonth10IsNull() {
            addCriterion("month10 is null");
            return (Criteria) this;
        }

        public Criteria andMonth10IsNotNull() {
            addCriterion("month10 is not null");
            return (Criteria) this;
        }

        public Criteria andMonth10EqualTo(Boolean value) {
            addCriterion("month10 =", value, "month10");
            return (Criteria) this;
        }

        public Criteria andMonth10NotEqualTo(Boolean value) {
            addCriterion("month10 <>", value, "month10");
            return (Criteria) this;
        }

        public Criteria andMonth10GreaterThan(Boolean value) {
            addCriterion("month10 >", value, "month10");
            return (Criteria) this;
        }

        public Criteria andMonth10GreaterThanOrEqualTo(Boolean value) {
            addCriterion("month10 >=", value, "month10");
            return (Criteria) this;
        }

        public Criteria andMonth10LessThan(Boolean value) {
            addCriterion("month10 <", value, "month10");
            return (Criteria) this;
        }

        public Criteria andMonth10LessThanOrEqualTo(Boolean value) {
            addCriterion("month10 <=", value, "month10");
            return (Criteria) this;
        }

        public Criteria andMonth10In(List<Boolean> values) {
            addCriterion("month10 in", values, "month10");
            return (Criteria) this;
        }

        public Criteria andMonth10NotIn(List<Boolean> values) {
            addCriterion("month10 not in", values, "month10");
            return (Criteria) this;
        }

        public Criteria andMonth10Between(Boolean value1, Boolean value2) {
            addCriterion("month10 between", value1, value2, "month10");
            return (Criteria) this;
        }

        public Criteria andMonth10NotBetween(Boolean value1, Boolean value2) {
            addCriterion("month10 not between", value1, value2, "month10");
            return (Criteria) this;
        }

        public Criteria andMonth11IsNull() {
            addCriterion("month11 is null");
            return (Criteria) this;
        }

        public Criteria andMonth11IsNotNull() {
            addCriterion("month11 is not null");
            return (Criteria) this;
        }

        public Criteria andMonth11EqualTo(Boolean value) {
            addCriterion("month11 =", value, "month11");
            return (Criteria) this;
        }

        public Criteria andMonth11NotEqualTo(Boolean value) {
            addCriterion("month11 <>", value, "month11");
            return (Criteria) this;
        }

        public Criteria andMonth11GreaterThan(Boolean value) {
            addCriterion("month11 >", value, "month11");
            return (Criteria) this;
        }

        public Criteria andMonth11GreaterThanOrEqualTo(Boolean value) {
            addCriterion("month11 >=", value, "month11");
            return (Criteria) this;
        }

        public Criteria andMonth11LessThan(Boolean value) {
            addCriterion("month11 <", value, "month11");
            return (Criteria) this;
        }

        public Criteria andMonth11LessThanOrEqualTo(Boolean value) {
            addCriterion("month11 <=", value, "month11");
            return (Criteria) this;
        }

        public Criteria andMonth11In(List<Boolean> values) {
            addCriterion("month11 in", values, "month11");
            return (Criteria) this;
        }

        public Criteria andMonth11NotIn(List<Boolean> values) {
            addCriterion("month11 not in", values, "month11");
            return (Criteria) this;
        }

        public Criteria andMonth11Between(Boolean value1, Boolean value2) {
            addCriterion("month11 between", value1, value2, "month11");
            return (Criteria) this;
        }

        public Criteria andMonth11NotBetween(Boolean value1, Boolean value2) {
            addCriterion("month11 not between", value1, value2, "month11");
            return (Criteria) this;
        }

        public Criteria andMonth12IsNull() {
            addCriterion("month12 is null");
            return (Criteria) this;
        }

        public Criteria andMonth12IsNotNull() {
            addCriterion("month12 is not null");
            return (Criteria) this;
        }

        public Criteria andMonth12EqualTo(Boolean value) {
            addCriterion("month12 =", value, "month12");
            return (Criteria) this;
        }

        public Criteria andMonth12NotEqualTo(Boolean value) {
            addCriterion("month12 <>", value, "month12");
            return (Criteria) this;
        }

        public Criteria andMonth12GreaterThan(Boolean value) {
            addCriterion("month12 >", value, "month12");
            return (Criteria) this;
        }

        public Criteria andMonth12GreaterThanOrEqualTo(Boolean value) {
            addCriterion("month12 >=", value, "month12");
            return (Criteria) this;
        }

        public Criteria andMonth12LessThan(Boolean value) {
            addCriterion("month12 <", value, "month12");
            return (Criteria) this;
        }

        public Criteria andMonth12LessThanOrEqualTo(Boolean value) {
            addCriterion("month12 <=", value, "month12");
            return (Criteria) this;
        }

        public Criteria andMonth12In(List<Boolean> values) {
            addCriterion("month12 in", values, "month12");
            return (Criteria) this;
        }

        public Criteria andMonth12NotIn(List<Boolean> values) {
            addCriterion("month12 not in", values, "month12");
            return (Criteria) this;
        }

        public Criteria andMonth12Between(Boolean value1, Boolean value2) {
            addCriterion("month12 between", value1, value2, "month12");
            return (Criteria) this;
        }

        public Criteria andMonth12NotBetween(Boolean value1, Boolean value2) {
            addCriterion("month12 not between", value1, value2, "month12");
            return (Criteria) this;
        }

        public Criteria andCreationTimeIsNull() {
            addCriterion("creation_time is null");
            return (Criteria) this;
        }

        public Criteria andCreationTimeIsNotNull() {
            addCriterion("creation_time is not null");
            return (Criteria) this;
        }

        public Criteria andCreationTimeEqualTo(LocalDateTime value) {
            addCriterion("creation_time =", value, "creationTime");
            return (Criteria) this;
        }

        public Criteria andCreationTimeNotEqualTo(LocalDateTime value) {
            addCriterion("creation_time <>", value, "creationTime");
            return (Criteria) this;
        }

        public Criteria andCreationTimeGreaterThan(LocalDateTime value) {
            addCriterion("creation_time >", value, "creationTime");
            return (Criteria) this;
        }

        public Criteria andCreationTimeGreaterThanOrEqualTo(LocalDateTime value) {
            addCriterion("creation_time >=", value, "creationTime");
            return (Criteria) this;
        }

        public Criteria andCreationTimeLessThan(LocalDateTime value) {
            addCriterion("creation_time <", value, "creationTime");
            return (Criteria) this;
        }

        public Criteria andCreationTimeLessThanOrEqualTo(LocalDateTime value) {
            addCriterion("creation_time <=", value, "creationTime");
            return (Criteria) this;
        }

        public Criteria andCreationTimeIn(List<LocalDateTime> values) {
            addCriterion("creation_time in", values, "creationTime");
            return (Criteria) this;
        }

        public Criteria andCreationTimeNotIn(List<LocalDateTime> values) {
            addCriterion("creation_time not in", values, "creationTime");
            return (Criteria) this;
        }

        public Criteria andCreationTimeBetween(LocalDateTime value1, LocalDateTime value2) {
            addCriterion("creation_time between", value1, value2, "creationTime");
            return (Criteria) this;
        }

        public Criteria andCreationTimeNotBetween(LocalDateTime value1, LocalDateTime value2) {
            addCriterion("creation_time not between", value1, value2, "creationTime");
            return (Criteria) this;
        }

        public Criteria andCreateUsernameIsNull() {
            addCriterion("create_username is null");
            return (Criteria) this;
        }

        public Criteria andCreateUsernameIsNotNull() {
            addCriterion("create_username is not null");
            return (Criteria) this;
        }

        public Criteria andCreateUsernameEqualTo(String value) {
            addCriterion("create_username =", value, "createUsername");
            return (Criteria) this;
        }

        public Criteria andCreateUsernameNotEqualTo(String value) {
            addCriterion("create_username <>", value, "createUsername");
            return (Criteria) this;
        }

        public Criteria andCreateUsernameGreaterThan(String value) {
            addCriterion("create_username >", value, "createUsername");
            return (Criteria) this;
        }

        public Criteria andCreateUsernameGreaterThanOrEqualTo(String value) {
            addCriterion("create_username >=", value, "createUsername");
            return (Criteria) this;
        }

        public Criteria andCreateUsernameLessThan(String value) {
            addCriterion("create_username <", value, "createUsername");
            return (Criteria) this;
        }

        public Criteria andCreateUsernameLessThanOrEqualTo(String value) {
            addCriterion("create_username <=", value, "createUsername");
            return (Criteria) this;
        }

        public Criteria andCreateUsernameLike(String value) {
            addCriterion("create_username like", value, "createUsername");
            return (Criteria) this;
        }

        public Criteria andCreateUsernameNotLike(String value) {
            addCriterion("create_username not like", value, "createUsername");
            return (Criteria) this;
        }

        public Criteria andCreateUsernameIn(List<String> values) {
            addCriterion("create_username in", values, "createUsername");
            return (Criteria) this;
        }

        public Criteria andCreateUsernameNotIn(List<String> values) {
            addCriterion("create_username not in", values, "createUsername");
            return (Criteria) this;
        }

        public Criteria andCreateUsernameBetween(String value1, String value2) {
            addCriterion("create_username between", value1, value2, "createUsername");
            return (Criteria) this;
        }

        public Criteria andCreateUsernameNotBetween(String value1, String value2) {
            addCriterion("create_username not between", value1, value2, "createUsername");
            return (Criteria) this;
        }

        public Criteria andCreateUserIdIsNull() {
            addCriterion("create_user_id is null");
            return (Criteria) this;
        }

        public Criteria andCreateUserIdIsNotNull() {
            addCriterion("create_user_id is not null");
            return (Criteria) this;
        }

        public Criteria andCreateUserIdEqualTo(String value) {
            addCriterion("create_user_id =", value, "createUserId");
            return (Criteria) this;
        }

        public Criteria andCreateUserIdNotEqualTo(String value) {
            addCriterion("create_user_id <>", value, "createUserId");
            return (Criteria) this;
        }

        public Criteria andCreateUserIdGreaterThan(String value) {
            addCriterion("create_user_id >", value, "createUserId");
            return (Criteria) this;
        }

        public Criteria andCreateUserIdGreaterThanOrEqualTo(String value) {
            addCriterion("create_user_id >=", value, "createUserId");
            return (Criteria) this;
        }

        public Criteria andCreateUserIdLessThan(String value) {
            addCriterion("create_user_id <", value, "createUserId");
            return (Criteria) this;
        }

        public Criteria andCreateUserIdLessThanOrEqualTo(String value) {
            addCriterion("create_user_id <=", value, "createUserId");
            return (Criteria) this;
        }

        public Criteria andCreateUserIdLike(String value) {
            addCriterion("create_user_id like", value, "createUserId");
            return (Criteria) this;
        }

        public Criteria andCreateUserIdNotLike(String value) {
            addCriterion("create_user_id not like", value, "createUserId");
            return (Criteria) this;
        }

        public Criteria andCreateUserIdIn(List<String> values) {
            addCriterion("create_user_id in", values, "createUserId");
            return (Criteria) this;
        }

        public Criteria andCreateUserIdNotIn(List<String> values) {
            addCriterion("create_user_id not in", values, "createUserId");
            return (Criteria) this;
        }

        public Criteria andCreateUserIdBetween(String value1, String value2) {
            addCriterion("create_user_id between", value1, value2, "createUserId");
            return (Criteria) this;
        }

        public Criteria andCreateUserIdNotBetween(String value1, String value2) {
            addCriterion("create_user_id not between", value1, value2, "createUserId");
            return (Criteria) this;
        }

        public Criteria andLastUpdateTimeIsNull() {
            addCriterion("last_update_time is null");
            return (Criteria) this;
        }

        public Criteria andLastUpdateTimeIsNotNull() {
            addCriterion("last_update_time is not null");
            return (Criteria) this;
        }

        public Criteria andLastUpdateTimeEqualTo(LocalDateTime value) {
            addCriterion("last_update_time =", value, "lastUpdateTime");
            return (Criteria) this;
        }

        public Criteria andLastUpdateTimeNotEqualTo(LocalDateTime value) {
            addCriterion("last_update_time <>", value, "lastUpdateTime");
            return (Criteria) this;
        }

        public Criteria andLastUpdateTimeGreaterThan(LocalDateTime value) {
            addCriterion("last_update_time >", value, "lastUpdateTime");
            return (Criteria) this;
        }

        public Criteria andLastUpdateTimeGreaterThanOrEqualTo(LocalDateTime value) {
            addCriterion("last_update_time >=", value, "lastUpdateTime");
            return (Criteria) this;
        }

        public Criteria andLastUpdateTimeLessThan(LocalDateTime value) {
            addCriterion("last_update_time <", value, "lastUpdateTime");
            return (Criteria) this;
        }

        public Criteria andLastUpdateTimeLessThanOrEqualTo(LocalDateTime value) {
            addCriterion("last_update_time <=", value, "lastUpdateTime");
            return (Criteria) this;
        }

        public Criteria andLastUpdateTimeIn(List<LocalDateTime> values) {
            addCriterion("last_update_time in", values, "lastUpdateTime");
            return (Criteria) this;
        }

        public Criteria andLastUpdateTimeNotIn(List<LocalDateTime> values) {
            addCriterion("last_update_time not in", values, "lastUpdateTime");
            return (Criteria) this;
        }

        public Criteria andLastUpdateTimeBetween(LocalDateTime value1, LocalDateTime value2) {
            addCriterion("last_update_time between", value1, value2, "lastUpdateTime");
            return (Criteria) this;
        }

        public Criteria andLastUpdateTimeNotBetween(LocalDateTime value1, LocalDateTime value2) {
            addCriterion("last_update_time not between", value1, value2, "lastUpdateTime");
            return (Criteria) this;
        }

        public Criteria andLastUpdateUsernameIsNull() {
            addCriterion("last_update_username is null");
            return (Criteria) this;
        }

        public Criteria andLastUpdateUsernameIsNotNull() {
            addCriterion("last_update_username is not null");
            return (Criteria) this;
        }

        public Criteria andLastUpdateUsernameEqualTo(String value) {
            addCriterion("last_update_username =", value, "lastUpdateUsername");
            return (Criteria) this;
        }

        public Criteria andLastUpdateUsernameNotEqualTo(String value) {
            addCriterion("last_update_username <>", value, "lastUpdateUsername");
            return (Criteria) this;
        }

        public Criteria andLastUpdateUsernameGreaterThan(String value) {
            addCriterion("last_update_username >", value, "lastUpdateUsername");
            return (Criteria) this;
        }

        public Criteria andLastUpdateUsernameGreaterThanOrEqualTo(String value) {
            addCriterion("last_update_username >=", value, "lastUpdateUsername");
            return (Criteria) this;
        }

        public Criteria andLastUpdateUsernameLessThan(String value) {
            addCriterion("last_update_username <", value, "lastUpdateUsername");
            return (Criteria) this;
        }

        public Criteria andLastUpdateUsernameLessThanOrEqualTo(String value) {
            addCriterion("last_update_username <=", value, "lastUpdateUsername");
            return (Criteria) this;
        }

        public Criteria andLastUpdateUsernameLike(String value) {
            addCriterion("last_update_username like", value, "lastUpdateUsername");
            return (Criteria) this;
        }

        public Criteria andLastUpdateUsernameNotLike(String value) {
            addCriterion("last_update_username not like", value, "lastUpdateUsername");
            return (Criteria) this;
        }

        public Criteria andLastUpdateUsernameIn(List<String> values) {
            addCriterion("last_update_username in", values, "lastUpdateUsername");
            return (Criteria) this;
        }

        public Criteria andLastUpdateUsernameNotIn(List<String> values) {
            addCriterion("last_update_username not in", values, "lastUpdateUsername");
            return (Criteria) this;
        }

        public Criteria andLastUpdateUsernameBetween(String value1, String value2) {
            addCriterion("last_update_username between", value1, value2, "lastUpdateUsername");
            return (Criteria) this;
        }

        public Criteria andLastUpdateUsernameNotBetween(String value1, String value2) {
            addCriterion("last_update_username not between", value1, value2, "lastUpdateUsername");
            return (Criteria) this;
        }

        public Criteria andLastUpdateUserIdIsNull() {
            addCriterion("last_update_user_id is null");
            return (Criteria) this;
        }

        public Criteria andLastUpdateUserIdIsNotNull() {
            addCriterion("last_update_user_id is not null");
            return (Criteria) this;
        }

        public Criteria andLastUpdateUserIdEqualTo(String value) {
            addCriterion("last_update_user_id =", value, "lastUpdateUserId");
            return (Criteria) this;
        }

        public Criteria andLastUpdateUserIdNotEqualTo(String value) {
            addCriterion("last_update_user_id <>", value, "lastUpdateUserId");
            return (Criteria) this;
        }

        public Criteria andLastUpdateUserIdGreaterThan(String value) {
            addCriterion("last_update_user_id >", value, "lastUpdateUserId");
            return (Criteria) this;
        }

        public Criteria andLastUpdateUserIdGreaterThanOrEqualTo(String value) {
            addCriterion("last_update_user_id >=", value, "lastUpdateUserId");
            return (Criteria) this;
        }

        public Criteria andLastUpdateUserIdLessThan(String value) {
            addCriterion("last_update_user_id <", value, "lastUpdateUserId");
            return (Criteria) this;
        }

        public Criteria andLastUpdateUserIdLessThanOrEqualTo(String value) {
            addCriterion("last_update_user_id <=", value, "lastUpdateUserId");
            return (Criteria) this;
        }

        public Criteria andLastUpdateUserIdLike(String value) {
            addCriterion("last_update_user_id like", value, "lastUpdateUserId");
            return (Criteria) this;
        }

        public Criteria andLastUpdateUserIdNotLike(String value) {
            addCriterion("last_update_user_id not like", value, "lastUpdateUserId");
            return (Criteria) this;
        }

        public Criteria andLastUpdateUserIdIn(List<String> values) {
            addCriterion("last_update_user_id in", values, "lastUpdateUserId");
            return (Criteria) this;
        }

        public Criteria andLastUpdateUserIdNotIn(List<String> values) {
            addCriterion("last_update_user_id not in", values, "lastUpdateUserId");
            return (Criteria) this;
        }

        public Criteria andLastUpdateUserIdBetween(String value1, String value2) {
            addCriterion("last_update_user_id between", value1, value2, "lastUpdateUserId");
            return (Criteria) this;
        }

        public Criteria andLastUpdateUserIdNotBetween(String value1, String value2) {
            addCriterion("last_update_user_id not between", value1, value2, "lastUpdateUserId");
            return (Criteria) this;
        }

        public Criteria andLastUpdateVersionIsNull() {
            addCriterion("last_update_version is null");
            return (Criteria) this;
        }

        public Criteria andLastUpdateVersionIsNotNull() {
            addCriterion("last_update_version is not null");
            return (Criteria) this;
        }

        public Criteria andLastUpdateVersionEqualTo(Integer value) {
            addCriterion("last_update_version =", value, "lastUpdateVersion");
            return (Criteria) this;
        }

        public Criteria andLastUpdateVersionNotEqualTo(Integer value) {
            addCriterion("last_update_version <>", value, "lastUpdateVersion");
            return (Criteria) this;
        }

        public Criteria andLastUpdateVersionGreaterThan(Integer value) {
            addCriterion("last_update_version >", value, "lastUpdateVersion");
            return (Criteria) this;
        }

        public Criteria andLastUpdateVersionGreaterThanOrEqualTo(Integer value) {
            addCriterion("last_update_version >=", value, "lastUpdateVersion");
            return (Criteria) this;
        }

        public Criteria andLastUpdateVersionLessThan(Integer value) {
            addCriterion("last_update_version <", value, "lastUpdateVersion");
            return (Criteria) this;
        }

        public Criteria andLastUpdateVersionLessThanOrEqualTo(Integer value) {
            addCriterion("last_update_version <=", value, "lastUpdateVersion");
            return (Criteria) this;
        }

        public Criteria andLastUpdateVersionIn(List<Integer> values) {
            addCriterion("last_update_version in", values, "lastUpdateVersion");
            return (Criteria) this;
        }

        public Criteria andLastUpdateVersionNotIn(List<Integer> values) {
            addCriterion("last_update_version not in", values, "lastUpdateVersion");
            return (Criteria) this;
        }

        public Criteria andLastUpdateVersionBetween(Integer value1, Integer value2) {
            addCriterion("last_update_version between", value1, value2, "lastUpdateVersion");
            return (Criteria) this;
        }

        public Criteria andLastUpdateVersionNotBetween(Integer value1, Integer value2) {
            addCriterion("last_update_version not between", value1, value2, "lastUpdateVersion");
            return (Criteria) this;
        }

        public Criteria andIsDeletedIsNull() {
            addCriterion("is_deleted is null");
            return (Criteria) this;
        }

        public Criteria andIsDeletedIsNotNull() {
            addCriterion("is_deleted is not null");
            return (Criteria) this;
        }

        public Criteria andIsDeletedEqualTo(Boolean value) {
            addCriterion("is_deleted =", value, "isDeleted");
            return (Criteria) this;
        }

        public Criteria andIsDeletedNotEqualTo(Boolean value) {
            addCriterion("is_deleted <>", value, "isDeleted");
            return (Criteria) this;
        }

        public Criteria andIsDeletedGreaterThan(Boolean value) {
            addCriterion("is_deleted >", value, "isDeleted");
            return (Criteria) this;
        }

        public Criteria andIsDeletedGreaterThanOrEqualTo(Boolean value) {
            addCriterion("is_deleted >=", value, "isDeleted");
            return (Criteria) this;
        }

        public Criteria andIsDeletedLessThan(Boolean value) {
            addCriterion("is_deleted <", value, "isDeleted");
            return (Criteria) this;
        }

        public Criteria andIsDeletedLessThanOrEqualTo(Boolean value) {
            addCriterion("is_deleted <=", value, "isDeleted");
            return (Criteria) this;
        }

        public Criteria andIsDeletedIn(List<Boolean> values) {
            addCriterion("is_deleted in", values, "isDeleted");
            return (Criteria) this;
        }

        public Criteria andIsDeletedNotIn(List<Boolean> values) {
            addCriterion("is_deleted not in", values, "isDeleted");
            return (Criteria) this;
        }

        public Criteria andIsDeletedBetween(Boolean value1, Boolean value2) {
            addCriterion("is_deleted between", value1, value2, "isDeleted");
            return (Criteria) this;
        }

        public Criteria andIsDeletedNotBetween(Boolean value1, Boolean value2) {
            addCriterion("is_deleted not between", value1, value2, "isDeleted");
            return (Criteria) this;
        }
    }

    /**
     * This class was generated by MyBatis Generator.
     * This class corresponds to the database table t_submission_month_config
     *
     * @mbg.generated do_not_delete_during_merge Thu Apr 18 11:23:09 HKT 2024
     */
    public static class Criteria extends GeneratedCriteria {
        protected Criteria() {
            super();
        }
    }

    /**
     * This class was generated by MyBatis Generator.
     * This class corresponds to the database table t_submission_month_config
     *
     * @mbg.generated Thu Apr 18 11:23:09 HKT 2024
     */
    public static class Criterion {
        private String condition;

        private Object value;

        private Object secondValue;

        private boolean noValue;

        private boolean singleValue;

        private boolean betweenValue;

        private boolean listValue;

        private String typeHandler;

        public String getCondition() {
            return condition;
        }

        public Object getValue() {
            return value;
        }

        public Object getSecondValue() {
            return secondValue;
        }

        public boolean isNoValue() {
            return noValue;
        }

        public boolean isSingleValue() {
            return singleValue;
        }

        public boolean isBetweenValue() {
            return betweenValue;
        }

        public boolean isListValue() {
            return listValue;
        }

        public String getTypeHandler() {
            return typeHandler;
        }

        protected Criterion(String condition) {
            super();
            this.condition = condition;
            this.typeHandler = null;
            this.noValue = true;
        }

        protected Criterion(String condition, Object value, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.typeHandler = typeHandler;
            if (value instanceof List<?>) {
                this.listValue = true;
            } else {
                this.singleValue = true;
            }
        }

        protected Criterion(String condition, Object value) {
            this(condition, value, null);
        }

        protected Criterion(String condition, Object value, Object secondValue, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.secondValue = secondValue;
            this.typeHandler = typeHandler;
            this.betweenValue = true;
        }

        protected Criterion(String condition, Object value, Object secondValue) {
            this(condition, value, secondValue, null);
        }
    }
}