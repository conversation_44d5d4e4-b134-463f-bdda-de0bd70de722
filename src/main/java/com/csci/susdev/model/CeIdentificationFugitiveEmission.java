package com.csci.susdev.model;

import java.time.LocalDateTime;

public class CeIdentificationFugitiveEmission {
    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_ce_identification_fugitive_emission.id
     *
     * @mbg.generated Wed Apr 02 16:23:01 CST 2025
     */
    private String id;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_ce_identification_fugitive_emission.ce_identification_head_id
     *
     * @mbg.generated Wed Apr 02 16:23:01 CST 2025
     */
    private String ceIdentificationHeadId;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_ce_identification_fugitive_emission.fugitive_module
     *
     * @mbg.generated Wed Apr 02 16:23:01 CST 2025
     */
    private String fugitiveModule;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_ce_identification_fugitive_emission.confirmation_item_one
     *
     * @mbg.generated Wed Apr 02 16:23:01 CST 2025
     */
    private String confirmationItemOne;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_ce_identification_fugitive_emission.confirmation_item_two
     *
     * @mbg.generated Wed Apr 02 16:23:01 CST 2025
     */
    private String confirmationItemTwo;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_ce_identification_fugitive_emission.confirmation_item_three
     *
     * @mbg.generated Wed Apr 02 16:23:01 CST 2025
     */
    private String confirmationItemThree;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_ce_identification_fugitive_emission.instructions 
     *
     * @mbg.generated Wed Apr 02 16:23:01 CST 2025
     */
    private String instructions;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_ce_identification_fugitive_emission.input_value
     *
     * @mbg.generated Wed Apr 02 16:23:01 CST 2025
     */
    private String inputValue;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_ce_identification_fugitive_emission.seq
     *
     * @mbg.generated Wed Apr 02 16:23:01 CST 2025
     */
    private Integer seq;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_ce_identification_fugitive_emission.creation_time
     *
     * @mbg.generated Wed Apr 02 16:23:01 CST 2025
     */
    private LocalDateTime creationTime;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_ce_identification_fugitive_emission.create_username
     *
     * @mbg.generated Wed Apr 02 16:23:01 CST 2025
     */
    private String createUsername;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_ce_identification_fugitive_emission.create_user_id
     *
     * @mbg.generated Wed Apr 02 16:23:01 CST 2025
     */
    private String createUserId;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_ce_identification_fugitive_emission.last_update_time
     *
     * @mbg.generated Wed Apr 02 16:23:01 CST 2025
     */
    private LocalDateTime lastUpdateTime;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_ce_identification_fugitive_emission.last_update_username
     *
     * @mbg.generated Wed Apr 02 16:23:01 CST 2025
     */
    private String lastUpdateUsername;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_ce_identification_fugitive_emission.last_update_user_id
     *
     * @mbg.generated Wed Apr 02 16:23:01 CST 2025
     */
    private String lastUpdateUserId;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_ce_identification_fugitive_emission.last_update_version
     *
     * @mbg.generated Wed Apr 02 16:23:01 CST 2025
     */
    private Integer lastUpdateVersion;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_ce_identification_fugitive_emission.is_deleted
     *
     * @mbg.generated Wed Apr 02 16:23:01 CST 2025
     */
    private Boolean isDeleted;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_ce_identification_fugitive_emission.control_col
     *
     * @mbg.generated Wed Apr 02 16:23:01 CST 2025
     */
    private String controlCol;

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column t_ce_identification_fugitive_emission.id
     *
     * @return the value of t_ce_identification_fugitive_emission.id
     *
     * @mbg.generated Wed Apr 02 16:23:01 CST 2025
     */
    public String getId() {
        return id;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column t_ce_identification_fugitive_emission.id
     *
     * @param id the value for t_ce_identification_fugitive_emission.id
     *
     * @mbg.generated Wed Apr 02 16:23:01 CST 2025
     */
    public void setId(String id) {
        this.id = id == null ? null : id.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column t_ce_identification_fugitive_emission.ce_identification_head_id
     *
     * @return the value of t_ce_identification_fugitive_emission.ce_identification_head_id
     *
     * @mbg.generated Wed Apr 02 16:23:01 CST 2025
     */
    public String getCeIdentificationHeadId() {
        return ceIdentificationHeadId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column t_ce_identification_fugitive_emission.ce_identification_head_id
     *
     * @param ceIdentificationHeadId the value for t_ce_identification_fugitive_emission.ce_identification_head_id
     *
     * @mbg.generated Wed Apr 02 16:23:01 CST 2025
     */
    public void setCeIdentificationHeadId(String ceIdentificationHeadId) {
        this.ceIdentificationHeadId = ceIdentificationHeadId == null ? null : ceIdentificationHeadId.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column t_ce_identification_fugitive_emission.fugitive_module
     *
     * @return the value of t_ce_identification_fugitive_emission.fugitive_module
     *
     * @mbg.generated Wed Apr 02 16:23:01 CST 2025
     */
    public String getFugitiveModule() {
        return fugitiveModule;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column t_ce_identification_fugitive_emission.fugitive_module
     *
     * @param fugitiveModule the value for t_ce_identification_fugitive_emission.fugitive_module
     *
     * @mbg.generated Wed Apr 02 16:23:01 CST 2025
     */
    public void setFugitiveModule(String fugitiveModule) {
        this.fugitiveModule = fugitiveModule == null ? null : fugitiveModule.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column t_ce_identification_fugitive_emission.confirmation_item_one
     *
     * @return the value of t_ce_identification_fugitive_emission.confirmation_item_one
     *
     * @mbg.generated Wed Apr 02 16:23:01 CST 2025
     */
    public String getConfirmationItemOne() {
        return confirmationItemOne;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column t_ce_identification_fugitive_emission.confirmation_item_one
     *
     * @param confirmationItemOne the value for t_ce_identification_fugitive_emission.confirmation_item_one
     *
     * @mbg.generated Wed Apr 02 16:23:01 CST 2025
     */
    public void setConfirmationItemOne(String confirmationItemOne) {
        this.confirmationItemOne = confirmationItemOne == null ? null : confirmationItemOne.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column t_ce_identification_fugitive_emission.confirmation_item_two
     *
     * @return the value of t_ce_identification_fugitive_emission.confirmation_item_two
     *
     * @mbg.generated Wed Apr 02 16:23:01 CST 2025
     */
    public String getConfirmationItemTwo() {
        return confirmationItemTwo;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column t_ce_identification_fugitive_emission.confirmation_item_two
     *
     * @param confirmationItemTwo the value for t_ce_identification_fugitive_emission.confirmation_item_two
     *
     * @mbg.generated Wed Apr 02 16:23:01 CST 2025
     */
    public void setConfirmationItemTwo(String confirmationItemTwo) {
        this.confirmationItemTwo = confirmationItemTwo == null ? null : confirmationItemTwo.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column t_ce_identification_fugitive_emission.confirmation_item_three
     *
     * @return the value of t_ce_identification_fugitive_emission.confirmation_item_three
     *
     * @mbg.generated Wed Apr 02 16:23:01 CST 2025
     */
    public String getConfirmationItemThree() {
        return confirmationItemThree;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column t_ce_identification_fugitive_emission.confirmation_item_three
     *
     * @param confirmationItemThree the value for t_ce_identification_fugitive_emission.confirmation_item_three
     *
     * @mbg.generated Wed Apr 02 16:23:01 CST 2025
     */
    public void setConfirmationItemThree(String confirmationItemThree) {
        this.confirmationItemThree = confirmationItemThree == null ? null : confirmationItemThree.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column t_ce_identification_fugitive_emission.instructions 
     *
     * @return the value of t_ce_identification_fugitive_emission.instructions 
     *
     * @mbg.generated Wed Apr 02 16:23:01 CST 2025
     */
    public String getInstructions() {
        return instructions;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column t_ce_identification_fugitive_emission.instructions 
     *
     * @param instructions the value for t_ce_identification_fugitive_emission.instructions 
     *
     * @mbg.generated Wed Apr 02 16:23:01 CST 2025
     */
    public void setInstructions(String instructions) {
        this.instructions = instructions == null ? null : instructions.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column t_ce_identification_fugitive_emission.input_value
     *
     * @return the value of t_ce_identification_fugitive_emission.input_value
     *
     * @mbg.generated Wed Apr 02 16:23:01 CST 2025
     */
    public String getInputValue() {
        return inputValue;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column t_ce_identification_fugitive_emission.input_value
     *
     * @param inputValue the value for t_ce_identification_fugitive_emission.input_value
     *
     * @mbg.generated Wed Apr 02 16:23:01 CST 2025
     */
    public void setInputValue(String inputValue) {
        this.inputValue = inputValue == null ? null : inputValue.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column t_ce_identification_fugitive_emission.seq
     *
     * @return the value of t_ce_identification_fugitive_emission.seq
     *
     * @mbg.generated Wed Apr 02 16:23:01 CST 2025
     */
    public Integer getSeq() {
        return seq;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column t_ce_identification_fugitive_emission.seq
     *
     * @param seq the value for t_ce_identification_fugitive_emission.seq
     *
     * @mbg.generated Wed Apr 02 16:23:01 CST 2025
     */
    public void setSeq(Integer seq) {
        this.seq = seq;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column t_ce_identification_fugitive_emission.creation_time
     *
     * @return the value of t_ce_identification_fugitive_emission.creation_time
     *
     * @mbg.generated Wed Apr 02 16:23:01 CST 2025
     */
    public LocalDateTime getCreationTime() {
        return creationTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column t_ce_identification_fugitive_emission.creation_time
     *
     * @param creationTime the value for t_ce_identification_fugitive_emission.creation_time
     *
     * @mbg.generated Wed Apr 02 16:23:01 CST 2025
     */
    public void setCreationTime(LocalDateTime creationTime) {
        this.creationTime = creationTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column t_ce_identification_fugitive_emission.create_username
     *
     * @return the value of t_ce_identification_fugitive_emission.create_username
     *
     * @mbg.generated Wed Apr 02 16:23:01 CST 2025
     */
    public String getCreateUsername() {
        return createUsername;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column t_ce_identification_fugitive_emission.create_username
     *
     * @param createUsername the value for t_ce_identification_fugitive_emission.create_username
     *
     * @mbg.generated Wed Apr 02 16:23:01 CST 2025
     */
    public void setCreateUsername(String createUsername) {
        this.createUsername = createUsername == null ? null : createUsername.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column t_ce_identification_fugitive_emission.create_user_id
     *
     * @return the value of t_ce_identification_fugitive_emission.create_user_id
     *
     * @mbg.generated Wed Apr 02 16:23:01 CST 2025
     */
    public String getCreateUserId() {
        return createUserId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column t_ce_identification_fugitive_emission.create_user_id
     *
     * @param createUserId the value for t_ce_identification_fugitive_emission.create_user_id
     *
     * @mbg.generated Wed Apr 02 16:23:01 CST 2025
     */
    public void setCreateUserId(String createUserId) {
        this.createUserId = createUserId == null ? null : createUserId.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column t_ce_identification_fugitive_emission.last_update_time
     *
     * @return the value of t_ce_identification_fugitive_emission.last_update_time
     *
     * @mbg.generated Wed Apr 02 16:23:01 CST 2025
     */
    public LocalDateTime getLastUpdateTime() {
        return lastUpdateTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column t_ce_identification_fugitive_emission.last_update_time
     *
     * @param lastUpdateTime the value for t_ce_identification_fugitive_emission.last_update_time
     *
     * @mbg.generated Wed Apr 02 16:23:01 CST 2025
     */
    public void setLastUpdateTime(LocalDateTime lastUpdateTime) {
        this.lastUpdateTime = lastUpdateTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column t_ce_identification_fugitive_emission.last_update_username
     *
     * @return the value of t_ce_identification_fugitive_emission.last_update_username
     *
     * @mbg.generated Wed Apr 02 16:23:01 CST 2025
     */
    public String getLastUpdateUsername() {
        return lastUpdateUsername;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column t_ce_identification_fugitive_emission.last_update_username
     *
     * @param lastUpdateUsername the value for t_ce_identification_fugitive_emission.last_update_username
     *
     * @mbg.generated Wed Apr 02 16:23:01 CST 2025
     */
    public void setLastUpdateUsername(String lastUpdateUsername) {
        this.lastUpdateUsername = lastUpdateUsername == null ? null : lastUpdateUsername.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column t_ce_identification_fugitive_emission.last_update_user_id
     *
     * @return the value of t_ce_identification_fugitive_emission.last_update_user_id
     *
     * @mbg.generated Wed Apr 02 16:23:01 CST 2025
     */
    public String getLastUpdateUserId() {
        return lastUpdateUserId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column t_ce_identification_fugitive_emission.last_update_user_id
     *
     * @param lastUpdateUserId the value for t_ce_identification_fugitive_emission.last_update_user_id
     *
     * @mbg.generated Wed Apr 02 16:23:01 CST 2025
     */
    public void setLastUpdateUserId(String lastUpdateUserId) {
        this.lastUpdateUserId = lastUpdateUserId == null ? null : lastUpdateUserId.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column t_ce_identification_fugitive_emission.last_update_version
     *
     * @return the value of t_ce_identification_fugitive_emission.last_update_version
     *
     * @mbg.generated Wed Apr 02 16:23:01 CST 2025
     */
    public Integer getLastUpdateVersion() {
        return lastUpdateVersion;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column t_ce_identification_fugitive_emission.last_update_version
     *
     * @param lastUpdateVersion the value for t_ce_identification_fugitive_emission.last_update_version
     *
     * @mbg.generated Wed Apr 02 16:23:01 CST 2025
     */
    public void setLastUpdateVersion(Integer lastUpdateVersion) {
        this.lastUpdateVersion = lastUpdateVersion;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column t_ce_identification_fugitive_emission.is_deleted
     *
     * @return the value of t_ce_identification_fugitive_emission.is_deleted
     *
     * @mbg.generated Wed Apr 02 16:23:01 CST 2025
     */
    public Boolean getIsDeleted() {
        return isDeleted;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column t_ce_identification_fugitive_emission.is_deleted
     *
     * @param isDeleted the value for t_ce_identification_fugitive_emission.is_deleted
     *
     * @mbg.generated Wed Apr 02 16:23:01 CST 2025
     */
    public void setIsDeleted(Boolean isDeleted) {
        this.isDeleted = isDeleted;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column t_ce_identification_fugitive_emission.control_col
     *
     * @return the value of t_ce_identification_fugitive_emission.control_col
     *
     * @mbg.generated Wed Apr 02 16:23:01 CST 2025
     */
    public String getControlCol() {
        return controlCol;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column t_ce_identification_fugitive_emission.control_col
     *
     * @param controlCol the value for t_ce_identification_fugitive_emission.control_col
     *
     * @mbg.generated Wed Apr 02 16:23:01 CST 2025
     */
    public void setControlCol(String controlCol) {
        this.controlCol = controlCol == null ? null : controlCol.trim();
    }
}