package com.csci.susdev.model;

import java.time.LocalDateTime;

public class CeIdentificationRunawayEmission {
    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_ce_identification_runaway_emission.id
     *
     * @mbg.generated Tue Apr 01 14:16:37 CST 2025
     */
    private String id;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_ce_identification_runaway_emission.ce_identification_head_id
     *
     * @mbg.generated Tue Apr 01 14:16:37 CST 2025
     */
    private String ceIdentificationHeadId;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_ce_identification_runaway_emission.is_own_septic_tank
     *
     * @mbg.generated Tue Apr 01 14:16:37 CST 2025
     */
    private String isOwnSepticTank;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_ce_identification_runaway_emission.regular_employee_num
     *
     * @mbg.generated Tue Apr 01 14:16:37 CST 2025
     */
    private String regularEmployeeNum;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_ce_identification_runaway_emission.average_work_day_year 
     *
     * @mbg.generated Tue Apr 01 14:16:37 CST 2025
     */
    private String averageWorkDayYear;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_ce_identification_runaway_emission.average_daily_commuting
     *
     * @mbg.generated Tue Apr 01 14:16:37 CST 2025
     */
    private String averageDailyCommuting;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_ce_identification_runaway_emission.average_daily_accommodation
     *
     * @mbg.generated Tue Apr 01 14:16:37 CST 2025
     */
    private String averageDailyAccommodation;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_ce_identification_runaway_emission.aircondition_refrigerant_type
     *
     * @mbg.generated Tue Apr 01 14:16:37 CST 2025
     */
    private String airconditionRefrigerantType;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_ce_identification_runaway_emission.aircondition_refrigerant_charge_amount
     *
     * @mbg.generated Tue Apr 01 14:16:37 CST 2025
     */
    private String airconditionRefrigerantChargeAmount;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_ce_identification_runaway_emission.aircondition_take_photo
     *
     * @mbg.generated Tue Apr 01 14:16:37 CST 2025
     */
    private String airconditionTakePhoto;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_ce_identification_runaway_emission.fridge_refrigerant_type
     *
     * @mbg.generated Tue Apr 01 14:16:37 CST 2025
     */
    private String fridgeRefrigerantType;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_ce_identification_runaway_emission.fridge_refrigerant_charge_amount
     *
     * @mbg.generated Tue Apr 01 14:16:37 CST 2025
     */
    private String fridgeRefrigerantChargeAmount;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_ce_identification_runaway_emission.fridge_take_photo
     *
     * @mbg.generated Tue Apr 01 14:16:37 CST 2025
     */
    private String fridgeTakePhoto;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_ce_identification_runaway_emission.service_car_refrigerant_type
     *
     * @mbg.generated Tue Apr 01 14:16:37 CST 2025
     */
    private String serviceCarRefrigerantType;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_ce_identification_runaway_emission.service_car_refrigerant_charge_amount
     *
     * @mbg.generated Tue Apr 01 14:16:37 CST 2025
     */
    private String serviceCarRefrigerantChargeAmount;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_ce_identification_runaway_emission.service_car_take_photo
     *
     * @mbg.generated Tue Apr 01 14:16:37 CST 2025
     */
    private String serviceCarTakePhoto;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_ce_identification_runaway_emission.creation_time
     *
     * @mbg.generated Tue Apr 01 14:16:37 CST 2025
     */
    private LocalDateTime creationTime;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_ce_identification_runaway_emission.create_username
     *
     * @mbg.generated Tue Apr 01 14:16:37 CST 2025
     */
    private String createUsername;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_ce_identification_runaway_emission.create_user_id
     *
     * @mbg.generated Tue Apr 01 14:16:37 CST 2025
     */
    private String createUserId;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_ce_identification_runaway_emission.last_update_time
     *
     * @mbg.generated Tue Apr 01 14:16:37 CST 2025
     */
    private LocalDateTime lastUpdateTime;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_ce_identification_runaway_emission.last_update_username
     *
     * @mbg.generated Tue Apr 01 14:16:37 CST 2025
     */
    private String lastUpdateUsername;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_ce_identification_runaway_emission.last_update_user_id
     *
     * @mbg.generated Tue Apr 01 14:16:37 CST 2025
     */
    private String lastUpdateUserId;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_ce_identification_runaway_emission.last_update_version
     *
     * @mbg.generated Tue Apr 01 14:16:37 CST 2025
     */
    private Integer lastUpdateVersion;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_ce_identification_runaway_emission.is_deleted
     *
     * @mbg.generated Tue Apr 01 14:16:37 CST 2025
     */
    private Boolean isDeleted;

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column t_ce_identification_runaway_emission.id
     *
     * @return the value of t_ce_identification_runaway_emission.id
     *
     * @mbg.generated Tue Apr 01 14:16:37 CST 2025
     */
    public String getId() {
        return id;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column t_ce_identification_runaway_emission.id
     *
     * @param id the value for t_ce_identification_runaway_emission.id
     *
     * @mbg.generated Tue Apr 01 14:16:37 CST 2025
     */
    public void setId(String id) {
        this.id = id == null ? null : id.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column t_ce_identification_runaway_emission.ce_identification_head_id
     *
     * @return the value of t_ce_identification_runaway_emission.ce_identification_head_id
     *
     * @mbg.generated Tue Apr 01 14:16:37 CST 2025
     */
    public String getCeIdentificationHeadId() {
        return ceIdentificationHeadId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column t_ce_identification_runaway_emission.ce_identification_head_id
     *
     * @param ceIdentificationHeadId the value for t_ce_identification_runaway_emission.ce_identification_head_id
     *
     * @mbg.generated Tue Apr 01 14:16:37 CST 2025
     */
    public void setCeIdentificationHeadId(String ceIdentificationHeadId) {
        this.ceIdentificationHeadId = ceIdentificationHeadId == null ? null : ceIdentificationHeadId.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column t_ce_identification_runaway_emission.is_own_septic_tank
     *
     * @return the value of t_ce_identification_runaway_emission.is_own_septic_tank
     *
     * @mbg.generated Tue Apr 01 14:16:37 CST 2025
     */
    public String getIsOwnSepticTank() {
        return isOwnSepticTank;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column t_ce_identification_runaway_emission.is_own_septic_tank
     *
     * @param isOwnSepticTank the value for t_ce_identification_runaway_emission.is_own_septic_tank
     *
     * @mbg.generated Tue Apr 01 14:16:37 CST 2025
     */
    public void setIsOwnSepticTank(String isOwnSepticTank) {
        this.isOwnSepticTank = isOwnSepticTank == null ? null : isOwnSepticTank.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column t_ce_identification_runaway_emission.regular_employee_num
     *
     * @return the value of t_ce_identification_runaway_emission.regular_employee_num
     *
     * @mbg.generated Tue Apr 01 14:16:37 CST 2025
     */
    public String getRegularEmployeeNum() {
        return regularEmployeeNum;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column t_ce_identification_runaway_emission.regular_employee_num
     *
     * @param regularEmployeeNum the value for t_ce_identification_runaway_emission.regular_employee_num
     *
     * @mbg.generated Tue Apr 01 14:16:37 CST 2025
     */
    public void setRegularEmployeeNum(String regularEmployeeNum) {
        this.regularEmployeeNum = regularEmployeeNum == null ? null : regularEmployeeNum.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column t_ce_identification_runaway_emission.average_work_day_year 
     *
     * @return the value of t_ce_identification_runaway_emission.average_work_day_year 
     *
     * @mbg.generated Tue Apr 01 14:16:37 CST 2025
     */
    public String getAverageWorkDayYear() {
        return averageWorkDayYear;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column t_ce_identification_runaway_emission.average_work_day_year 
     *
     * @param averageWorkDayYear the value for t_ce_identification_runaway_emission.average_work_day_year 
     *
     * @mbg.generated Tue Apr 01 14:16:37 CST 2025
     */
    public void setAverageWorkDayYear(String averageWorkDayYear) {
        this.averageWorkDayYear = averageWorkDayYear == null ? null : averageWorkDayYear.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column t_ce_identification_runaway_emission.average_daily_commuting
     *
     * @return the value of t_ce_identification_runaway_emission.average_daily_commuting
     *
     * @mbg.generated Tue Apr 01 14:16:37 CST 2025
     */
    public String getAverageDailyCommuting() {
        return averageDailyCommuting;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column t_ce_identification_runaway_emission.average_daily_commuting
     *
     * @param averageDailyCommuting the value for t_ce_identification_runaway_emission.average_daily_commuting
     *
     * @mbg.generated Tue Apr 01 14:16:37 CST 2025
     */
    public void setAverageDailyCommuting(String averageDailyCommuting) {
        this.averageDailyCommuting = averageDailyCommuting == null ? null : averageDailyCommuting.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column t_ce_identification_runaway_emission.average_daily_accommodation
     *
     * @return the value of t_ce_identification_runaway_emission.average_daily_accommodation
     *
     * @mbg.generated Tue Apr 01 14:16:37 CST 2025
     */
    public String getAverageDailyAccommodation() {
        return averageDailyAccommodation;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column t_ce_identification_runaway_emission.average_daily_accommodation
     *
     * @param averageDailyAccommodation the value for t_ce_identification_runaway_emission.average_daily_accommodation
     *
     * @mbg.generated Tue Apr 01 14:16:37 CST 2025
     */
    public void setAverageDailyAccommodation(String averageDailyAccommodation) {
        this.averageDailyAccommodation = averageDailyAccommodation == null ? null : averageDailyAccommodation.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column t_ce_identification_runaway_emission.aircondition_refrigerant_type
     *
     * @return the value of t_ce_identification_runaway_emission.aircondition_refrigerant_type
     *
     * @mbg.generated Tue Apr 01 14:16:37 CST 2025
     */
    public String getAirconditionRefrigerantType() {
        return airconditionRefrigerantType;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column t_ce_identification_runaway_emission.aircondition_refrigerant_type
     *
     * @param airconditionRefrigerantType the value for t_ce_identification_runaway_emission.aircondition_refrigerant_type
     *
     * @mbg.generated Tue Apr 01 14:16:37 CST 2025
     */
    public void setAirconditionRefrigerantType(String airconditionRefrigerantType) {
        this.airconditionRefrigerantType = airconditionRefrigerantType == null ? null : airconditionRefrigerantType.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column t_ce_identification_runaway_emission.aircondition_refrigerant_charge_amount
     *
     * @return the value of t_ce_identification_runaway_emission.aircondition_refrigerant_charge_amount
     *
     * @mbg.generated Tue Apr 01 14:16:37 CST 2025
     */
    public String getAirconditionRefrigerantChargeAmount() {
        return airconditionRefrigerantChargeAmount;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column t_ce_identification_runaway_emission.aircondition_refrigerant_charge_amount
     *
     * @param airconditionRefrigerantChargeAmount the value for t_ce_identification_runaway_emission.aircondition_refrigerant_charge_amount
     *
     * @mbg.generated Tue Apr 01 14:16:37 CST 2025
     */
    public void setAirconditionRefrigerantChargeAmount(String airconditionRefrigerantChargeAmount) {
        this.airconditionRefrigerantChargeAmount = airconditionRefrigerantChargeAmount == null ? null : airconditionRefrigerantChargeAmount.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column t_ce_identification_runaway_emission.aircondition_take_photo
     *
     * @return the value of t_ce_identification_runaway_emission.aircondition_take_photo
     *
     * @mbg.generated Tue Apr 01 14:16:37 CST 2025
     */
    public String getAirconditionTakePhoto() {
        return airconditionTakePhoto;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column t_ce_identification_runaway_emission.aircondition_take_photo
     *
     * @param airconditionTakePhoto the value for t_ce_identification_runaway_emission.aircondition_take_photo
     *
     * @mbg.generated Tue Apr 01 14:16:37 CST 2025
     */
    public void setAirconditionTakePhoto(String airconditionTakePhoto) {
        this.airconditionTakePhoto = airconditionTakePhoto == null ? null : airconditionTakePhoto.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column t_ce_identification_runaway_emission.fridge_refrigerant_type
     *
     * @return the value of t_ce_identification_runaway_emission.fridge_refrigerant_type
     *
     * @mbg.generated Tue Apr 01 14:16:37 CST 2025
     */
    public String getFridgeRefrigerantType() {
        return fridgeRefrigerantType;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column t_ce_identification_runaway_emission.fridge_refrigerant_type
     *
     * @param fridgeRefrigerantType the value for t_ce_identification_runaway_emission.fridge_refrigerant_type
     *
     * @mbg.generated Tue Apr 01 14:16:37 CST 2025
     */
    public void setFridgeRefrigerantType(String fridgeRefrigerantType) {
        this.fridgeRefrigerantType = fridgeRefrigerantType == null ? null : fridgeRefrigerantType.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column t_ce_identification_runaway_emission.fridge_refrigerant_charge_amount
     *
     * @return the value of t_ce_identification_runaway_emission.fridge_refrigerant_charge_amount
     *
     * @mbg.generated Tue Apr 01 14:16:37 CST 2025
     */
    public String getFridgeRefrigerantChargeAmount() {
        return fridgeRefrigerantChargeAmount;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column t_ce_identification_runaway_emission.fridge_refrigerant_charge_amount
     *
     * @param fridgeRefrigerantChargeAmount the value for t_ce_identification_runaway_emission.fridge_refrigerant_charge_amount
     *
     * @mbg.generated Tue Apr 01 14:16:37 CST 2025
     */
    public void setFridgeRefrigerantChargeAmount(String fridgeRefrigerantChargeAmount) {
        this.fridgeRefrigerantChargeAmount = fridgeRefrigerantChargeAmount == null ? null : fridgeRefrigerantChargeAmount.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column t_ce_identification_runaway_emission.fridge_take_photo
     *
     * @return the value of t_ce_identification_runaway_emission.fridge_take_photo
     *
     * @mbg.generated Tue Apr 01 14:16:37 CST 2025
     */
    public String getFridgeTakePhoto() {
        return fridgeTakePhoto;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column t_ce_identification_runaway_emission.fridge_take_photo
     *
     * @param fridgeTakePhoto the value for t_ce_identification_runaway_emission.fridge_take_photo
     *
     * @mbg.generated Tue Apr 01 14:16:37 CST 2025
     */
    public void setFridgeTakePhoto(String fridgeTakePhoto) {
        this.fridgeTakePhoto = fridgeTakePhoto == null ? null : fridgeTakePhoto.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column t_ce_identification_runaway_emission.service_car_refrigerant_type
     *
     * @return the value of t_ce_identification_runaway_emission.service_car_refrigerant_type
     *
     * @mbg.generated Tue Apr 01 14:16:37 CST 2025
     */
    public String getServiceCarRefrigerantType() {
        return serviceCarRefrigerantType;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column t_ce_identification_runaway_emission.service_car_refrigerant_type
     *
     * @param serviceCarRefrigerantType the value for t_ce_identification_runaway_emission.service_car_refrigerant_type
     *
     * @mbg.generated Tue Apr 01 14:16:37 CST 2025
     */
    public void setServiceCarRefrigerantType(String serviceCarRefrigerantType) {
        this.serviceCarRefrigerantType = serviceCarRefrigerantType == null ? null : serviceCarRefrigerantType.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column t_ce_identification_runaway_emission.service_car_refrigerant_charge_amount
     *
     * @return the value of t_ce_identification_runaway_emission.service_car_refrigerant_charge_amount
     *
     * @mbg.generated Tue Apr 01 14:16:37 CST 2025
     */
    public String getServiceCarRefrigerantChargeAmount() {
        return serviceCarRefrigerantChargeAmount;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column t_ce_identification_runaway_emission.service_car_refrigerant_charge_amount
     *
     * @param serviceCarRefrigerantChargeAmount the value for t_ce_identification_runaway_emission.service_car_refrigerant_charge_amount
     *
     * @mbg.generated Tue Apr 01 14:16:37 CST 2025
     */
    public void setServiceCarRefrigerantChargeAmount(String serviceCarRefrigerantChargeAmount) {
        this.serviceCarRefrigerantChargeAmount = serviceCarRefrigerantChargeAmount == null ? null : serviceCarRefrigerantChargeAmount.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column t_ce_identification_runaway_emission.service_car_take_photo
     *
     * @return the value of t_ce_identification_runaway_emission.service_car_take_photo
     *
     * @mbg.generated Tue Apr 01 14:16:37 CST 2025
     */
    public String getServiceCarTakePhoto() {
        return serviceCarTakePhoto;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column t_ce_identification_runaway_emission.service_car_take_photo
     *
     * @param serviceCarTakePhoto the value for t_ce_identification_runaway_emission.service_car_take_photo
     *
     * @mbg.generated Tue Apr 01 14:16:37 CST 2025
     */
    public void setServiceCarTakePhoto(String serviceCarTakePhoto) {
        this.serviceCarTakePhoto = serviceCarTakePhoto == null ? null : serviceCarTakePhoto.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column t_ce_identification_runaway_emission.creation_time
     *
     * @return the value of t_ce_identification_runaway_emission.creation_time
     *
     * @mbg.generated Tue Apr 01 14:16:37 CST 2025
     */
    public LocalDateTime getCreationTime() {
        return creationTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column t_ce_identification_runaway_emission.creation_time
     *
     * @param creationTime the value for t_ce_identification_runaway_emission.creation_time
     *
     * @mbg.generated Tue Apr 01 14:16:37 CST 2025
     */
    public void setCreationTime(LocalDateTime creationTime) {
        this.creationTime = creationTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column t_ce_identification_runaway_emission.create_username
     *
     * @return the value of t_ce_identification_runaway_emission.create_username
     *
     * @mbg.generated Tue Apr 01 14:16:37 CST 2025
     */
    public String getCreateUsername() {
        return createUsername;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column t_ce_identification_runaway_emission.create_username
     *
     * @param createUsername the value for t_ce_identification_runaway_emission.create_username
     *
     * @mbg.generated Tue Apr 01 14:16:37 CST 2025
     */
    public void setCreateUsername(String createUsername) {
        this.createUsername = createUsername == null ? null : createUsername.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column t_ce_identification_runaway_emission.create_user_id
     *
     * @return the value of t_ce_identification_runaway_emission.create_user_id
     *
     * @mbg.generated Tue Apr 01 14:16:37 CST 2025
     */
    public String getCreateUserId() {
        return createUserId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column t_ce_identification_runaway_emission.create_user_id
     *
     * @param createUserId the value for t_ce_identification_runaway_emission.create_user_id
     *
     * @mbg.generated Tue Apr 01 14:16:37 CST 2025
     */
    public void setCreateUserId(String createUserId) {
        this.createUserId = createUserId == null ? null : createUserId.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column t_ce_identification_runaway_emission.last_update_time
     *
     * @return the value of t_ce_identification_runaway_emission.last_update_time
     *
     * @mbg.generated Tue Apr 01 14:16:37 CST 2025
     */
    public LocalDateTime getLastUpdateTime() {
        return lastUpdateTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column t_ce_identification_runaway_emission.last_update_time
     *
     * @param lastUpdateTime the value for t_ce_identification_runaway_emission.last_update_time
     *
     * @mbg.generated Tue Apr 01 14:16:37 CST 2025
     */
    public void setLastUpdateTime(LocalDateTime lastUpdateTime) {
        this.lastUpdateTime = lastUpdateTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column t_ce_identification_runaway_emission.last_update_username
     *
     * @return the value of t_ce_identification_runaway_emission.last_update_username
     *
     * @mbg.generated Tue Apr 01 14:16:37 CST 2025
     */
    public String getLastUpdateUsername() {
        return lastUpdateUsername;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column t_ce_identification_runaway_emission.last_update_username
     *
     * @param lastUpdateUsername the value for t_ce_identification_runaway_emission.last_update_username
     *
     * @mbg.generated Tue Apr 01 14:16:37 CST 2025
     */
    public void setLastUpdateUsername(String lastUpdateUsername) {
        this.lastUpdateUsername = lastUpdateUsername == null ? null : lastUpdateUsername.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column t_ce_identification_runaway_emission.last_update_user_id
     *
     * @return the value of t_ce_identification_runaway_emission.last_update_user_id
     *
     * @mbg.generated Tue Apr 01 14:16:37 CST 2025
     */
    public String getLastUpdateUserId() {
        return lastUpdateUserId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column t_ce_identification_runaway_emission.last_update_user_id
     *
     * @param lastUpdateUserId the value for t_ce_identification_runaway_emission.last_update_user_id
     *
     * @mbg.generated Tue Apr 01 14:16:37 CST 2025
     */
    public void setLastUpdateUserId(String lastUpdateUserId) {
        this.lastUpdateUserId = lastUpdateUserId == null ? null : lastUpdateUserId.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column t_ce_identification_runaway_emission.last_update_version
     *
     * @return the value of t_ce_identification_runaway_emission.last_update_version
     *
     * @mbg.generated Tue Apr 01 14:16:37 CST 2025
     */
    public Integer getLastUpdateVersion() {
        return lastUpdateVersion;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column t_ce_identification_runaway_emission.last_update_version
     *
     * @param lastUpdateVersion the value for t_ce_identification_runaway_emission.last_update_version
     *
     * @mbg.generated Tue Apr 01 14:16:37 CST 2025
     */
    public void setLastUpdateVersion(Integer lastUpdateVersion) {
        this.lastUpdateVersion = lastUpdateVersion;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column t_ce_identification_runaway_emission.is_deleted
     *
     * @return the value of t_ce_identification_runaway_emission.is_deleted
     *
     * @mbg.generated Tue Apr 01 14:16:37 CST 2025
     */
    public Boolean getIsDeleted() {
        return isDeleted;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column t_ce_identification_runaway_emission.is_deleted
     *
     * @param isDeleted the value for t_ce_identification_runaway_emission.is_deleted
     *
     * @mbg.generated Tue Apr 01 14:16:37 CST 2025
     */
    public void setIsDeleted(Boolean isDeleted) {
        this.isDeleted = isDeleted;
    }
}