package com.csci.susdev.model;

import java.math.BigDecimal;
import java.time.LocalDateTime;

public class EmissionReductionInfo {
    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_emission_reduction_info.id
     *
     * @mbg.generated Thu Jun 20 18:41:32 HKT 2024
     */
    private String id;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_emission_reduction_info.head_id
     *
     * @mbg.generated Thu Jun 20 18:41:32 HKT 2024
     */
    private String headId;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_emission_reduction_info.description
     *
     * @mbg.generated Thu Jun 20 18:41:32 HKT 2024
     */
    private String description;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_emission_reduction_info.implement_time
     *
     * @mbg.generated Thu Jun 20 18:41:32 HKT 2024
     */
    private LocalDateTime implementTime;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_emission_reduction_info.est_annual_energy_saving
     *
     * @mbg.generated Thu Jun 20 18:41:32 HKT 2024
     */
    private BigDecimal estAnnualEnergySaving;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_emission_reduction_info.est_annual_emission_reduction
     *
     * @mbg.generated Thu Jun 20 18:41:32 HKT 2024
     */
    private BigDecimal estAnnualEmissionReduction;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_emission_reduction_info.est_annual_energy_saving_last_year
     *
     * @mbg.generated Thu Jun 20 18:41:32 HKT 2024
     */
    private BigDecimal estAnnualEnergySavingLastYear;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_emission_reduction_info.est_annual_emission_reduction_last_year
     *
     * @mbg.generated Thu Jun 20 18:41:32 HKT 2024
     */
    private BigDecimal estAnnualEmissionReductionLastYear;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_emission_reduction_info.evidence
     *
     * @mbg.generated Thu Jun 20 18:41:32 HKT 2024
     */
    private String evidence;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_emission_reduction_info.creation_time
     *
     * @mbg.generated Thu Jun 20 18:41:32 HKT 2024
     */
    private LocalDateTime creationTime;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_emission_reduction_info.create_username
     *
     * @mbg.generated Thu Jun 20 18:41:32 HKT 2024
     */
    private String createUsername;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_emission_reduction_info.create_user_id
     *
     * @mbg.generated Thu Jun 20 18:41:32 HKT 2024
     */
    private String createUserId;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_emission_reduction_info.last_update_time
     *
     * @mbg.generated Thu Jun 20 18:41:32 HKT 2024
     */
    private LocalDateTime lastUpdateTime;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_emission_reduction_info.last_update_username
     *
     * @mbg.generated Thu Jun 20 18:41:32 HKT 2024
     */
    private String lastUpdateUsername;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_emission_reduction_info.last_update_user_id
     *
     * @mbg.generated Thu Jun 20 18:41:32 HKT 2024
     */
    private String lastUpdateUserId;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_emission_reduction_info.last_update_version
     *
     * @mbg.generated Thu Jun 20 18:41:32 HKT 2024
     */
    private Integer lastUpdateVersion;

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column t_emission_reduction_info.id
     *
     * @return the value of t_emission_reduction_info.id
     *
     * @mbg.generated Thu Jun 20 18:41:32 HKT 2024
     */
    public String getId() {
        return id;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column t_emission_reduction_info.id
     *
     * @param id the value for t_emission_reduction_info.id
     *
     * @mbg.generated Thu Jun 20 18:41:32 HKT 2024
     */
    public void setId(String id) {
        this.id = id == null ? null : id.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column t_emission_reduction_info.head_id
     *
     * @return the value of t_emission_reduction_info.head_id
     *
     * @mbg.generated Thu Jun 20 18:41:32 HKT 2024
     */
    public String getHeadId() {
        return headId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column t_emission_reduction_info.head_id
     *
     * @param headId the value for t_emission_reduction_info.head_id
     *
     * @mbg.generated Thu Jun 20 18:41:32 HKT 2024
     */
    public void setHeadId(String headId) {
        this.headId = headId == null ? null : headId.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column t_emission_reduction_info.description
     *
     * @return the value of t_emission_reduction_info.description
     *
     * @mbg.generated Thu Jun 20 18:41:32 HKT 2024
     */
    public String getDescription() {
        return description;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column t_emission_reduction_info.description
     *
     * @param description the value for t_emission_reduction_info.description
     *
     * @mbg.generated Thu Jun 20 18:41:32 HKT 2024
     */
    public void setDescription(String description) {
        this.description = description == null ? null : description.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column t_emission_reduction_info.implement_time
     *
     * @return the value of t_emission_reduction_info.implement_time
     *
     * @mbg.generated Thu Jun 20 18:41:32 HKT 2024
     */
    public LocalDateTime getImplementTime() {
        return implementTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column t_emission_reduction_info.implement_time
     *
     * @param implementTime the value for t_emission_reduction_info.implement_time
     *
     * @mbg.generated Thu Jun 20 18:41:32 HKT 2024
     */
    public void setImplementTime(LocalDateTime implementTime) {
        this.implementTime = implementTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column t_emission_reduction_info.est_annual_energy_saving
     *
     * @return the value of t_emission_reduction_info.est_annual_energy_saving
     *
     * @mbg.generated Thu Jun 20 18:41:32 HKT 2024
     */
    public BigDecimal getEstAnnualEnergySaving() {
        return estAnnualEnergySaving;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column t_emission_reduction_info.est_annual_energy_saving
     *
     * @param estAnnualEnergySaving the value for t_emission_reduction_info.est_annual_energy_saving
     *
     * @mbg.generated Thu Jun 20 18:41:32 HKT 2024
     */
    public void setEstAnnualEnergySaving(BigDecimal estAnnualEnergySaving) {
        this.estAnnualEnergySaving = estAnnualEnergySaving;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column t_emission_reduction_info.est_annual_emission_reduction
     *
     * @return the value of t_emission_reduction_info.est_annual_emission_reduction
     *
     * @mbg.generated Thu Jun 20 18:41:32 HKT 2024
     */
    public BigDecimal getEstAnnualEmissionReduction() {
        return estAnnualEmissionReduction;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column t_emission_reduction_info.est_annual_emission_reduction
     *
     * @param estAnnualEmissionReduction the value for t_emission_reduction_info.est_annual_emission_reduction
     *
     * @mbg.generated Thu Jun 20 18:41:32 HKT 2024
     */
    public void setEstAnnualEmissionReduction(BigDecimal estAnnualEmissionReduction) {
        this.estAnnualEmissionReduction = estAnnualEmissionReduction;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column t_emission_reduction_info.est_annual_energy_saving_last_year
     *
     * @return the value of t_emission_reduction_info.est_annual_energy_saving_last_year
     *
     * @mbg.generated Thu Jun 20 18:41:32 HKT 2024
     */
    public BigDecimal getEstAnnualEnergySavingLastYear() {
        return estAnnualEnergySavingLastYear;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column t_emission_reduction_info.est_annual_energy_saving_last_year
     *
     * @param estAnnualEnergySavingLastYear the value for t_emission_reduction_info.est_annual_energy_saving_last_year
     *
     * @mbg.generated Thu Jun 20 18:41:32 HKT 2024
     */
    public void setEstAnnualEnergySavingLastYear(BigDecimal estAnnualEnergySavingLastYear) {
        this.estAnnualEnergySavingLastYear = estAnnualEnergySavingLastYear;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column t_emission_reduction_info.est_annual_emission_reduction_last_year
     *
     * @return the value of t_emission_reduction_info.est_annual_emission_reduction_last_year
     *
     * @mbg.generated Thu Jun 20 18:41:32 HKT 2024
     */
    public BigDecimal getEstAnnualEmissionReductionLastYear() {
        return estAnnualEmissionReductionLastYear;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column t_emission_reduction_info.est_annual_emission_reduction_last_year
     *
     * @param estAnnualEmissionReductionLastYear the value for t_emission_reduction_info.est_annual_emission_reduction_last_year
     *
     * @mbg.generated Thu Jun 20 18:41:32 HKT 2024
     */
    public void setEstAnnualEmissionReductionLastYear(BigDecimal estAnnualEmissionReductionLastYear) {
        this.estAnnualEmissionReductionLastYear = estAnnualEmissionReductionLastYear;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column t_emission_reduction_info.evidence
     *
     * @return the value of t_emission_reduction_info.evidence
     *
     * @mbg.generated Thu Jun 20 18:41:32 HKT 2024
     */
    public String getEvidence() {
        return evidence;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column t_emission_reduction_info.evidence
     *
     * @param evidence the value for t_emission_reduction_info.evidence
     *
     * @mbg.generated Thu Jun 20 18:41:32 HKT 2024
     */
    public void setEvidence(String evidence) {
        this.evidence = evidence == null ? null : evidence.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column t_emission_reduction_info.creation_time
     *
     * @return the value of t_emission_reduction_info.creation_time
     *
     * @mbg.generated Thu Jun 20 18:41:32 HKT 2024
     */
    public LocalDateTime getCreationTime() {
        return creationTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column t_emission_reduction_info.creation_time
     *
     * @param creationTime the value for t_emission_reduction_info.creation_time
     *
     * @mbg.generated Thu Jun 20 18:41:32 HKT 2024
     */
    public void setCreationTime(LocalDateTime creationTime) {
        this.creationTime = creationTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column t_emission_reduction_info.create_username
     *
     * @return the value of t_emission_reduction_info.create_username
     *
     * @mbg.generated Thu Jun 20 18:41:32 HKT 2024
     */
    public String getCreateUsername() {
        return createUsername;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column t_emission_reduction_info.create_username
     *
     * @param createUsername the value for t_emission_reduction_info.create_username
     *
     * @mbg.generated Thu Jun 20 18:41:32 HKT 2024
     */
    public void setCreateUsername(String createUsername) {
        this.createUsername = createUsername == null ? null : createUsername.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column t_emission_reduction_info.create_user_id
     *
     * @return the value of t_emission_reduction_info.create_user_id
     *
     * @mbg.generated Thu Jun 20 18:41:32 HKT 2024
     */
    public String getCreateUserId() {
        return createUserId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column t_emission_reduction_info.create_user_id
     *
     * @param createUserId the value for t_emission_reduction_info.create_user_id
     *
     * @mbg.generated Thu Jun 20 18:41:32 HKT 2024
     */
    public void setCreateUserId(String createUserId) {
        this.createUserId = createUserId == null ? null : createUserId.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column t_emission_reduction_info.last_update_time
     *
     * @return the value of t_emission_reduction_info.last_update_time
     *
     * @mbg.generated Thu Jun 20 18:41:32 HKT 2024
     */
    public LocalDateTime getLastUpdateTime() {
        return lastUpdateTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column t_emission_reduction_info.last_update_time
     *
     * @param lastUpdateTime the value for t_emission_reduction_info.last_update_time
     *
     * @mbg.generated Thu Jun 20 18:41:32 HKT 2024
     */
    public void setLastUpdateTime(LocalDateTime lastUpdateTime) {
        this.lastUpdateTime = lastUpdateTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column t_emission_reduction_info.last_update_username
     *
     * @return the value of t_emission_reduction_info.last_update_username
     *
     * @mbg.generated Thu Jun 20 18:41:32 HKT 2024
     */
    public String getLastUpdateUsername() {
        return lastUpdateUsername;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column t_emission_reduction_info.last_update_username
     *
     * @param lastUpdateUsername the value for t_emission_reduction_info.last_update_username
     *
     * @mbg.generated Thu Jun 20 18:41:32 HKT 2024
     */
    public void setLastUpdateUsername(String lastUpdateUsername) {
        this.lastUpdateUsername = lastUpdateUsername == null ? null : lastUpdateUsername.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column t_emission_reduction_info.last_update_user_id
     *
     * @return the value of t_emission_reduction_info.last_update_user_id
     *
     * @mbg.generated Thu Jun 20 18:41:32 HKT 2024
     */
    public String getLastUpdateUserId() {
        return lastUpdateUserId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column t_emission_reduction_info.last_update_user_id
     *
     * @param lastUpdateUserId the value for t_emission_reduction_info.last_update_user_id
     *
     * @mbg.generated Thu Jun 20 18:41:32 HKT 2024
     */
    public void setLastUpdateUserId(String lastUpdateUserId) {
        this.lastUpdateUserId = lastUpdateUserId == null ? null : lastUpdateUserId.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column t_emission_reduction_info.last_update_version
     *
     * @return the value of t_emission_reduction_info.last_update_version
     *
     * @mbg.generated Thu Jun 20 18:41:32 HKT 2024
     */
    public Integer getLastUpdateVersion() {
        return lastUpdateVersion;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column t_emission_reduction_info.last_update_version
     *
     * @param lastUpdateVersion the value for t_emission_reduction_info.last_update_version
     *
     * @mbg.generated Thu Jun 20 18:41:32 HKT 2024
     */
    public void setLastUpdateVersion(Integer lastUpdateVersion) {
        this.lastUpdateVersion = lastUpdateVersion;
    }
}