package com.csci.susdev.model;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;

public class AirPollutantsExample {
    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database table t_cf_air_pollutants
     *
     * @mbg.generated
     */
    protected String orderByClause;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database table t_cf_air_pollutants
     *
     * @mbg.generated
     */
    protected boolean distinct;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database table t_cf_air_pollutants
     *
     * @mbg.generated
     */
    protected List<Criteria> oredCriteria;

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table t_cf_air_pollutants
     *
     * @mbg.generated
     */
    public AirPollutantsExample() {
        oredCriteria = new ArrayList<Criteria>();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table t_cf_air_pollutants
     *
     * @mbg.generated
     */
    public void setOrderByClause(String orderByClause) {
        this.orderByClause = orderByClause;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table t_cf_air_pollutants
     *
     * @mbg.generated
     */
    public String getOrderByClause() {
        return orderByClause;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table t_cf_air_pollutants
     *
     * @mbg.generated
     */
    public void setDistinct(boolean distinct) {
        this.distinct = distinct;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table t_cf_air_pollutants
     *
     * @mbg.generated
     */
    public boolean isDistinct() {
        return distinct;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table t_cf_air_pollutants
     *
     * @mbg.generated
     */
    public List<Criteria> getOredCriteria() {
        return oredCriteria;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table t_cf_air_pollutants
     *
     * @mbg.generated
     */
    public void or(Criteria criteria) {
        oredCriteria.add(criteria);
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table t_cf_air_pollutants
     *
     * @mbg.generated
     */
    public Criteria or() {
        Criteria criteria = createCriteriaInternal();
        oredCriteria.add(criteria);
        return criteria;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table t_cf_air_pollutants
     *
     * @mbg.generated
     */
    public Criteria createCriteria() {
        Criteria criteria = createCriteriaInternal();
        if (oredCriteria.size() == 0) {
            oredCriteria.add(criteria);
        }
        return criteria;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table t_cf_air_pollutants
     *
     * @mbg.generated
     */
    protected Criteria createCriteriaInternal() {
        Criteria criteria = new Criteria();
        return criteria;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table t_cf_air_pollutants
     *
     * @mbg.generated
     */
    public void clear() {
        oredCriteria.clear();
        orderByClause = null;
        distinct = false;
    }

    /**
     * This class was generated by MyBatis Generator.
     * This class corresponds to the database table t_cf_air_pollutants
     *
     * @mbg.generated
     */
    protected abstract static class GeneratedCriteria {
        protected List<Criterion> criteria;

        protected GeneratedCriteria() {
            super();
            criteria = new ArrayList<Criterion>();
        }

        public boolean isValid() {
            return criteria.size() > 0;
        }

        public List<Criterion> getAllCriteria() {
            return criteria;
        }

        public List<Criterion> getCriteria() {
            return criteria;
        }

        protected void addCriterion(String condition) {
            if (condition == null) {
                throw new RuntimeException("Value for condition cannot be null");
            }
            criteria.add(new Criterion(condition));
        }

        protected void addCriterion(String condition, Object value, String property) {
            if (value == null) {
                throw new RuntimeException("Value for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value));
        }

        protected void addCriterion(String condition, Object value1, Object value2, String property) {
            if (value1 == null || value2 == null) {
                throw new RuntimeException("Between values for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value1, value2));
        }

        public Criteria andIdIsNull() {
            addCriterion("id is null");
            return (Criteria) this;
        }

        public Criteria andIdIsNotNull() {
            addCriterion("id is not null");
            return (Criteria) this;
        }

        public Criteria andIdEqualTo(String value) {
            addCriterion("id =", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotEqualTo(String value) {
            addCriterion("id <>", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThan(String value) {
            addCriterion("id >", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThanOrEqualTo(String value) {
            addCriterion("id >=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThan(String value) {
            addCriterion("id <", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThanOrEqualTo(String value) {
            addCriterion("id <=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLike(String value) {
            addCriterion("id like", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotLike(String value) {
            addCriterion("id not like", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdIn(List<String> values) {
            addCriterion("id in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotIn(List<String> values) {
            addCriterion("id not in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdBetween(String value1, String value2) {
            addCriterion("id between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotBetween(String value1, String value2) {
            addCriterion("id not between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andBatchIdIsNull() {
            addCriterion("batch_id is null");
            return (Criteria) this;
        }

        public Criteria andBatchIdIsNotNull() {
            addCriterion("batch_id is not null");
            return (Criteria) this;
        }

        public Criteria andBatchIdEqualTo(String value) {
            addCriterion("batch_id =", value, "batchId");
            return (Criteria) this;
        }

        public Criteria andBatchIdNotEqualTo(String value) {
            addCriterion("batch_id <>", value, "batchId");
            return (Criteria) this;
        }

        public Criteria andBatchIdGreaterThan(String value) {
            addCriterion("batch_id >", value, "batchId");
            return (Criteria) this;
        }

        public Criteria andBatchIdGreaterThanOrEqualTo(String value) {
            addCriterion("batch_id >=", value, "batchId");
            return (Criteria) this;
        }

        public Criteria andBatchIdLessThan(String value) {
            addCriterion("batch_id <", value, "batchId");
            return (Criteria) this;
        }

        public Criteria andBatchIdLessThanOrEqualTo(String value) {
            addCriterion("batch_id <=", value, "batchId");
            return (Criteria) this;
        }

        public Criteria andBatchIdLike(String value) {
            addCriterion("batch_id like", value, "batchId");
            return (Criteria) this;
        }

        public Criteria andBatchIdNotLike(String value) {
            addCriterion("batch_id not like", value, "batchId");
            return (Criteria) this;
        }

        public Criteria andBatchIdIn(List<String> values) {
            addCriterion("batch_id in", values, "batchId");
            return (Criteria) this;
        }

        public Criteria andBatchIdNotIn(List<String> values) {
            addCriterion("batch_id not in", values, "batchId");
            return (Criteria) this;
        }

        public Criteria andBatchIdBetween(String value1, String value2) {
            addCriterion("batch_id between", value1, value2, "batchId");
            return (Criteria) this;
        }

        public Criteria andBatchIdNotBetween(String value1, String value2) {
            addCriterion("batch_id not between", value1, value2, "batchId");
            return (Criteria) this;
        }

        public Criteria andCategoryIsNull() {
            addCriterion("category is null");
            return (Criteria) this;
        }

        public Criteria andCategoryIsNotNull() {
            addCriterion("category is not null");
            return (Criteria) this;
        }

        public Criteria andCategoryEqualTo(String value) {
            addCriterion("category =", value, "category");
            return (Criteria) this;
        }

        public Criteria andCategoryNotEqualTo(String value) {
            addCriterion("category <>", value, "category");
            return (Criteria) this;
        }

        public Criteria andCategoryGreaterThan(String value) {
            addCriterion("category >", value, "category");
            return (Criteria) this;
        }

        public Criteria andCategoryGreaterThanOrEqualTo(String value) {
            addCriterion("category >=", value, "category");
            return (Criteria) this;
        }

        public Criteria andCategoryLessThan(String value) {
            addCriterion("category <", value, "category");
            return (Criteria) this;
        }

        public Criteria andCategoryLessThanOrEqualTo(String value) {
            addCriterion("category <=", value, "category");
            return (Criteria) this;
        }

        public Criteria andCategoryLike(String value) {
            addCriterion("category like", value, "category");
            return (Criteria) this;
        }

        public Criteria andCategoryNotLike(String value) {
            addCriterion("category not like", value, "category");
            return (Criteria) this;
        }

        public Criteria andCategoryIn(List<String> values) {
            addCriterion("category in", values, "category");
            return (Criteria) this;
        }

        public Criteria andCategoryNotIn(List<String> values) {
            addCriterion("category not in", values, "category");
            return (Criteria) this;
        }

        public Criteria andCategoryBetween(String value1, String value2) {
            addCriterion("category between", value1, value2, "category");
            return (Criteria) this;
        }

        public Criteria andCategoryNotBetween(String value1, String value2) {
            addCriterion("category not between", value1, value2, "category");
            return (Criteria) this;
        }

        public Criteria andEmissionSourceIsNull() {
            addCriterion("emission_source is null");
            return (Criteria) this;
        }

        public Criteria andEmissionSourceIsNotNull() {
            addCriterion("emission_source is not null");
            return (Criteria) this;
        }

        public Criteria andEmissionSourceEqualTo(String value) {
            addCriterion("emission_source =", value, "emissionSource");
            return (Criteria) this;
        }

        public Criteria andEmissionSourceNotEqualTo(String value) {
            addCriterion("emission_source <>", value, "emissionSource");
            return (Criteria) this;
        }

        public Criteria andEmissionSourceGreaterThan(String value) {
            addCriterion("emission_source >", value, "emissionSource");
            return (Criteria) this;
        }

        public Criteria andEmissionSourceGreaterThanOrEqualTo(String value) {
            addCriterion("emission_source >=", value, "emissionSource");
            return (Criteria) this;
        }

        public Criteria andEmissionSourceLessThan(String value) {
            addCriterion("emission_source <", value, "emissionSource");
            return (Criteria) this;
        }

        public Criteria andEmissionSourceLessThanOrEqualTo(String value) {
            addCriterion("emission_source <=", value, "emissionSource");
            return (Criteria) this;
        }

        public Criteria andEmissionSourceLike(String value) {
            addCriterion("emission_source like", value, "emissionSource");
            return (Criteria) this;
        }

        public Criteria andEmissionSourceNotLike(String value) {
            addCriterion("emission_source not like", value, "emissionSource");
            return (Criteria) this;
        }

        public Criteria andEmissionSourceIn(List<String> values) {
            addCriterion("emission_source in", values, "emissionSource");
            return (Criteria) this;
        }

        public Criteria andEmissionSourceNotIn(List<String> values) {
            addCriterion("emission_source not in", values, "emissionSource");
            return (Criteria) this;
        }

        public Criteria andEmissionSourceBetween(String value1, String value2) {
            addCriterion("emission_source between", value1, value2, "emissionSource");
            return (Criteria) this;
        }

        public Criteria andEmissionSourceNotBetween(String value1, String value2) {
            addCriterion("emission_source not between", value1, value2, "emissionSource");
            return (Criteria) this;
        }

        public Criteria andTypeIsNull() {
            addCriterion("type is null");
            return (Criteria) this;
        }

        public Criteria andTypeIsNotNull() {
            addCriterion("type is not null");
            return (Criteria) this;
        }

        public Criteria andTypeEqualTo(String value) {
            addCriterion("type =", value, "type");
            return (Criteria) this;
        }

        public Criteria andTypeNotEqualTo(String value) {
            addCriterion("type <>", value, "type");
            return (Criteria) this;
        }

        public Criteria andTypeGreaterThan(String value) {
            addCriterion("type >", value, "type");
            return (Criteria) this;
        }

        public Criteria andTypeGreaterThanOrEqualTo(String value) {
            addCriterion("type >=", value, "type");
            return (Criteria) this;
        }

        public Criteria andTypeLessThan(String value) {
            addCriterion("type <", value, "type");
            return (Criteria) this;
        }

        public Criteria andTypeLessThanOrEqualTo(String value) {
            addCriterion("type <=", value, "type");
            return (Criteria) this;
        }

        public Criteria andTypeLike(String value) {
            addCriterion("type like", value, "type");
            return (Criteria) this;
        }

        public Criteria andTypeNotLike(String value) {
            addCriterion("type not like", value, "type");
            return (Criteria) this;
        }

        public Criteria andTypeIn(List<String> values) {
            addCriterion("type in", values, "type");
            return (Criteria) this;
        }

        public Criteria andTypeNotIn(List<String> values) {
            addCriterion("type not in", values, "type");
            return (Criteria) this;
        }

        public Criteria andTypeBetween(String value1, String value2) {
            addCriterion("type between", value1, value2, "type");
            return (Criteria) this;
        }

        public Criteria andTypeNotBetween(String value1, String value2) {
            addCriterion("type not between", value1, value2, "type");
            return (Criteria) this;
        }

        public Criteria andUnitIsNull() {
            addCriterion("unit is null");
            return (Criteria) this;
        }

        public Criteria andUnitIsNotNull() {
            addCriterion("unit is not null");
            return (Criteria) this;
        }

        public Criteria andUnitEqualTo(String value) {
            addCriterion("unit =", value, "unit");
            return (Criteria) this;
        }

        public Criteria andUnitNotEqualTo(String value) {
            addCriterion("unit <>", value, "unit");
            return (Criteria) this;
        }

        public Criteria andUnitGreaterThan(String value) {
            addCriterion("unit >", value, "unit");
            return (Criteria) this;
        }

        public Criteria andUnitGreaterThanOrEqualTo(String value) {
            addCriterion("unit >=", value, "unit");
            return (Criteria) this;
        }

        public Criteria andUnitLessThan(String value) {
            addCriterion("unit <", value, "unit");
            return (Criteria) this;
        }

        public Criteria andUnitLessThanOrEqualTo(String value) {
            addCriterion("unit <=", value, "unit");
            return (Criteria) this;
        }

        public Criteria andUnitLike(String value) {
            addCriterion("unit like", value, "unit");
            return (Criteria) this;
        }

        public Criteria andUnitNotLike(String value) {
            addCriterion("unit not like", value, "unit");
            return (Criteria) this;
        }

        public Criteria andUnitIn(List<String> values) {
            addCriterion("unit in", values, "unit");
            return (Criteria) this;
        }

        public Criteria andUnitNotIn(List<String> values) {
            addCriterion("unit not in", values, "unit");
            return (Criteria) this;
        }

        public Criteria andUnitBetween(String value1, String value2) {
            addCriterion("unit between", value1, value2, "unit");
            return (Criteria) this;
        }

        public Criteria andUnitNotBetween(String value1, String value2) {
            addCriterion("unit not between", value1, value2, "unit");
            return (Criteria) this;
        }

        public Criteria andAirPollutionTypeIsNull() {
            addCriterion("air_pollution_type is null");
            return (Criteria) this;
        }

        public Criteria andAirPollutionTypeIsNotNull() {
            addCriterion("air_pollution_type is not null");
            return (Criteria) this;
        }

        public Criteria andAirPollutionTypeEqualTo(String value) {
            addCriterion("air_pollution_type =", value, "airPollutionType");
            return (Criteria) this;
        }

        public Criteria andAirPollutionTypeNotEqualTo(String value) {
            addCriterion("air_pollution_type <>", value, "airPollutionType");
            return (Criteria) this;
        }

        public Criteria andAirPollutionTypeGreaterThan(String value) {
            addCriterion("air_pollution_type >", value, "airPollutionType");
            return (Criteria) this;
        }

        public Criteria andAirPollutionTypeGreaterThanOrEqualTo(String value) {
            addCriterion("air_pollution_type >=", value, "airPollutionType");
            return (Criteria) this;
        }

        public Criteria andAirPollutionTypeLessThan(String value) {
            addCriterion("air_pollution_type <", value, "airPollutionType");
            return (Criteria) this;
        }

        public Criteria andAirPollutionTypeLessThanOrEqualTo(String value) {
            addCriterion("air_pollution_type <=", value, "airPollutionType");
            return (Criteria) this;
        }

        public Criteria andAirPollutionTypeLike(String value) {
            addCriterion("air_pollution_type like", value, "airPollutionType");
            return (Criteria) this;
        }

        public Criteria andAirPollutionTypeNotLike(String value) {
            addCriterion("air_pollution_type not like", value, "airPollutionType");
            return (Criteria) this;
        }

        public Criteria andAirPollutionTypeIn(List<String> values) {
            addCriterion("air_pollution_type in", values, "airPollutionType");
            return (Criteria) this;
        }

        public Criteria andAirPollutionTypeNotIn(List<String> values) {
            addCriterion("air_pollution_type not in", values, "airPollutionType");
            return (Criteria) this;
        }

        public Criteria andAirPollutionTypeBetween(String value1, String value2) {
            addCriterion("air_pollution_type between", value1, value2, "airPollutionType");
            return (Criteria) this;
        }

        public Criteria andAirPollutionTypeNotBetween(String value1, String value2) {
            addCriterion("air_pollution_type not between", value1, value2, "airPollutionType");
            return (Criteria) this;
        }

        public Criteria andVehicleTypeIsNull() {
            addCriterion("vehicle_type is null");
            return (Criteria) this;
        }

        public Criteria andVehicleTypeIsNotNull() {
            addCriterion("vehicle_type is not null");
            return (Criteria) this;
        }

        public Criteria andVehicleTypeEqualTo(String value) {
            addCriterion("vehicle_type =", value, "vehicleType");
            return (Criteria) this;
        }

        public Criteria andVehicleTypeNotEqualTo(String value) {
            addCriterion("vehicle_type <>", value, "vehicleType");
            return (Criteria) this;
        }

        public Criteria andVehicleTypeGreaterThan(String value) {
            addCriterion("vehicle_type >", value, "vehicleType");
            return (Criteria) this;
        }

        public Criteria andVehicleTypeGreaterThanOrEqualTo(String value) {
            addCriterion("vehicle_type >=", value, "vehicleType");
            return (Criteria) this;
        }

        public Criteria andVehicleTypeLessThan(String value) {
            addCriterion("vehicle_type <", value, "vehicleType");
            return (Criteria) this;
        }

        public Criteria andVehicleTypeLessThanOrEqualTo(String value) {
            addCriterion("vehicle_type <=", value, "vehicleType");
            return (Criteria) this;
        }

        public Criteria andVehicleTypeLike(String value) {
            addCriterion("vehicle_type like", value, "vehicleType");
            return (Criteria) this;
        }

        public Criteria andVehicleTypeNotLike(String value) {
            addCriterion("vehicle_type not like", value, "vehicleType");
            return (Criteria) this;
        }

        public Criteria andVehicleTypeIn(List<String> values) {
            addCriterion("vehicle_type in", values, "vehicleType");
            return (Criteria) this;
        }

        public Criteria andVehicleTypeNotIn(List<String> values) {
            addCriterion("vehicle_type not in", values, "vehicleType");
            return (Criteria) this;
        }

        public Criteria andVehicleTypeBetween(String value1, String value2) {
            addCriterion("vehicle_type between", value1, value2, "vehicleType");
            return (Criteria) this;
        }

        public Criteria andVehicleTypeNotBetween(String value1, String value2) {
            addCriterion("vehicle_type not between", value1, value2, "vehicleType");
            return (Criteria) this;
        }

        public Criteria andVehicleEmissionStandardIsNull() {
            addCriterion("vehicle_emission_standard is null");
            return (Criteria) this;
        }

        public Criteria andVehicleEmissionStandardIsNotNull() {
            addCriterion("vehicle_emission_standard is not null");
            return (Criteria) this;
        }

        public Criteria andVehicleEmissionStandardEqualTo(String value) {
            addCriterion("vehicle_emission_standard =", value, "vehicleEmissionStandard");
            return (Criteria) this;
        }

        public Criteria andVehicleEmissionStandardNotEqualTo(String value) {
            addCriterion("vehicle_emission_standard <>", value, "vehicleEmissionStandard");
            return (Criteria) this;
        }

        public Criteria andVehicleEmissionStandardGreaterThan(String value) {
            addCriterion("vehicle_emission_standard >", value, "vehicleEmissionStandard");
            return (Criteria) this;
        }

        public Criteria andVehicleEmissionStandardGreaterThanOrEqualTo(String value) {
            addCriterion("vehicle_emission_standard >=", value, "vehicleEmissionStandard");
            return (Criteria) this;
        }

        public Criteria andVehicleEmissionStandardLessThan(String value) {
            addCriterion("vehicle_emission_standard <", value, "vehicleEmissionStandard");
            return (Criteria) this;
        }

        public Criteria andVehicleEmissionStandardLessThanOrEqualTo(String value) {
            addCriterion("vehicle_emission_standard <=", value, "vehicleEmissionStandard");
            return (Criteria) this;
        }

        public Criteria andVehicleEmissionStandardLike(String value) {
            addCriterion("vehicle_emission_standard like", value, "vehicleEmissionStandard");
            return (Criteria) this;
        }

        public Criteria andVehicleEmissionStandardNotLike(String value) {
            addCriterion("vehicle_emission_standard not like", value, "vehicleEmissionStandard");
            return (Criteria) this;
        }

        public Criteria andVehicleEmissionStandardIn(List<String> values) {
            addCriterion("vehicle_emission_standard in", values, "vehicleEmissionStandard");
            return (Criteria) this;
        }

        public Criteria andVehicleEmissionStandardNotIn(List<String> values) {
            addCriterion("vehicle_emission_standard not in", values, "vehicleEmissionStandard");
            return (Criteria) this;
        }

        public Criteria andVehicleEmissionStandardBetween(String value1, String value2) {
            addCriterion("vehicle_emission_standard between", value1, value2, "vehicleEmissionStandard");
            return (Criteria) this;
        }

        public Criteria andVehicleEmissionStandardNotBetween(String value1, String value2) {
            addCriterion("vehicle_emission_standard not between", value1, value2, "vehicleEmissionStandard");
            return (Criteria) this;
        }

        public Criteria andAirPolEmiFactorIsNull() {
            addCriterion("air_pol_emi_factor is null");
            return (Criteria) this;
        }

        public Criteria andAirPolEmiFactorIsNotNull() {
            addCriterion("air_pol_emi_factor is not null");
            return (Criteria) this;
        }

        public Criteria andAirPolEmiFactorEqualTo(String value) {
            addCriterion("air_pol_emi_factor =", value, "airPolEmiFactor");
            return (Criteria) this;
        }

        public Criteria andAirPolEmiFactorNotEqualTo(String value) {
            addCriterion("air_pol_emi_factor <>", value, "airPolEmiFactor");
            return (Criteria) this;
        }

        public Criteria andAirPolEmiFactorGreaterThan(String value) {
            addCriterion("air_pol_emi_factor >", value, "airPolEmiFactor");
            return (Criteria) this;
        }

        public Criteria andAirPolEmiFactorGreaterThanOrEqualTo(String value) {
            addCriterion("air_pol_emi_factor >=", value, "airPolEmiFactor");
            return (Criteria) this;
        }

        public Criteria andAirPolEmiFactorLessThan(String value) {
            addCriterion("air_pol_emi_factor <", value, "airPolEmiFactor");
            return (Criteria) this;
        }

        public Criteria andAirPolEmiFactorLessThanOrEqualTo(String value) {
            addCriterion("air_pol_emi_factor <=", value, "airPolEmiFactor");
            return (Criteria) this;
        }

        public Criteria andAirPolEmiFactorLike(String value) {
            addCriterion("air_pol_emi_factor like", value, "airPolEmiFactor");
            return (Criteria) this;
        }

        public Criteria andAirPolEmiFactorNotLike(String value) {
            addCriterion("air_pol_emi_factor not like", value, "airPolEmiFactor");
            return (Criteria) this;
        }

        public Criteria andAirPolEmiFactorIn(List<String> values) {
            addCriterion("air_pol_emi_factor in", values, "airPolEmiFactor");
            return (Criteria) this;
        }

        public Criteria andAirPolEmiFactorNotIn(List<String> values) {
            addCriterion("air_pol_emi_factor not in", values, "airPolEmiFactor");
            return (Criteria) this;
        }

        public Criteria andAirPolEmiFactorBetween(String value1, String value2) {
            addCriterion("air_pol_emi_factor between", value1, value2, "airPolEmiFactor");
            return (Criteria) this;
        }

        public Criteria andAirPolEmiFactorNotBetween(String value1, String value2) {
            addCriterion("air_pol_emi_factor not between", value1, value2, "airPolEmiFactor");
            return (Criteria) this;
        }

        public Criteria andAirPolEmiUnitIsNull() {
            addCriterion("air_pol_emi_unit is null");
            return (Criteria) this;
        }

        public Criteria andAirPolEmiUnitIsNotNull() {
            addCriterion("air_pol_emi_unit is not null");
            return (Criteria) this;
        }

        public Criteria andAirPolEmiUnitEqualTo(String value) {
            addCriterion("air_pol_emi_unit =", value, "airPolEmiUnit");
            return (Criteria) this;
        }

        public Criteria andAirPolEmiUnitNotEqualTo(String value) {
            addCriterion("air_pol_emi_unit <>", value, "airPolEmiUnit");
            return (Criteria) this;
        }

        public Criteria andAirPolEmiUnitGreaterThan(String value) {
            addCriterion("air_pol_emi_unit >", value, "airPolEmiUnit");
            return (Criteria) this;
        }

        public Criteria andAirPolEmiUnitGreaterThanOrEqualTo(String value) {
            addCriterion("air_pol_emi_unit >=", value, "airPolEmiUnit");
            return (Criteria) this;
        }

        public Criteria andAirPolEmiUnitLessThan(String value) {
            addCriterion("air_pol_emi_unit <", value, "airPolEmiUnit");
            return (Criteria) this;
        }

        public Criteria andAirPolEmiUnitLessThanOrEqualTo(String value) {
            addCriterion("air_pol_emi_unit <=", value, "airPolEmiUnit");
            return (Criteria) this;
        }

        public Criteria andAirPolEmiUnitLike(String value) {
            addCriterion("air_pol_emi_unit like", value, "airPolEmiUnit");
            return (Criteria) this;
        }

        public Criteria andAirPolEmiUnitNotLike(String value) {
            addCriterion("air_pol_emi_unit not like", value, "airPolEmiUnit");
            return (Criteria) this;
        }

        public Criteria andAirPolEmiUnitIn(List<String> values) {
            addCriterion("air_pol_emi_unit in", values, "airPolEmiUnit");
            return (Criteria) this;
        }

        public Criteria andAirPolEmiUnitNotIn(List<String> values) {
            addCriterion("air_pol_emi_unit not in", values, "airPolEmiUnit");
            return (Criteria) this;
        }

        public Criteria andAirPolEmiUnitBetween(String value1, String value2) {
            addCriterion("air_pol_emi_unit between", value1, value2, "airPolEmiUnit");
            return (Criteria) this;
        }

        public Criteria andAirPolEmiUnitNotBetween(String value1, String value2) {
            addCriterion("air_pol_emi_unit not between", value1, value2, "airPolEmiUnit");
            return (Criteria) this;
        }

        public Criteria andCreationTimeIsNull() {
            addCriterion("creation_time is null");
            return (Criteria) this;
        }

        public Criteria andCreationTimeIsNotNull() {
            addCriterion("creation_time is not null");
            return (Criteria) this;
        }

        public Criteria andCreationTimeEqualTo(LocalDateTime value) {
            addCriterion("creation_time =", value, "creationTime");
            return (Criteria) this;
        }

        public Criteria andCreationTimeNotEqualTo(LocalDateTime value) {
            addCriterion("creation_time <>", value, "creationTime");
            return (Criteria) this;
        }

        public Criteria andCreationTimeGreaterThan(LocalDateTime value) {
            addCriterion("creation_time >", value, "creationTime");
            return (Criteria) this;
        }

        public Criteria andCreationTimeGreaterThanOrEqualTo(LocalDateTime value) {
            addCriterion("creation_time >=", value, "creationTime");
            return (Criteria) this;
        }

        public Criteria andCreationTimeLessThan(LocalDateTime value) {
            addCriterion("creation_time <", value, "creationTime");
            return (Criteria) this;
        }

        public Criteria andCreationTimeLessThanOrEqualTo(LocalDateTime value) {
            addCriterion("creation_time <=", value, "creationTime");
            return (Criteria) this;
        }

        public Criteria andCreationTimeIn(List<LocalDateTime> values) {
            addCriterion("creation_time in", values, "creationTime");
            return (Criteria) this;
        }

        public Criteria andCreationTimeNotIn(List<LocalDateTime> values) {
            addCriterion("creation_time not in", values, "creationTime");
            return (Criteria) this;
        }

        public Criteria andCreationTimeBetween(LocalDateTime value1, LocalDateTime value2) {
            addCriterion("creation_time between", value1, value2, "creationTime");
            return (Criteria) this;
        }

        public Criteria andCreationTimeNotBetween(LocalDateTime value1, LocalDateTime value2) {
            addCriterion("creation_time not between", value1, value2, "creationTime");
            return (Criteria) this;
        }

        public Criteria andCreateUsernameIsNull() {
            addCriterion("create_username is null");
            return (Criteria) this;
        }

        public Criteria andCreateUsernameIsNotNull() {
            addCriterion("create_username is not null");
            return (Criteria) this;
        }

        public Criteria andCreateUsernameEqualTo(String value) {
            addCriterion("create_username =", value, "createUsername");
            return (Criteria) this;
        }

        public Criteria andCreateUsernameNotEqualTo(String value) {
            addCriterion("create_username <>", value, "createUsername");
            return (Criteria) this;
        }

        public Criteria andCreateUsernameGreaterThan(String value) {
            addCriterion("create_username >", value, "createUsername");
            return (Criteria) this;
        }

        public Criteria andCreateUsernameGreaterThanOrEqualTo(String value) {
            addCriterion("create_username >=", value, "createUsername");
            return (Criteria) this;
        }

        public Criteria andCreateUsernameLessThan(String value) {
            addCriterion("create_username <", value, "createUsername");
            return (Criteria) this;
        }

        public Criteria andCreateUsernameLessThanOrEqualTo(String value) {
            addCriterion("create_username <=", value, "createUsername");
            return (Criteria) this;
        }

        public Criteria andCreateUsernameLike(String value) {
            addCriterion("create_username like", value, "createUsername");
            return (Criteria) this;
        }

        public Criteria andCreateUsernameNotLike(String value) {
            addCriterion("create_username not like", value, "createUsername");
            return (Criteria) this;
        }

        public Criteria andCreateUsernameIn(List<String> values) {
            addCriterion("create_username in", values, "createUsername");
            return (Criteria) this;
        }

        public Criteria andCreateUsernameNotIn(List<String> values) {
            addCriterion("create_username not in", values, "createUsername");
            return (Criteria) this;
        }

        public Criteria andCreateUsernameBetween(String value1, String value2) {
            addCriterion("create_username between", value1, value2, "createUsername");
            return (Criteria) this;
        }

        public Criteria andCreateUsernameNotBetween(String value1, String value2) {
            addCriterion("create_username not between", value1, value2, "createUsername");
            return (Criteria) this;
        }

        public Criteria andCreateUserIdIsNull() {
            addCriterion("create_user_id is null");
            return (Criteria) this;
        }

        public Criteria andCreateUserIdIsNotNull() {
            addCriterion("create_user_id is not null");
            return (Criteria) this;
        }

        public Criteria andCreateUserIdEqualTo(String value) {
            addCriterion("create_user_id =", value, "createUserId");
            return (Criteria) this;
        }

        public Criteria andCreateUserIdNotEqualTo(String value) {
            addCriterion("create_user_id <>", value, "createUserId");
            return (Criteria) this;
        }

        public Criteria andCreateUserIdGreaterThan(String value) {
            addCriterion("create_user_id >", value, "createUserId");
            return (Criteria) this;
        }

        public Criteria andCreateUserIdGreaterThanOrEqualTo(String value) {
            addCriterion("create_user_id >=", value, "createUserId");
            return (Criteria) this;
        }

        public Criteria andCreateUserIdLessThan(String value) {
            addCriterion("create_user_id <", value, "createUserId");
            return (Criteria) this;
        }

        public Criteria andCreateUserIdLessThanOrEqualTo(String value) {
            addCriterion("create_user_id <=", value, "createUserId");
            return (Criteria) this;
        }

        public Criteria andCreateUserIdLike(String value) {
            addCriterion("create_user_id like", value, "createUserId");
            return (Criteria) this;
        }

        public Criteria andCreateUserIdNotLike(String value) {
            addCriterion("create_user_id not like", value, "createUserId");
            return (Criteria) this;
        }

        public Criteria andCreateUserIdIn(List<String> values) {
            addCriterion("create_user_id in", values, "createUserId");
            return (Criteria) this;
        }

        public Criteria andCreateUserIdNotIn(List<String> values) {
            addCriterion("create_user_id not in", values, "createUserId");
            return (Criteria) this;
        }

        public Criteria andCreateUserIdBetween(String value1, String value2) {
            addCriterion("create_user_id between", value1, value2, "createUserId");
            return (Criteria) this;
        }

        public Criteria andCreateUserIdNotBetween(String value1, String value2) {
            addCriterion("create_user_id not between", value1, value2, "createUserId");
            return (Criteria) this;
        }

        public Criteria andLastUpdateTimeIsNull() {
            addCriterion("last_update_time is null");
            return (Criteria) this;
        }

        public Criteria andLastUpdateTimeIsNotNull() {
            addCriterion("last_update_time is not null");
            return (Criteria) this;
        }

        public Criteria andLastUpdateTimeEqualTo(LocalDateTime value) {
            addCriterion("last_update_time =", value, "lastUpdateTime");
            return (Criteria) this;
        }

        public Criteria andLastUpdateTimeNotEqualTo(LocalDateTime value) {
            addCriterion("last_update_time <>", value, "lastUpdateTime");
            return (Criteria) this;
        }

        public Criteria andLastUpdateTimeGreaterThan(LocalDateTime value) {
            addCriterion("last_update_time >", value, "lastUpdateTime");
            return (Criteria) this;
        }

        public Criteria andLastUpdateTimeGreaterThanOrEqualTo(LocalDateTime value) {
            addCriterion("last_update_time >=", value, "lastUpdateTime");
            return (Criteria) this;
        }

        public Criteria andLastUpdateTimeLessThan(LocalDateTime value) {
            addCriterion("last_update_time <", value, "lastUpdateTime");
            return (Criteria) this;
        }

        public Criteria andLastUpdateTimeLessThanOrEqualTo(LocalDateTime value) {
            addCriterion("last_update_time <=", value, "lastUpdateTime");
            return (Criteria) this;
        }

        public Criteria andLastUpdateTimeIn(List<LocalDateTime> values) {
            addCriterion("last_update_time in", values, "lastUpdateTime");
            return (Criteria) this;
        }

        public Criteria andLastUpdateTimeNotIn(List<LocalDateTime> values) {
            addCriterion("last_update_time not in", values, "lastUpdateTime");
            return (Criteria) this;
        }

        public Criteria andLastUpdateTimeBetween(LocalDateTime value1, LocalDateTime value2) {
            addCriterion("last_update_time between", value1, value2, "lastUpdateTime");
            return (Criteria) this;
        }

        public Criteria andLastUpdateTimeNotBetween(LocalDateTime value1, LocalDateTime value2) {
            addCriterion("last_update_time not between", value1, value2, "lastUpdateTime");
            return (Criteria) this;
        }

        public Criteria andLastUpdateUsernameIsNull() {
            addCriterion("last_update_username is null");
            return (Criteria) this;
        }

        public Criteria andLastUpdateUsernameIsNotNull() {
            addCriterion("last_update_username is not null");
            return (Criteria) this;
        }

        public Criteria andLastUpdateUsernameEqualTo(String value) {
            addCriterion("last_update_username =", value, "lastUpdateUsername");
            return (Criteria) this;
        }

        public Criteria andLastUpdateUsernameNotEqualTo(String value) {
            addCriterion("last_update_username <>", value, "lastUpdateUsername");
            return (Criteria) this;
        }

        public Criteria andLastUpdateUsernameGreaterThan(String value) {
            addCriterion("last_update_username >", value, "lastUpdateUsername");
            return (Criteria) this;
        }

        public Criteria andLastUpdateUsernameGreaterThanOrEqualTo(String value) {
            addCriterion("last_update_username >=", value, "lastUpdateUsername");
            return (Criteria) this;
        }

        public Criteria andLastUpdateUsernameLessThan(String value) {
            addCriterion("last_update_username <", value, "lastUpdateUsername");
            return (Criteria) this;
        }

        public Criteria andLastUpdateUsernameLessThanOrEqualTo(String value) {
            addCriterion("last_update_username <=", value, "lastUpdateUsername");
            return (Criteria) this;
        }

        public Criteria andLastUpdateUsernameLike(String value) {
            addCriterion("last_update_username like", value, "lastUpdateUsername");
            return (Criteria) this;
        }

        public Criteria andLastUpdateUsernameNotLike(String value) {
            addCriterion("last_update_username not like", value, "lastUpdateUsername");
            return (Criteria) this;
        }

        public Criteria andLastUpdateUsernameIn(List<String> values) {
            addCriterion("last_update_username in", values, "lastUpdateUsername");
            return (Criteria) this;
        }

        public Criteria andLastUpdateUsernameNotIn(List<String> values) {
            addCriterion("last_update_username not in", values, "lastUpdateUsername");
            return (Criteria) this;
        }

        public Criteria andLastUpdateUsernameBetween(String value1, String value2) {
            addCriterion("last_update_username between", value1, value2, "lastUpdateUsername");
            return (Criteria) this;
        }

        public Criteria andLastUpdateUsernameNotBetween(String value1, String value2) {
            addCriterion("last_update_username not between", value1, value2, "lastUpdateUsername");
            return (Criteria) this;
        }

        public Criteria andLastUpdateUserIdIsNull() {
            addCriterion("last_update_user_id is null");
            return (Criteria) this;
        }

        public Criteria andLastUpdateUserIdIsNotNull() {
            addCriterion("last_update_user_id is not null");
            return (Criteria) this;
        }

        public Criteria andLastUpdateUserIdEqualTo(String value) {
            addCriterion("last_update_user_id =", value, "lastUpdateUserId");
            return (Criteria) this;
        }

        public Criteria andLastUpdateUserIdNotEqualTo(String value) {
            addCriterion("last_update_user_id <>", value, "lastUpdateUserId");
            return (Criteria) this;
        }

        public Criteria andLastUpdateUserIdGreaterThan(String value) {
            addCriterion("last_update_user_id >", value, "lastUpdateUserId");
            return (Criteria) this;
        }

        public Criteria andLastUpdateUserIdGreaterThanOrEqualTo(String value) {
            addCriterion("last_update_user_id >=", value, "lastUpdateUserId");
            return (Criteria) this;
        }

        public Criteria andLastUpdateUserIdLessThan(String value) {
            addCriterion("last_update_user_id <", value, "lastUpdateUserId");
            return (Criteria) this;
        }

        public Criteria andLastUpdateUserIdLessThanOrEqualTo(String value) {
            addCriterion("last_update_user_id <=", value, "lastUpdateUserId");
            return (Criteria) this;
        }

        public Criteria andLastUpdateUserIdLike(String value) {
            addCriterion("last_update_user_id like", value, "lastUpdateUserId");
            return (Criteria) this;
        }

        public Criteria andLastUpdateUserIdNotLike(String value) {
            addCriterion("last_update_user_id not like", value, "lastUpdateUserId");
            return (Criteria) this;
        }

        public Criteria andLastUpdateUserIdIn(List<String> values) {
            addCriterion("last_update_user_id in", values, "lastUpdateUserId");
            return (Criteria) this;
        }

        public Criteria andLastUpdateUserIdNotIn(List<String> values) {
            addCriterion("last_update_user_id not in", values, "lastUpdateUserId");
            return (Criteria) this;
        }

        public Criteria andLastUpdateUserIdBetween(String value1, String value2) {
            addCriterion("last_update_user_id between", value1, value2, "lastUpdateUserId");
            return (Criteria) this;
        }

        public Criteria andLastUpdateUserIdNotBetween(String value1, String value2) {
            addCriterion("last_update_user_id not between", value1, value2, "lastUpdateUserId");
            return (Criteria) this;
        }

        public Criteria andLastUpdateVersionIsNull() {
            addCriterion("last_update_version is null");
            return (Criteria) this;
        }

        public Criteria andLastUpdateVersionIsNotNull() {
            addCriterion("last_update_version is not null");
            return (Criteria) this;
        }

        public Criteria andLastUpdateVersionEqualTo(Integer value) {
            addCriterion("last_update_version =", value, "lastUpdateVersion");
            return (Criteria) this;
        }

        public Criteria andLastUpdateVersionNotEqualTo(Integer value) {
            addCriterion("last_update_version <>", value, "lastUpdateVersion");
            return (Criteria) this;
        }

        public Criteria andLastUpdateVersionGreaterThan(Integer value) {
            addCriterion("last_update_version >", value, "lastUpdateVersion");
            return (Criteria) this;
        }

        public Criteria andLastUpdateVersionGreaterThanOrEqualTo(Integer value) {
            addCriterion("last_update_version >=", value, "lastUpdateVersion");
            return (Criteria) this;
        }

        public Criteria andLastUpdateVersionLessThan(Integer value) {
            addCriterion("last_update_version <", value, "lastUpdateVersion");
            return (Criteria) this;
        }

        public Criteria andLastUpdateVersionLessThanOrEqualTo(Integer value) {
            addCriterion("last_update_version <=", value, "lastUpdateVersion");
            return (Criteria) this;
        }

        public Criteria andLastUpdateVersionIn(List<Integer> values) {
            addCriterion("last_update_version in", values, "lastUpdateVersion");
            return (Criteria) this;
        }

        public Criteria andLastUpdateVersionNotIn(List<Integer> values) {
            addCriterion("last_update_version not in", values, "lastUpdateVersion");
            return (Criteria) this;
        }

        public Criteria andLastUpdateVersionBetween(Integer value1, Integer value2) {
            addCriterion("last_update_version between", value1, value2, "lastUpdateVersion");
            return (Criteria) this;
        }

        public Criteria andLastUpdateVersionNotBetween(Integer value1, Integer value2) {
            addCriterion("last_update_version not between", value1, value2, "lastUpdateVersion");
            return (Criteria) this;
        }
    }

    /**
     * This class was generated by MyBatis Generator.
     * This class corresponds to the database table t_cf_air_pollutants
     *
     * @mbg.generated do_not_delete_during_merge
     */
    public static class Criteria extends GeneratedCriteria {

        protected Criteria() {
            super();
        }
    }

    /**
     * This class was generated by MyBatis Generator.
     * This class corresponds to the database table t_cf_air_pollutants
     *
     * @mbg.generated
     */
    public static class Criterion {
        private String condition;

        private Object value;

        private Object secondValue;

        private boolean noValue;

        private boolean singleValue;

        private boolean betweenValue;

        private boolean listValue;

        private String typeHandler;

        public String getCondition() {
            return condition;
        }

        public Object getValue() {
            return value;
        }

        public Object getSecondValue() {
            return secondValue;
        }

        public boolean isNoValue() {
            return noValue;
        }

        public boolean isSingleValue() {
            return singleValue;
        }

        public boolean isBetweenValue() {
            return betweenValue;
        }

        public boolean isListValue() {
            return listValue;
        }

        public String getTypeHandler() {
            return typeHandler;
        }

        protected Criterion(String condition) {
            super();
            this.condition = condition;
            this.typeHandler = null;
            this.noValue = true;
        }

        protected Criterion(String condition, Object value, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.typeHandler = typeHandler;
            if (value instanceof List<?>) {
                this.listValue = true;
            } else {
                this.singleValue = true;
            }
        }

        protected Criterion(String condition, Object value) {
            this(condition, value, null);
        }

        protected Criterion(String condition, Object value, Object secondValue, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.secondValue = secondValue;
            this.typeHandler = typeHandler;
            this.betweenValue = true;
        }

        protected Criterion(String condition, Object value, Object secondValue) {
            this(condition, value, secondValue, null);
        }
    }
}