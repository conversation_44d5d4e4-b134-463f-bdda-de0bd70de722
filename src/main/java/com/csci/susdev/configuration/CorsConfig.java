package com.csci.susdev.configuration;

import com.csci.susdev.constant.SusDevConsts;
import org.springframework.context.annotation.Configuration;
import org.springframework.web.servlet.config.annotation.CorsRegistry;
import org.springframework.web.servlet.config.annotation.WebMvcConfigurer;

@Configuration
public class CorsConfig implements WebMvcConfigurer {


    @Override
    public void addCorsMappings(CorsRegistry registry) {
        registry.addMapping("/**")
                .allowCredentials(true)
                .allowedOriginPatterns("*")
                .allowedMethods("*")
                .allowedHeaders("*", SusDevConsts.HEADER_TOKEN_KEY).exposedHeaders(SusDevConsts.HEADER_TOKEN_KEY);
    }

}  