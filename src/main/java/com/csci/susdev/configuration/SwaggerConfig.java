package com.csci.susdev.configuration;

import org.springdoc.core.GroupedOpenApi;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/**
 * 配置swagger
 *
 * <AUTHOR>
 * @date 2019-09-19
 */
@Configuration
public class SwaggerConfig {

    @Bean
    public GroupedOpenApi sampleApi() {
        return GroupedOpenApi.builder()
                .group("open-api")
                .pathsToMatch("/**")
                .build();
    }

}
