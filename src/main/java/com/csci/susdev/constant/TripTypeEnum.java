package com.csci.susdev.constant;

public enum TripTypeEnum {

    LONG_DISTANCE(1, "长途"),
    SHORT_DISTANCE(2, "短途"),
    // 国内
    DOMESTIC(3, "国内");

    private int code;

    private String name;

    TripTypeEnum(int code, String name) {
        this.code = code;
        this.name = name;
    }

    public int getCode() {
        return code;
    }

    public String getName() {
        return name;
    }
}
