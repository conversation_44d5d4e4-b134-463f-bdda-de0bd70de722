package com.csci.susdev.interceptor;

import com.csci.cohl.exception.BizException;
import com.csci.common.exception.ServiceException;
import com.csci.susdev.model.Operation;
import com.csci.susdev.service.IAuthService;
import com.csci.susdev.service.OperationService;
import com.csci.susdev.util.context.RequestContextManager;
import com.csci.susdev.util.context.model.UserInfo;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.servlet.handler.HandlerInterceptorAdapter;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.List;

@Component
public class AuthorityInterceptor extends HandlerInterceptorAdapter {

    @Resource
    private IAuthService authService;
    @Value("${server.servlet.context-path}")
    private String contextPath;
    @Value("${susdev.authentication.enabled:false}")
    private String authenticationEnabled;
    @Resource
    private OperationService operationService;

    @Override
    public boolean preHandle(HttpServletRequest request, HttpServletResponse response, Object handler) throws Exception {

        if (RequestMethod.OPTIONS.toString().equalsIgnoreCase(request.getMethod())) {
            return true;
        }
        String requestURI = request.getRequestURI().replace(contextPath, "");
        if (pass(requestURI)) {
            return true;
        }
        UserInfo userInfo = (UserInfo) RequestContextManager.getCurrent().getCurrentUser();
        if (userInfo == null) {
            response.sendError(HttpServletResponse.SC_UNAUTHORIZED,"未登入");
//            return false;
            throw new ServiceException("沒有訪問權限");
        }
        List<String> roles = userInfo.getRoles();
        if (roles == null || roles.isEmpty()) {
            response.sendError(HttpServletResponse.SC_FORBIDDEN,"沒有訪問權限");
            throw new ServiceException("沒有訪問權限");
        }
        boolean superAdmin = roles.contains("SuperAdmin");
        if (superAdmin) {
            return true;
        }
        List<Operation> operations = authService.getOperationsByRoles(roles);
        if (operations == null|| operations.isEmpty()) {
            response.sendError(HttpServletResponse.SC_FORBIDDEN);
            throw new ServiceException("沒有訪問權限");
        }
        for (Operation operation : operations) {
            String url = operation.getUrl();
            if (requestURI.contains(url.replace("{id}", ""))) {
                //todo: 这里可以添加权限检查的逻辑
                return true;
            }
        }
//        response.sendError(HttpServletResponse.SC_FORBIDDEN,"No permission");
        throw new ServiceException("沒有訪問權限");
    }

    private boolean pass(String uri) {

        if (!Boolean.parseBoolean(authenticationEnabled)) {
            return true;
        }
        List<Operation> ignoreOperations = operationService.getIgnoreOperations();
        if (ignoreOperations == null || ignoreOperations.isEmpty()) {
            return false;
        }
        int lastSlashIndex = uri.lastIndexOf('/');
        String result = uri.substring(0, lastSlashIndex + 1);
        for (Operation operation : ignoreOperations) {
            String url = operation.getUrl();
            if (uri.equals(url) || (url.contains("{id}") && result.equals(url.replace("{id}", "")))) {
                return true;
            }
        }

        return false;
    }
}
