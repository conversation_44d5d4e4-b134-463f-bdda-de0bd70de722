package com.csci.susdev.util.userinit;

import com.alibaba.excel.EasyExcel;
import com.csci.susdev.service.UserService;
import com.csci.susdev.util.SpringContextUtil;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import java.io.InputStream;

@Component
public class UserInitializer {

    /** 日志记录对象 */
    private static final Logger logger = LoggerFactory.getLogger(UserInitializer.class);

    public void initByFileName(String filename) {
        try (InputStream is = this.getClass().getClassLoader().getResourceAsStream(filename)) {
            EasyExcel.read(is, UserData.class, new UserInitListener()).sheet().doRead();
        } catch (Exception e) {
            logger.error("初始化出错", e);
        }
    }

    public void init() {
        // initByFileName("users/兴业.xlsx");
        // initByFileName("users/土木.xlsx");
        // initByFileName("users/房屋.xlsx");
        // initByFileName("users/国投.xlsx");
        // initByFileName("users/澳门.xlsx");
        // initByFileName("users/珠海.xlsx");
        initByFileName("users/AccountTemplate.xlsx");
    }

    public void initByUserData(UserData data) throws Exception {
        UserService userService = SpringContextUtil.getBean(UserService.class);
        userService.initUserByUserData(data);
    }
}
