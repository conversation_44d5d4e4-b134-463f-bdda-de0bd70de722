package com.csci.susdev.util;

import org.apache.commons.codec.binary.Base64;

import javax.crypto.Cipher;
import java.security.*;
import java.security.interfaces.RSAPublicKey;
import java.security.spec.PKCS8EncodedKeySpec;
import java.security.spec.X509EncodedKeySpec;

public class RsaUtil
{
    public static String publicKeyString = "MIGfMA0GCSqGSIb3DQEBAQUAA4GNADCBiQKBgQCGXG423pFrAhgDGtS1u70ggF+7XHIQc4Jfsv0OzrCob6Wyt8pmD0PJJipXw3NW82Q/kxnDxaE6fxdiQlLK0uLqDKEibxjuJ2tSmGCJmykD4gzM0GobY06ukH076dNhvap5d3oKfeG8XCpYH9lI2SvXcSWbpGRT1reU940xsO5onwIDAQAB";

    public static String privateKeyString = "MIICdgIBADANBgkqhkiG9w0BAQEFAASCAmAwggJcAgEAAoGBAIZcbjbekWsCGAMa1LW7vSCAX7tcchBzgl+y/Q7OsKhvpbK3ymYPQ8kmKlfDc1bzZD+TGcPFoTp/F2JCUsrS4uoMoSJvGO4na1KYYImbKQPiDMzQahtjTq6QfTvp02G9qnl3egp94bxcKlgf2UjZK9dxJZukZFPWt5T3jTGw7mifAgMBAAECgYBBwTBrB+dw0BiCRHo+6j73jfvLzMVBydXPEeCIg5yvAdy8pveVlPjekx/7zXo/3mN8PXhJeAmcgCAohT7RZf2IQGsRkMNs4viPdQf0tmgybye53uCfolJnAhYs/vlNPfw+0b759c/7Cfd75OrACrACqnEcbhsehebBuwrCuWm4WQJBAP+jdSnNiR1hkm37al0fURTZPC4nYlE9Ka9nIGhBf/bduv6mJXRGcGC29rq9wQ8TKTOMbiRseTVUHOJCZIGeiOsCQQCGjRHh69jyl40+urRxbOW9EaZvbbuTyci4dy5r+grniYXfh/NFT5c4EXgLSIYscpe6dZR4ovJaJhmatDPy8TIdAkEAweoLKQ+ZL+lguv7YuxTTW35BsTz8zmUX5s7SfWMaH3gorZv4k1APVL3VQOhJtxawzUJ8FjMWaoaIdnUoak6IywJATMtlYnm2+DbxgdUUOgy6TyAsyzppLh+kNUyorS4oXSBLzVoNygh0OacWyfHZyrKY0O5dEEGIa1WFlZu2brmlLQJAfBZviLtWBqx1XUVEn0XlUeeJxGMwwAf6DM0ozAyi8dMwzJt7sPmpFfMeZ80qzzF8ilu51UdByI3qSiOPccOG+A==";

    public static void main( String[] args ) throws Exception {
        //genKeyPair();
        //加密字符串
        String appId="hi_agent_ai";
        String appKey="DCC9B9AC-61DC-4917-BEF5-508321E280B8";
        System.out.println("原字符串为："+appId);

        System.out.println("公钥为："+publicKeyString);
        System.out.println("私钥为："+privateKeyString);

        String appIdEn=encrypt(appId,publicKeyString);
        System.out.println("加密后的字符串为："+appIdEn);
        String appIdDe=decrypt(appIdEn,privateKeyString);
        System.out.println("还原后的字符串为："+appIdDe);


        System.out.println("原字符串为："+appKey);

        System.out.println("公钥为："+publicKeyString);
        System.out.println("私钥为："+privateKeyString);

        String appKeyEn=encrypt(appKey,publicKeyString);
        System.out.println("加密后的字符串为："+appKeyEn);
        String appKeyDe=decrypt(appKeyEn,privateKeyString);
        System.out.println("还原后的字符串为："+appKeyDe);
    }
    /**
     * 随机生成密钥对
     * @throws NoSuchAlgorithmException
     */
    public static void genKeyPair() throws Exception {
        //KeyPairGenerator类用于生成公钥和密钥对，基于RSA算法生成对象
        KeyPairGenerator keyPairGen = KeyPairGenerator.getInstance("RSA");
        //初始化密钥对生成器，密钥大小为96-1024位
        keyPairGen.initialize(1024,new SecureRandom());
        //生成一个密钥对，保存在keyPair中
        KeyPair keyPair = keyPairGen.generateKeyPair();
        PrivateKey privateKey = keyPair.getPrivate();//得到私钥
        PublicKey publicKey = keyPair.getPublic();//得到公钥
        //得到公钥字符串
        publicKeyString = new String(Base64.encodeBase64(publicKey.getEncoded()));
        //得到私钥字符串
        privateKeyString = new String(Base64.encodeBase64(privateKey.getEncoded()));
    }
    /**
     * RSA公钥加密
     *
     * @param str
     *            加密字符串
     * @param publicKey
     *            公钥
     * @return 密文
     * @throws Exception
     *             加密过程中的异常信息
     */
    public static String encrypt(String str,String publicKey) throws Exception {
        //base64编码的公钥
        byte[] decoded = Base64.decodeBase64(publicKey);
        RSAPublicKey pubKey = (RSAPublicKey) KeyFactory.getInstance("RSA").generatePublic(new X509EncodedKeySpec(decoded));
        //Rsa加密
        Cipher cipher = Cipher.getInstance("RSA");
        cipher.init(Cipher.ENCRYPT_MODE,pubKey);
        String outStr = Base64.encodeBase64String(cipher.doFinal(str.getBytes("UTF-8")));
        return outStr;
    }

    /**
     * RSA私钥解密
     *
     * @param str
     *            加密字符串
     * @param privateKey
     *            私钥
     * @return 铭文
     * @throws Exception
     *             解密过程中的异常信息
     */
    public static String decrypt(String str,String privateKey) throws Exception {
        //Base64解码加密后的字符串
        byte[] inputByte = Base64.decodeBase64(str.getBytes("UTF-8"));
        //Base64编码的私钥
        byte[] decoded = Base64.decodeBase64(privateKey);
        PrivateKey priKey = KeyFactory.getInstance("RSA").generatePrivate(new PKCS8EncodedKeySpec(decoded));
        //RSA解密
        Cipher cipher = Cipher.getInstance("RSA");
        cipher.init(Cipher.DECRYPT_MODE,priKey);
        String outStr = new String(cipher.doFinal(inputByte));
        return outStr;

    }
}

