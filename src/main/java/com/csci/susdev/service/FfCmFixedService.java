package com.csci.susdev.service;

import cn.hutool.core.util.StrUtil;
import com.csci.common.exception.ServiceException;
import com.csci.susdev.annotation.LogMethod;
import com.csci.susdev.constant.SusDevConsts;
import com.csci.susdev.constant.WorkflowControlState;
import com.csci.susdev.mapper.*;
import com.csci.susdev.model.*;
import com.csci.susdev.modelcovt.FfCmFixedDetailConverter;
import com.csci.susdev.modelcovt.FfCmFixedHeadConverter;
import com.csci.susdev.modelcovt.FfCmFixedTableDataConverter;
import com.csci.susdev.qo.CeBasicInfoQO;
import com.csci.susdev.qo.FfCmFixedQO;
import com.csci.susdev.util.redis.RedisLockUtil;
import com.csci.susdev.util.SpringContextUtil;
import com.csci.susdev.vo.FfCmFixedDetailVO;
import com.csci.susdev.vo.FfCmFixedHeadVO;
import com.csci.susdev.vo.FfCmFixedTableDataVO;
import com.csci.susdev.vo.IdVersionVO;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.UUID;
import java.util.stream.Collectors;

import static com.csci.susdev.service.ServiceHelper.checkExist;

@Service
@LogMethod
public class FfCmFixedService {

    /**
     * 日志记录对象
     */
    private static final Logger logger = LoggerFactory.getLogger(FfCmFixedService.class);
    /**
     * 初始化记录的过期时间
     */
    @Value("${lock.init.head.expire.milis:1000}")
    private long lockInitHeadExpireMilis;
    /**
     * 查询head记录时，获取锁失败循环等待的次数
     */
    @Value("${query.head.wait.loop.times:5}")
    private int lockQueryHeadWaitLoopTimes;
    /**
     * 查询head记录时，获取锁失败循环时每次休眠的时间
     */
    @Value("${query.head.wait.sleep.milis:50}")
    private int lockQueryHeadWaitSleepMilis;

    @Resource
    private FfCmFixedHeadMapper ffCmFixedHeadMapper;

    @Resource
    private FfCmFixedDetailMapper ffCmFixedDetailMapper;

    @Resource
    private FfCmFixedCustomMapper ffCmFixedCustomMapper;

    /**
     * 根据查询条件查询分判商固定源信息
     * 只会查询 active = true 的记录
     *
     * @param organizationId 组织 id
     * @param year           年份
     * @param month          月份
     * @return
     */
    FfCmFixedHead getFfCmFixedHeadBy(String organizationId, Integer year, Integer month) {
        FfCmFixedHeadExample example = new FfCmFixedHeadExample();
        example.or().andOrganizationIdEqualTo(organizationId).andYearEqualTo(year)
                .andMonthEqualTo(month).andIsActiveEqualTo(Boolean.TRUE);
        return ffCmFixedHeadMapper.selectByExample(example).stream().findFirst().orElse(null);
    }

    /**
     * @param qo
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    public FfCmFixedHead createFfCmFixedHead(FfCmFixedQO qo) {
        FfCmFixedHead ffCmFixedHead = getFfCmFixedHeadBy(qo.getOrganizationId(), qo.getYear(),
                qo.getMonth());
        if (ffCmFixedHead == null) {
            ffCmFixedHead = new FfCmFixedHead();
            ffCmFixedHead.setOrganizationId(qo.getOrganizationId());
            ffCmFixedHead.setYear(qo.getYear());
            ffCmFixedHead.setMonth(qo.getMonth());
            ffCmFixedHead.setIsActive(Boolean.TRUE);
            ffCmFixedHead.setLastUpdateVersion(0);
            ffCmFixedHeadMapper.insertSelective(ffCmFixedHead);
        }
        return ffCmFixedHead;
    }

    /**
     * 根据查询条件查询分判商固定源信息，如果不存在则创建
     *
     * @param ffCmFixedQO 查询条件
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    public FfCmFixedHeadVO getOrInit(FfCmFixedQO ffCmFixedQO) {
        checkExist(ffCmFixedQO.getOrganizationId(), "组织编号不能为空");
        checkExist(ffCmFixedQO.getYear(), "年份不能为空");
        if(ObjectUtils.isEmpty(ffCmFixedQO.getMonth())) {
            ffCmFixedQO.setMonth(12);
        }

        if (ffCmFixedQO.getYear() < 1900 || ffCmFixedQO.getYear() > 2100) {
            throw new ServiceException("年份必须在 1900-2100 之间");
        }

        //查询或初始化记录，如果获取失败，直接抛出异常退出
        FfCmFixedHead ffCmFixedHead = getOrInitHeadRecord(ffCmFixedQO);


        // 查询流程审批状态
        WorkflowControlService workflowControlService = SpringContextUtil.getBean(WorkflowControlService.class);
        WorkflowControl workflowControl = workflowControlService.findWorkflowControlByBusinessId(ffCmFixedHead.getId());
        FfCmFixedHeadVO ffCmFixedHeadVO = FfCmFixedHeadConverter.convert(ffCmFixedHead);
        if (Objects.nonNull(workflowControl)) {
            ffCmFixedHeadVO.setWorkflowControlState(workflowControl.getState());
            ffCmFixedHeadVO.setWorkflowControlStateName(Optional.ofNullable(WorkflowControlState.getWorkflowControlState(workflowControl.getState())).map(WorkflowControlState::getName).orElse(null));
        }
        return ffCmFixedHeadVO;
    }

    /**
     * 查询或初始化记录，如果获取失败，直接抛出异常退出
     * @param
     * @return
     */
    private FfCmFixedHead getOrInitHeadRecord(FfCmFixedQO ffCmFixedQO) {
        FfCmFixedHead ffCmFixedHead = getFfCmFixedHeadBy(ffCmFixedQO.getOrganizationId(),
                ffCmFixedQO.getYear(), ffCmFixedQO.getMonth());
        if (Objects.isNull(ffCmFixedHead)) {
            //分布式锁的key：分别表示前缀:业务:组织ID:年:月
            String key = StrUtil.format(SusDevConsts.DISTRIBUTE_LOCK_KEY_FORMAT, SusDevConsts.PROJECT_PREFIX,
                    "FfCmMobileHead:getOrInit", ffCmFixedQO.getOrganizationId(),
                    ffCmFixedQO.getYear(), ffCmFixedQO.getMonth());
            //超时时间1s
            boolean redisLock = RedisLockUtil.lock(key, lockInitHeadExpireMilis);
            if (!redisLock) {
                //获取锁失败，停顿50ms一次，停顿5次
                int count = lockQueryHeadWaitLoopTimes;
                while(count-- > 0){
                    ffCmFixedHead = getFfCmFixedHeadBy(ffCmFixedQO.getOrganizationId(),
                            ffCmFixedQO.getYear(), ffCmFixedQO.getMonth());
                    if(ffCmFixedHead != null){
                        break;
                    }
                    try {
                        Thread.sleep(lockQueryHeadWaitSleepMilis);
                    } catch (InterruptedException e) {
                        throw new RuntimeException(e);
                    }
                }
            }else{
                try{
                    ffCmFixedHead = createFfCmFixedHead(ffCmFixedQO);
                    FfCmFixedHead prevHead = getPrevHead(ffCmFixedQO.getOrganizationId(), ffCmFixedQO.getYear(),
                            ffCmFixedQO.getMonth());
                    if (ObjectUtils.isNotEmpty(prevHead)) {
                        initDetailsWithPrevData(prevHead.getId(), ffCmFixedHead.getId());
                    }
                }
                finally {
                    RedisLockUtil.unlock(key);
                }
            }
        }
        //如果最终没拿到数据，直接返回
        if(ffCmFixedHead == null){
            throw new ServiceException("生成记录失败, 请稍后再尝试！");
        }
        return ffCmFixedHead;
    }

    public FfCmFixedHead getPrevHead(String organizationId, Integer year, Integer month) {
        FfCmFixedHeadExample example = new FfCmFixedHeadExample();
        example.or().andIsActiveEqualTo(true).andOrganizationIdEqualTo(organizationId)
                .andYearEqualTo(year).andMonthLessThan(month);
        example.setOrderByClause("month desc");
        List<FfCmFixedHead> lst = selectByExample(example);
        if(lst.size() > 0) {
            return lst.get(0);
        } else {
            return null;
        }
    }

    void initDetailsWithPrevData(String prevHeadId, String curHeadId) {
        FfCmFixedDetailExample example = new FfCmFixedDetailExample();
        example.or().andHeadIdEqualTo(prevHeadId);
        List<FfCmFixedDetail> details = ffCmFixedDetailMapper.selectByExample(example);
        for(FfCmFixedDetail detail : details) {
            detail.setId(null);
            detail.setHeadId(curHeadId);
            ffCmFixedDetailMapper.insert(detail);
        }
    }

    /**
     * 根据主表id查询分判商固定源明细信息
     *
     * @param headId 分判商固定源头 id
     * @return
     */
    public List<FfCmFixedDetailVO> listFfCmFixedDetail(String headId) {
        checkExist(headId, "分判商固定源主表id不能为空");
        FfCmFixedDetailExample example = new FfCmFixedDetailExample();
        example.or().andHeadIdEqualTo(headId);
        example.setOrderByClause("seq");
        return ffCmFixedDetailMapper.selectByExample(example).stream().map(new FfCmFixedDetailConverter()::convert).collect(Collectors.toList());
    }

    /**
     * 查询用于前端页面handsontable使用的数据
     *
     * @param headId 分判商固定源头 id
     * @return
     */
    public List<FfCmFixedTableDataVO> listFfCmFixedTable(String headId) {
        checkExist(headId, "分判商固定源主表id不能为空");
        FfCmFixedDetailExample example = new FfCmFixedDetailExample();
        example.or().andHeadIdEqualTo(headId);
        example.setOrderByClause("seq");
        return ffCmFixedDetailMapper.selectByExample(example).stream().map(FfCmFixedTableDataConverter::convert).collect(Collectors.toList());
    }

    public void summaryOrgData(String organizationId, Integer year, Integer month) {
        checkExist(organizationId, "organizationId不能为空");
        checkExist(year, "year不能为空");
        checkExist(month, "month不能为空");

        List<FfCmFixedDetail> lst = ffCmFixedCustomMapper.summaryOrgData(organizationId, year, month);
        if(lst == null || lst.size() == 0) {
            throw new ServiceException("找不到子組織數據");
        }
        deleteDetailByHeadId(lst.get(0).getHeadId());
        for (FfCmFixedDetail ffCmFixedDetail : lst) {
            ffCmFixedDetailMapper.insertSelective(ffCmFixedDetail);
        }
    }

    public static void deleteDetailByHeadId(String headId) {
        checkExist(headId, "headId is null");
        FfCmFixedDetailMapper ffCmFixedDetailMapper = SpringContextUtil.getBean(FfCmFixedDetailMapper.class);
        FfCmFixedDetailExample example = new FfCmFixedDetailExample();
        example.or().andHeadIdEqualTo(headId);
        ffCmFixedDetailMapper.deleteByExample(example);
    }

    /**
     * proxy for mapper method
     *
     * @param record
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    public int insertDetailSelective(FfCmFixedDetail record) {
        return ffCmFixedDetailMapper.insertSelective(record);
    }

    /**
     * 保存分判商固定源明细信息
     *
     * @param ffCmFixedHeadVO 分判商固定源头信息
     */
    @Transactional(rollbackFor = Exception.class)
    public void updateFfCmFixedWithDetail(FfCmFixedHeadVO ffCmFixedHeadVO) {
        WorkflowControlService workflowControlService = SpringContextUtil.getBean(WorkflowControlService.class);
        if (workflowControlService.isNotAllowEdit(ffCmFixedHeadVO.getId())) {
            throw new ServiceException("已经发起审批流程的单据不允许进行修改");
        }
        // 更新主表信息
        updateHead(ffCmFixedHeadVO);
        // 删除明细
        deleteDetailByHeadId(ffCmFixedHeadVO.getId());
        List<FfCmFixedDetail> lstDetails = ffCmFixedHeadVO.getDetails().stream().map(FfCmFixedTableDataConverter::convert).collect(Collectors.toList());

        addDetails(ffCmFixedHeadVO.getId(), lstDetails);
    }

    /**
     * 提交分判商固定源
     *
     * @param idVersionVO
     */
    public void submit(IdVersionVO idVersionVO) {
        // 验证逻辑---------
        checkExist(idVersionVO.getId(), "分判商固定源主表id不能为空");
        checkExist(idVersionVO.getLastUpdateVersion(), "分判商固定源主表版本号不能为空");

        FfCmFixedHead existedRecord = ffCmFixedHeadMapper.selectByPrimaryKey(idVersionVO.getId());
        Optional.ofNullable(existedRecord).orElseThrow(() -> new ServiceException("指定的分判商固定源记录不存在"));
        // 验证逻辑---------

        FfCmFixedHeadExample example = new FfCmFixedHeadExample();
        example.or().andIdEqualTo(idVersionVO.getId()).andLastUpdateVersionEqualTo(idVersionVO.getLastUpdateVersion());

        FfCmFixedHead record = new FfCmFixedHead();
        //record.setApproveStatus(ApproveStatus.SUBMIT.getCode());
        record.setLastUpdateVersion(idVersionVO.getLastUpdateVersion());

        int count = ffCmFixedHeadMapper.updateByExampleSelective(record, example);
        if (count == 0) {
            throw new ServiceException("分判商固定源已经被修改，请刷新后重试");
        }
        // todo 增加流程审批记录
    }

    private void addDetails(String headId, List<FfCmFixedDetail> ffCmFixedDetails) {
        if (CollectionUtils.isEmpty(ffCmFixedDetails)) {
            throw new ServiceException("分判商固定源明细不能为空");
        }
        int seq = 0;
        FfCmFixedDetail lastDetail = ffCmFixedDetails.get(0);
        for (FfCmFixedDetail ffCmFixedDetail : ffCmFixedDetails) {
            /*
            if (StringUtils.isNotBlank(ffCmFixedDetail.getCategory()) && !StringUtils.equals(ffCmFixedDetail.getCategory(), lastDetail.getCategory())) {
                lastDetail = ffCmFixedDetail;
            }
            if (StringUtils.isBlank(ffCmFixedDetail.getCategory())) {
                ffCmFixedDetail.setCategory(lastDetail.getCategory());
            }
             */
            ffCmFixedDetail.setId(UUID.randomUUID().toString());
            ffCmFixedDetail.setHeadId(headId);
            ffCmFixedDetail.setSeq(seq++);
            ffCmFixedDetailMapper.insertSelective(ffCmFixedDetail);
        }
    }

    private void updateHead(FfCmFixedHeadVO ffCmFixedHeadVO) {
        checkExist(ffCmFixedHeadVO.getId(), "分判商固定源主表id不能为空");
        checkExist(ffCmFixedHeadVO.getOrganizationId(), "组织编号不能为空");
        checkExist(ffCmFixedHeadVO.getLastUpdateVersion(), "版本号不能为空");

        FfCmFixedHeadExample headExample = new FfCmFixedHeadExample();
        headExample.or().andIdEqualTo(ffCmFixedHeadVO.getId()).andLastUpdateVersionEqualTo(ffCmFixedHeadVO.getLastUpdateVersion());

        FfCmFixedHead ffCmFixedHead = FfCmFixedHeadConverter.convert(ffCmFixedHeadVO);
        int updateCount = ffCmFixedHeadMapper.updateByExampleSelective(ffCmFixedHead, headExample);
        if (updateCount == 0) {
            throw new ServiceException("分判商固定源主表信息已经被修改，请刷新后重试");
        }
    }

    /**
     * proxy for mapper method
     *
     * @param id 分判商固定源主表id
     * @return
     */
    public FfCmFixedHead selectByPrimaryKey(String id) {
        return ffCmFixedHeadMapper.selectByPrimaryKey(id);
    }

    public List<FfCmFixedHead> selectByExample(FfCmFixedHeadExample example) {
        return ffCmFixedHeadMapper.selectByExample(example);
    }

}


