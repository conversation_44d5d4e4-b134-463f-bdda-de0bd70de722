package com.csci.susdev.service;

import com.csci.common.exception.ServiceException;
import com.csci.susdev.annotation.LogMethod;
import com.csci.susdev.constant.NamingConventionEnum;
import com.csci.susdev.mapper.ProtocolDetailCustomMapper;
import com.csci.susdev.mapper.ProtocolDetailMapper;
import com.csci.susdev.model.*;
import com.csci.susdev.modelcovt.ProtocolDetailConverter;
import com.csci.susdev.qo.ProtocolDetailQO;
import com.csci.susdev.util.MybatisHelper;
import com.csci.susdev.util.NamingConventionUtils;
import com.csci.susdev.util.SimpTradUtil;
import com.csci.susdev.vo.ProtocolDetailVO;
import com.github.pagehelper.PageHelper;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.lang.reflect.Field;
import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

import static com.csci.susdev.service.ServiceHelper.checkExist;

@Service
@LogMethod
public class ProtocolDetailService {

    @Resource
    private ProtocolDetailMapper protocolDetailMapper;

    @Resource
    private ProtocolDetailCustomMapper protocolDetailCustomMapper;

    @Resource
    private FactorScopeService factorScopeService;


    @Transactional(rollbackFor = Exception.class)
    public int insertSelective(ProtocolDetail record) {
        return protocolDetailMapper.insertSelective(record);
    }

    public ProtocolDetail selectByPrimaryKey(String id) {
        return protocolDetailMapper.selectByPrimaryKey(id);
    }

    public ProtocolDetailVO getProtocolDetail(String id) {
        checkExist(id, "id不能为空");
        ProtocolDetail protocolDetail = selectByPrimaryKey(id);
        checkExist(protocolDetail, "未找到对应的记录");
        if(protocolDetail.getIsDeleted()) {
            throw new ServiceException("紀錄已被刪除");
        }
        return ProtocolDetailConverter.convertToVO(protocolDetail);
    }


    @Transactional(rollbackFor = Exception.class)
    public String saveProtocolDetail(ProtocolDetailVO protocolDetailVO) {
        if (StringUtils.isBlank(protocolDetailVO.getId())) {
            // 新增
            return doAdd(protocolDetailVO);
        } else {
            checkExist(protocolDetailVO.getLastUpdateVersion(), "更新时版本号不能为空");
            // 修改
            return doUpdate(protocolDetailVO);
        }

    }

    public ResultPage<ProtocolDetailVO> listProtocolDetail(ProtocolDetailQO protocolDetailQO) {
        if (StringUtils.isNotBlank(protocolDetailQO.getOrderBy())) {
            List<String> fieldNames = Arrays.stream(ProtocolDetailVO.class.getDeclaredFields()).map(field -> field.getName()).collect(Collectors.toList());
            if(MybatisHelper.checkSqlInjectionForOrderBy(protocolDetailQO.getOrderBy(), fieldNames, NamingConventionEnum.CAMO)) {
                throw new ServiceException("orderBy 有SQL注入行為");
            }
        }

        PageHelper.startPage(protocolDetailQO.getCurPage(), protocolDetailQO.getPageSize());

        List<ProtocolDetailVO> protocolDetailVOs = protocolDetailCustomMapper.list(
                protocolDetailQO.getProtocolId(),
                protocolDetailQO.getCarbonEmissionLocationId(),
                protocolDetailQO.getOrderBy());
        return new ResultPage<>(protocolDetailVOs, true);
    }

    @Transactional(rollbackFor = Exception.class)
    public void deleteProtocolDetail(String id) {
        ProtocolDetail record = selectByPrimaryKey(id);

        FactorScopeExample example = new FactorScopeExample();
        example.or().andProtocolDetailIdEqualTo(id).andIsDeletedEqualTo(false);
        List<FactorScope> factorScopes = factorScopeService.selectByExample(example);
        if(factorScopes.size() > 0) {
            throw new ServiceException("已在因子範圍中被使用，不能刪除");
        }

        record.setIsDeleted(true);
        protocolDetailMapper.updateByPrimaryKeySelective(record);
    }

    private String doAdd(ProtocolDetailVO protocolDetailVO) {
        ProtocolDetail protocolDetail = ProtocolDetailConverter.convertToModel(protocolDetailVO);
        protocolDetailMapper.insertSelective(protocolDetail);
        return protocolDetail.getId();
    }

    private String doUpdate(ProtocolDetailVO protocolDetailVO) {
        ProtocolDetail originalRecord = selectByPrimaryKey(protocolDetailVO.getId());
        ProtocolDetail protocolDetail = ProtocolDetailConverter.convertToModelWithBase(protocolDetailVO, originalRecord);

        ProtocolDetailExample example = new ProtocolDetailExample();
        example.or().andIdEqualTo(protocolDetailVO.getId()).andLastUpdateVersionEqualTo(protocolDetailVO.getLastUpdateVersion());
        int updateCount = protocolDetailMapper.updateByExample(protocolDetail, example);
        if (updateCount == 0) {
            throw new ServiceException("更新失败，可能是数据已被修改，请刷新后重试");
        }
        return protocolDetail.getId();
    }

    public List<ProtocolDetail> selectByExample(ProtocolDetailExample example) {
        return protocolDetailMapper.selectByExample(example);
    }
}
