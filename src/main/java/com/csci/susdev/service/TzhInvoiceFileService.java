package com.csci.susdev.service;

import com.csci.common.exception.ServiceException;
import com.csci.susdev.annotation.DS;
import com.csci.susdev.annotation.LogMethod;
import com.csci.susdev.constant.DatasourceContextEnum;
import com.csci.susdev.util.context.ContextUtils;
import com.csci.susdev.util.context.model.UserInfo;
import com.csci.tzh.qo.TzhInvoiceFileQO;
import com.csci.tzh.mapper.TzhInvoiceFileMapper;
import com.csci.tzh.model.TzhInvoiceFile;
import com.csci.tzh.model.TzhInvoiceFileExample;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.time.LocalDateTime;
import java.util.List;
import java.util.UUID;

@Service
@DS(DatasourceContextEnum.TANZHONGHE)
@LogMethod
public class TzhInvoiceFileService {

	@Autowired
	private TzhInvoiceFileMapper mapper;

	@Transactional(rollbackFor = Exception.class)
	public int upload(TzhInvoiceFileQO qo, MultipartFile multipartFile) throws IOException {
		int result = 0;

		UserInfo userInfo = ContextUtils.getCurrentUser();
		String username = userInfo.getUsername();

		// 刪除記錄
		if(StringUtils.isNotEmpty(qo.getBillno())) {
			result += delete(qo.getBillno());
		}

		TzhInvoiceFile x = new TzhInvoiceFile();
		BeanUtils.copyProperties(qo, x);
		x.setId(UUID.randomUUID().toString());
		x.setSection(qo.getSection());
		x.setPdf(multipartFile.getBytes());
		x.setCreatedby(username);
		x.setCreatedtime(LocalDateTime.now());
		x.setIsdeleted(false);

		// 新增記錄
		result += mapper.insert(x);

		return result;
	}

	@Transactional(rollbackFor = Exception.class)
	public int delete(String billNo) {
		int result = 0;

		UserInfo userInfo = ContextUtils.getCurrentUser();
		String username = userInfo.getUsername();

		TzhInvoiceFileExample example = new TzhInvoiceFileExample();

		TzhInvoiceFileExample.Criteria criteria = example.or();

		criteria.andBillnoEqualTo(billNo);
		criteria.andIsdeletedEqualTo(false);

		TzhInvoiceFile x = new TzhInvoiceFile();
		x.setId(null);
		x.setIsdeleted(true);
		x.setDeletedby(username);
		x.setDeletedtime(LocalDateTime.now());
		result += mapper.updateByExampleSelective(x, example);

		if(result == 0) {
			throw new ServiceException("檔案不存在，無法刪除。");
		}

		return result;
	}


	public TzhInvoiceFile get(String billNo) {
		TzhInvoiceFileExample example = new TzhInvoiceFileExample();

		TzhInvoiceFileExample.Criteria criteria = example.or();
		if(StringUtils.isNotEmpty(billNo)) {
			criteria.andBillnoEqualTo(billNo);
		}
		criteria.andIsdeletedEqualTo(false);

		List<TzhInvoiceFile> lst = mapper.selectByExampleWithBLOBs(example);
		if(lst.size() > 0) {
			return lst.get(0);
		} else {
			return null;
		}
	}
}
