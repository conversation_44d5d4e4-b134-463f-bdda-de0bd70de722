package com.csci.susdev.service;

import com.csci.susdev.annotation.DS;
import com.csci.susdev.annotation.LogMethod;
import com.csci.susdev.constant.DatasourceContextEnum;
import com.csci.susdev.model.ResultPage;
import com.csci.susdev.util.context.ContextUtils;
import com.csci.susdev.util.context.model.UserInfo;
import com.csci.tzh.mapper.TzhOrgMaterialCarbonFactorW50CustomMapper;
import com.csci.tzh.mapper.TzhOrgMaterialCarbonFactorW50Mapper;
import com.csci.tzh.model.TzhOrgMaterialCarbonFactorW50;
import com.csci.tzh.model.TzhOrgMaterialCarbonFactorW50Example;
import com.csci.tzh.qo.TzhOrgMaterialCarbonFactorW50PageableQO;
import com.github.pagehelper.PageHelper;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.List;
import java.util.UUID;

@Service
@DS(DatasourceContextEnum.TANZHONGHE)
@LogMethod
public class TzhOrgMaterialCarbonFactorW50Service {

	@Autowired
	private TzhOrgMaterialCarbonFactorW50Mapper mapper;

	@Autowired
	private TzhOrgMaterialCarbonFactorW50CustomMapper customMapper;

	public List<TzhOrgMaterialCarbonFactorW50> selectByExample(TzhOrgMaterialCarbonFactorW50Example example) {
		return mapper.selectByExample(example);
	}

	public ResultPage<TzhOrgMaterialCarbonFactorW50> list(TzhOrgMaterialCarbonFactorW50PageableQO qo) {
		PageHelper.startPage(qo.getCurPage(), qo.getPageSize(), "OMCF.ChineseName");

		List<TzhOrgMaterialCarbonFactorW50> lst = customMapper.list(qo.getChineseName());
		ResultPage<TzhOrgMaterialCarbonFactorW50> resultPage = new ResultPage<>(lst, true);

		return resultPage;
	}

	@Transactional(rollbackFor = Exception.class)
	public TzhOrgMaterialCarbonFactorW50 save(TzhOrgMaterialCarbonFactorW50 newModel) {
		UserInfo currentUser = ContextUtils.getCurrentUser();

		if(newModel.getIsdeleted() == false) {
			if(StringUtils.isNotBlank(newModel.getId())) {
				// 備份已刪除數據
				TzhOrgMaterialCarbonFactorW50Example originalExample = new TzhOrgMaterialCarbonFactorW50Example();
				TzhOrgMaterialCarbonFactorW50Example.Criteria originalCriteria = originalExample.or();
				originalCriteria.andIdEqualTo(newModel.getId());
				List<TzhOrgMaterialCarbonFactorW50> lstOriginalModel = mapper.selectByExample(originalExample);
				if(lstOriginalModel.size() > 0) {
					TzhOrgMaterialCarbonFactorW50 originalModel = lstOriginalModel.get(0);
					originalModel.setId(UUID.randomUUID().toString());
					originalModel.setIsdeleted(true);
					originalModel.setDeletedby(currentUser.getUsername());
					originalModel.setDeletedtime(LocalDateTime.now());
					mapper.insertSelective(originalModel);
				}

				// 新增數據(保留舊ID)
				TzhOrgMaterialCarbonFactorW50Example newExample = new TzhOrgMaterialCarbonFactorW50Example();
				TzhOrgMaterialCarbonFactorW50Example.Criteria newCriteria = newExample.or();
				newCriteria.andIdEqualTo(newModel.getId());
				newCriteria.andIsdeletedEqualTo(false);
				newModel.setCreatedby(currentUser.getUsername());
				newModel.setCreatedtime(LocalDateTime.now());
				newModel.setDeletedby(null);
				newModel.setDeletedtime(null);
				mapper.deleteByExample(newExample);
				mapper.insertSelective(newModel);
			} else {
				// 新增數據(新建ID)
				newModel.setId(UUID.randomUUID().toString());
				newModel.setCreatedby(currentUser.getUsername());
				newModel.setCreatedtime(LocalDateTime.now());
				mapper.insertSelective(newModel);
			}
		} else {
			// 刪除數據
			TzhOrgMaterialCarbonFactorW50Example example = new TzhOrgMaterialCarbonFactorW50Example();
			TzhOrgMaterialCarbonFactorW50Example.Criteria criteria = example.or();
			criteria.andIdEqualTo(newModel.getId());
			newModel.setIsdeleted(true);
			newModel.setDeletedby(currentUser.getUsername());
			newModel.setDeletedtime(LocalDateTime.now());
			mapper.updateByExampleSelective(newModel, example);
		}

		return newModel;
	}
}
