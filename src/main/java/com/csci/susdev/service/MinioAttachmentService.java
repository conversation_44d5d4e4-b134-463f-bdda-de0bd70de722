package com.csci.susdev.service;


import com.csci.common.exception.ServiceException;
import com.csci.susdev.annotation.DS;
import com.csci.susdev.annotation.LogMethod;
import com.csci.susdev.constant.DatasourceContextEnum;
import com.csci.susdev.mapper.MinioAttachmentMapper;
import com.csci.susdev.model.Attachment;
import com.csci.susdev.model.AttachmentExample;
import com.csci.susdev.model.MinioAttachment;
import com.csci.susdev.model.MinioAttachmentExample;
import com.csci.susdev.qo.MinioAttachmentListQO;
import com.csci.susdev.qo.MinioAttachmentQO;
import com.csci.susdev.util.FileUtil;
import com.csci.susdev.util.MinioUtil;
import com.csci.susdev.util.context.ContextUtils;
import com.csci.susdev.util.context.model.UserInfo;
import com.csci.susdev.vo.AttachmentVO;
import com.csci.susdev.vo.MinioAttachmentVO;
import com.csci.susdev.vo.StreamResultVO;
import org.apache.catalina.connector.Response;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.time.LocalDateTime;
import java.util.Arrays;
import java.util.List;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;


@Service
@DS(DatasourceContextEnum.SUSDEV)
@LogMethod
public class MinioAttachmentService {

    @Resource
    private MinioAttachmentMapper mapper;
    @Autowired
    private MinioUtil minioUtil;

    private static final int PREVIEW_URL_EXPIRATION_SECONDS = 3600;

    @Transactional(rollbackFor = Exception.class)
    public String upload(MinioAttachmentQO qo, MultipartFile multipartFile) throws Exception {
        String[] availableTypes = {"jpg","jpeg","png","gif", "pdf", "doc", "docx", "xls", "xlsx"};
        if (!Arrays.asList(availableTypes).contains(FileUtil.getFileType(multipartFile.getBytes()))){
                throw new ServiceException("文件類型與副案名不匹配，或文件損壞，請重新上傳。");
        }

    	int result = 0;

        UserInfo userInfo = ContextUtils.getCurrentUser();

        if(StringUtils.isNotEmpty(qo.getId())) {
            this.delete(qo.getId());
        }
        // 上传到Minio
        String minioFileName = minioUtil.upload(multipartFile);
        if (StringUtils.isEmpty(minioFileName)) {
            throw new ServiceException("文件上傳失敗，請重新上傳。");
        }

        // 生成模型
        String newId = qo.getId();
        MinioAttachment x = new MinioAttachment();
    	BeanUtils.copyProperties(qo, x);
    	x.setId(newId);
        x.setMinioFileName(minioFileName);
        x.setCreateUserId(userInfo.getId());
    	x.setCreateUsername(userInfo.getUsername());
    	x.setCreationTime(LocalDateTime.now());

    	// 新增新的記錄
    	result += mapper.insert(x);

        return newId;
    }

    @Transactional(rollbackFor = Exception.class)
    public int delete(String id) {
    	int result = 0;

        UserInfo userInfo = ContextUtils.getCurrentUser();

        MinioAttachmentExample example = new MinioAttachmentExample();

        MinioAttachmentExample.Criteria criteria = example.or();
        criteria.andIdEqualTo(id);

        MinioAttachment x = new MinioAttachment();
        List<MinioAttachment> lst = mapper.selectByExample(example);
        if(lst.size() > 0) {
            x = lst.get(0);
        }
        result += mapper.deleteByExample(example);

        if(result == 0) {
            throw new ServiceException("檔案不存在，無法刪除。");
        }

        if (x != null) {
            // 刪除Minio上面的文件
            Boolean deleteFlag = minioUtil.delete(x.getMinioFileName());
            if (!deleteFlag) {
                throw new ServiceException("附件刪除失敗。");
            }
        }

        return result;
    }

    public MinioAttachment get(String id) {
        MinioAttachmentExample example = new MinioAttachmentExample();

        MinioAttachmentExample.Criteria criteria = example.or();
        criteria.andIdEqualTo(id);

        List<MinioAttachment> lst = mapper.selectByExample(example);
        if(lst.size() > 0) {
            return lst.get(0);
        } else {
            return null;
        }
    }

    public List<MinioAttachmentVO> list(MinioAttachmentListQO qo) {
        MinioAttachmentExample example = new MinioAttachmentExample();

        MinioAttachmentExample.Criteria criteria = example.or();
        if(StringUtils.isNotBlank(qo.getRefId())) {
            criteria.andRefIdLike(qo.getRefId());
        }
        if(StringUtils.isNotBlank(qo.getSection())) {
            criteria.andSectionLike(qo.getSection());
        }
        if(StringUtils.isNotBlank(qo.getCategory())) {
            criteria.andCategoryLike("%" + qo.getCategory() + "%");
        }

        example.setOrderByClause("last_update_time desc");

        List<MinioAttachment> lst = mapper.selectByExample(example);

		List<MinioAttachmentVO> lstVO = lst.stream().map(x -> {
            MinioAttachmentVO vo = new MinioAttachmentVO();
            BeanUtils.copyProperties(x, vo);
            return vo;
        }).collect(Collectors.toList());
        return lstVO;
    }


    public String getFileUrl(String minioFileName) {
        return minioUtil.getExpireFileUrl(minioFileName, PREVIEW_URL_EXPIRATION_SECONDS, TimeUnit.SECONDS);
    }

    public byte[] getObject(String minioFileName) {
        return minioUtil.getObject(minioFileName);
    }

    public byte[] batchDownloadFiles(List<String> minioFileNames) {
        return minioUtil.batchDownloadFiles(minioFileNames);
    }

    public StreamResultVO getObjectStream(String minioFileName){
        return minioUtil.getObjectStream(minioFileName);
    }
}
