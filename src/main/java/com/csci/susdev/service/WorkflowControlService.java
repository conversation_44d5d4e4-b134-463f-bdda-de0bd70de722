package com.csci.susdev.service;

import cn.hutool.core.math.MathUtil;
import com.csci.common.exception.ServiceException;
import com.csci.susdev.annotation.LogMethod;
import com.csci.susdev.constant.WorkflowControlState;
import com.csci.susdev.mapper.*;
import com.csci.susdev.model.*;
import com.csci.susdev.qo.WorkflowControlQO;
import com.csci.susdev.util.DateUtils;
import com.csci.susdev.util.NotificationUtil;
import com.csci.susdev.util.context.ContextUtils;
import com.csci.susdev.util.context.model.UserInfo;
import com.csci.susdev.vo.UnSubmitWorkflowExportData;
import com.csci.susdev.vo.WorkFlowControlUrgentEditableVO;
import com.csci.susdev.vo.WorkflowControlVO;
import com.csci.susdev.vo.WorkflowNodeVO;
import com.github.pagehelper.PageHelper;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.math3.util.MathUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.stream.Collectors;

import static com.csci.susdev.service.ServiceHelper.checkExist;
import static com.csci.susdev.service.ServiceHelper.listSortedWorkflowNodes;

@Service
@LogMethod
public class WorkflowControlService {

    @Resource
    private OrganizationMapper organizationMapper;

    @Resource
    private FormMapper formMapper;

    @Resource
    private WorkflowMapper workflowMapper;

    @Resource
    private WorkflowControlMapper workflowControlMapper;

    @Resource
    private WorkflowCustomMapper workflowCustomMapper;

    @Resource
    private WorkflowNodeMapper workflowNodeMapper;

    @Resource
    private WorkflowNodeUserMapper workflowNodeUserMapper;

    @Resource
    private NotificationUtil notificationUtil;

    /**
     * proxy for {@link WorkflowControlMapper#insertSelective(WorkflowControl)}
     *
     * @param record
     * @return
     */
    public int insertSelective(WorkflowControl record) {
        return workflowControlMapper.insertSelective(record);
    }

    /**
     * 判断指定流程记录是否已经存在
     *
     * @param workflowId 流程id
     * @param businessId 业务id
     * @return
     */
    public boolean isWorkflowControlExist(String workflowId, String businessId) {
        WorkflowControlExample example = new WorkflowControlExample();
        example.or().andIsActiveEqualTo(Boolean.TRUE).andWorkflowIdEqualTo(workflowId).andBusinessIdEqualTo(businessId);
        return workflowControlMapper.countByExample(example) > 0;
    }


    public boolean isNotAllowEdit(String businessId) {
        WorkflowControlExample example = new WorkflowControlExample();

        example.or().andIsActiveEqualTo(Boolean.TRUE).andIsUrgentEditableEqualTo(Boolean.FALSE).andBusinessIdEqualTo(businessId);
        return workflowControlMapper.countByExample(example) > 0;
    }

    public void throwIfHasWorkflowControl(String businessId, String errorMsg) {
        if (isNotAllowEdit(businessId)) {
            if (StringUtils.isBlank(errorMsg)) {
                throw new ServiceException("该业务单据已处于审批中，不能再提交更新");
            }
            throw new ServiceException(errorMsg);
        }
    }

    /**
     * 判断是否允许发起流程
     * 如果拥有状态为
     * 1, "进行中"
     * 2, "已完成"
     * 的流程记录，则不允许发起流程，否则允许发起流程
     *
     * @param workflowId 流程id
     * @param businessId 业务id
     * @return
     */
    public boolean isAllowToStartWorkflow(String workflowId, String businessId) {
        WorkflowControlExample example = new WorkflowControlExample();
        // WorkflowControlStateEnum.IN_PROGRESS.getCode(), WorkflowControlStateEnum.FINISHED.getCode()
        example.or().andIsActiveEqualTo(Boolean.TRUE).andWorkflowIdEqualTo(workflowId).andBusinessIdEqualTo(businessId).andStateIn(Arrays.asList(1, 2));
        return workflowControlMapper.countByExample(example) == 0;
    }

    /**
     * 查詢指定流程指定業務單據的流程控制記錄
     *
     * @param workflowId 流程id
     * @param businessId 业务id
     * @return
     */
    @Deprecated
    public WorkflowControl findWorkflowControlByBusinessId(String workflowId, String businessId) {
        WorkflowControlExample example = new WorkflowControlExample();
        example.or().andIsActiveEqualTo(Boolean.TRUE).andWorkflowIdEqualTo(workflowId).andBusinessIdEqualTo(businessId);
        List<WorkflowControl> lstFlowControl = workflowControlMapper.selectByExample(example);
        if (CollectionUtils.isEmpty(lstFlowControl)) {
            return null;
        }
        if (lstFlowControl.size() > 1) {
            throw new ServiceException("存在多条流程控制记录, 請檢查數據");
        }
        return lstFlowControl.get(0);
    }

    /**
     * proxy for {@link WorkflowControlMapper#updateByPrimaryKeySelective(WorkflowControl)}
     *
     * @param record 流程控制记录
     * @return
     */
    public int updateByPrimaryKeySelective(WorkflowControl record) {
        return workflowControlMapper.updateByPrimaryKeySelective(record);
    }

    /**
     * 根据业务id查询流程控制记录
     *
     * @param businessId 业务id
     * @return
     */
    public WorkflowControl findWorkflowControlByBusinessId(String businessId) {
        WorkflowControlExample example = new WorkflowControlExample();
        example.or().andIsActiveEqualTo(Boolean.TRUE).andBusinessIdEqualTo(businessId);
        List<WorkflowControl> lstFlowControl = workflowControlMapper.selectByExample(example);
        if (CollectionUtils.isEmpty(lstFlowControl)) {
            return null;
        }
        if (lstFlowControl.size() > 1) {
            throw new ServiceException("存在多条流程控制记录, 請檢查數據");
        }
        return lstFlowControl.get(0);
    }

    public WorkflowControl findWorkflowControlByWorkflowId(String workflowId) {
        WorkflowControlExample example = new WorkflowControlExample();
        example.or().andIsActiveEqualTo(Boolean.TRUE).andWorkflowIdEqualTo(workflowId);
        List<WorkflowControl> lstFlowControl = workflowControlMapper.selectByExample(example);
        if (CollectionUtils.isEmpty(lstFlowControl)) {
            return null;
        }
        return lstFlowControl.get(0);
    }

    public Integer findFormYearByBusinessId(String businessId) {
        return workflowCustomMapper.selectFormYearByBusinessId(businessId);
    }

    public Integer findFormMonthByBusinessId(String businessId) {
        return workflowCustomMapper.selectFormMonthByBusinessId(businessId);
    }

    public ResultPage<WorkflowControlVO> listWorkflowControlByPage(WorkflowControlQO workflowControlQO) {
        workflowControlQO.setUserId(ContextUtils.getCurrentUser().getId());
        return listWorkflowControl(workflowControlQO);
    }

    public ResultPage<WorkflowControlVO> listWorkflowControlExactByPage(WorkflowControlQO workflowControlQO) {
        workflowControlQO.setUserId(ContextUtils.getCurrentUser().getId());
        return listWorkflowControlExact(workflowControlQO);
    }

    private ResultPage<WorkflowControlVO> listWorkflowControlExact(WorkflowControlQO workflowControlQO) {
        PageHelper.startPage(workflowControlQO.getCurPage(), workflowControlQO.getPageSize());
        List<WorkflowControlVO> lstWorkflowControlVO = workflowCustomMapper.selectWorkflowControlExact(workflowControlQO);
        for(WorkflowControlVO vo : lstWorkflowControlVO) {
            UserInfo curUserInfo = ContextUtils.getCurrentUser();
            if(vo.getRepresentativeId() == null || vo.getRepresentativeId() == curUserInfo.getId()) {
                vo.setIsAllowSubmit(true);
            } else {
                vo.setIsAllowSubmit(false);
            }
        }

        ResultPage<WorkflowControlVO> resultPage = new ResultPage<>(lstWorkflowControlVO);

        lstWorkflowControlVO.forEach(x -> {
            x.setStateName(Optional.ofNullable(WorkflowControlState.getWorkflowControlState(x.getState())).map(WorkflowControlState::getName).orElse(null));

            List<WorkflowNodeVO> lstNodes = new ArrayList<>();
            WorkflowNodeVO startNode = new WorkflowNodeVO();
            // node.setId();
            startNode.setWorkflowId(x.getWorkflowId());
            startNode.setName("发起人");
            // node.setPreviousNodeId();
            startNode.setIsBeginNode(Boolean.TRUE);
            startNode.setUserId(x.getCreateUserId());
            startNode.setUserRealName(x.getCreateUserRealName());
            startNode.setUserName(x.getCreateUserName());
            startNode.setUserMobile(x.getCreateUserMobile());
            startNode.setLastUpdateTime(x.getLastUpdateTime());
            if(startNode.getLastUpdateTime() != null){
                startNode.setLastUpdateTimeStr(startNode.getLastUpdateTime()
                        .format(DateTimeFormatter.ofPattern(DateUtils.DEFAULT_DATE_TIME_FORMAT)));
            }
            lstNodes.add(startNode);

            lstNodes.addAll(listSortedWorkflowNodes(x.getWorkflowId()));
            WorkflowNodeVO currentNode = lstNodes.stream().filter(y -> y.getId() != null
                    && y.getId().equals(x.getCurrentNodeId())).findFirst().orElse(null);
            //1.未提交状态，所有节点时间都为空 2.审批中，将未处理的节点的最新时间清空，包括当前节点 3.已完成，保留所有节点最新时间,无需处理
            if(Objects.equals(WorkflowControlState.NOT_STARTED.getCode(), x.getState())){
                //未提交状态，所有节点时间都为空
                lstNodes.forEach(it -> {
                    it.setLastUpdateTimeStr(""); //未处理过的节点，时间为空
                    it.setLastUpdateTime(null);
                });
            } else if(Objects.equals(WorkflowControlState.IN_PROGRESS.getCode(), x.getState())){
                //审批中状态下，将当前节点之后的节点的时间都改成空
                String currentNodeId = x.getCurrentNodeId();
                //标记该节点是否已经处理过
                boolean processed = true;
                if(StringUtils.isNotBlank(currentNodeId)){
                    for(WorkflowNodeVO vo : lstNodes){
                        if(currentNodeId.equalsIgnoreCase(vo.getId())){
                            processed = false;
                        }
                        if(!processed){
                            vo.setLastUpdateTimeStr("");
                            vo.setLastUpdateTime(null);
                        }
                    }
                }
            }
            x.setWorkflowNodes(lstNodes);

            if(currentNode != null){
                x.setCurrentNodeUserName(currentNode.getUserRealName());
            }
        });
        /*
        lstWorkflowControlVO.forEach(x -> {
            x.setWorkflowNodes(listSortedWorkflowNodes(x.getWorkflowId()));
        });
         */
        resultPage.setList(lstWorkflowControlVO);
        return resultPage;
    }

    private ResultPage<WorkflowControlVO> listWorkflowControl(WorkflowControlQO workflowControlQO) {
        PageHelper.startPage(workflowControlQO.getCurPage(), workflowControlQO.getPageSize());
        List<WorkflowControlVO> lstWorkflowControlVO = workflowCustomMapper.selectWorkflowControl(workflowControlQO);
        ResultPage<WorkflowControlVO> resultPage = new ResultPage<>(lstWorkflowControlVO);


        lstWorkflowControlVO.forEach(x -> {
            x.setStateName(Optional.ofNullable(WorkflowControlState.getWorkflowControlState(x.getState())).map(WorkflowControlState::getName).orElse(null));

            List<WorkflowNodeVO> lstNodes = new ArrayList<>();
            WorkflowNodeVO startNode = new WorkflowNodeVO();
            // node.setId();
            startNode.setWorkflowId(x.getWorkflowId());
            startNode.setName("发起人");
            // node.setPreviousNodeId();
            startNode.setIsBeginNode(Boolean.TRUE);
            startNode.setUserId(x.getCreateUserId());
            startNode.setUserRealName(x.getCreateUserRealName());
            startNode.setUserName(x.getCreateUserName());
            startNode.setUserMobile(x.getCreateUserMobile());
            //发起人的最新更新时间为流程的创建时间
            List<WorkflowNodeVO> lstWorkflowNode = workflowCustomMapper.selectWorkflowNodeByWorkflowId(x.getWorkflowId());
            if (!CollectionUtils.isEmpty(lstWorkflowNode)) {
                WorkflowNodeVO beginNode = lstWorkflowNode.stream().filter(node -> Objects.equals(node.getIsBeginNode(), Boolean.TRUE)).findFirst().orElse(null);
                startNode.setLastUpdateTime(beginNode.getLastUpdateTime());
            }
            startNode.setLastUpdateTimeStr("");
            if(startNode.getLastUpdateTime() != null){
                startNode.setLastUpdateTimeStr(startNode.getLastUpdateTime()
                        .format(DateTimeFormatter.ofPattern(DateUtils.DEFAULT_DATE_TIME_FORMAT)));
            }
            lstNodes.add(startNode);
            lstNodes.addAll(listSortedWorkflowNodes(x.getWorkflowId()));
            int idx = 0;
            for (int i = 1; i < lstNodes.size() + 1; i++) {
                WorkflowNodeVO nodeVO = lstNodes.get(i-1);
                if (StringUtils.equals(x.getCurrentNodeId(), nodeVO.getId())) {
                    idx = i;
                }
                if (x.getState() == 0) {
                    nodeVO.setLastUpdateTime(null);
                    nodeVO.setLastUpdateTimeStr("");
                    continue;
                }

                if (idx == i && x.getState() != 2) {
                    nodeVO.setLastUpdateTime(null);
                    nodeVO.setLastUpdateTimeStr("");
                    continue;
                }
                if (i > idx && idx != 0) {
                    nodeVO.setLastUpdateTime(null);
                    nodeVO.setLastUpdateTimeStr("");
                }
            }

            x.setWorkflowNodes(lstNodes);
        });
        /*
        lstWorkflowControlVO.forEach(x -> {
            x.setWorkflowNodes(listSortedWorkflowNodes(x.getWorkflowId()));
        });
         */
        resultPage.setList(lstWorkflowControlVO);
        return resultPage;
    }

    public List<WorkflowControlVO> listWorkflowControlVOByCurrentUser() {
        String userId = ContextUtils.getCurrentUser().getId();
        List<WorkflowControlVO> lstWorkflowControlVO = workflowCustomMapper.selectWorkflowControlByUserId(userId);

        lstWorkflowControlVO.forEach(x -> {
            List<WorkflowNodeVO> lstNodes = new ArrayList<>();
            WorkflowNodeVO startNode = new WorkflowNodeVO();
            startNode.setWorkflowId(x.getWorkflowId());
            startNode.setName("发起人");
            startNode.setIsBeginNode(Boolean.TRUE);
            startNode.setUserId(x.getCreateUserId());
            startNode.setUserRealName(x.getCreateUserRealName());
            lstNodes.add(startNode);
            lstNodes.addAll(listSortedWorkflowNodes(x.getWorkflowId()));
            x.setWorkflowNodes(lstNodes);
        });

        return lstWorkflowControlVO;
    }

    public List<WorkflowControlVO> listWorkflowControlVOByBusinessId(String businessId) {
        List<WorkflowControlVO> lstWorkflowControlVO = workflowCustomMapper.selectWorkflowControlByBusinessId(businessId);

        lstWorkflowControlVO.forEach(x -> {
            List<WorkflowNodeVO> lstNodes = new ArrayList<>();
            WorkflowNodeVO startNode = new WorkflowNodeVO();
            // node.setId();
            startNode.setWorkflowId(x.getWorkflowId());
            startNode.setName("发起人");
            // node.setPreviousNodeId();
            startNode.setIsBeginNode(Boolean.TRUE);
            startNode.setUserId(x.getCreateUserId());
            startNode.setUserRealName(x.getCreateUserRealName());
            lstNodes.add(startNode);
            lstNodes.addAll(listSortedWorkflowNodes(x.getWorkflowId()));
            x.setWorkflowNodes(lstNodes);
        });

        return lstWorkflowControlVO;
    }

    public void sendReviewEmail(String businessId) {
        List<WorkflowControlVO> lstWorkflowControlVO = listWorkflowControlVOByBusinessId(businessId);

        if(lstWorkflowControlVO != null && lstWorkflowControlVO.size() > 0) {
            WorkflowControlVO workflowControlVO = lstWorkflowControlVO.get(0);
            List<String> lstTo = new ArrayList<>();

            if(StringUtils.isNotBlank(workflowControlVO.getCurrentNodeUserEmail())) {
                lstTo.add(workflowControlVO.getCurrentNodeUserEmail());
            }

            if(lstTo.size() > 0) {
                notificationUtil.sendMail(lstTo, "[可持續發展及碳中和系統]你有表單需要審核 ( 表單名稱: " + workflowControlVO.getFormName() + ")", "詳情請登入系統後查看右上角審核提示: " + "https://esg.csci.com.hk/");
            }
        }
    }


    /**
     * 查詢指定業務單據編號對應的流程控制記錄
     *
     * @param businessId 业务id
     * @return
     */
    public WorkflowControlVO getWorkflowControlByBusinessId(String businessId) {

        WorkflowControl workflowControl = findWorkflowControlByBusinessId(businessId);

        if (workflowControl == null) {
            return null;
        }
        WorkflowControlVO workflowControlVO = new WorkflowControlVO();
        BeanUtils.copyProperties(workflowControl, workflowControlVO);

        // 查詢對應的流程定義
        Workflow workflow = workflowMapper.selectByPrimaryKey(workflowControl.getWorkflowId());

        // 流程對應的組織機構
        Organization organization = organizationMapper.selectByPrimaryKey(workflow.getOrganizationId());
        if (organization != null) {
            workflowControlVO.setOrganizationId(organization.getId());
            workflowControlVO.setOrganizationName(organization.getName());
        }

        // 流程對應的表單
        Form form = formMapper.selectByPrimaryKey(workflow.getFormId());
        if (form != null) {
            workflowControlVO.setFormId(form.getId());
            workflowControlVO.setFormName(form.getName());
        }

        // 流程對應的節點
        WorkflowNode currentWorkflowNode = workflowNodeMapper.selectByPrimaryKey(workflowControl.getCurrentNodeId());

        if (Objects.nonNull(currentWorkflowNode)) {
            workflowControlVO.setCurrentNodeId(currentWorkflowNode.getId());
            workflowControlVO.setCurrentNodeName(currentWorkflowNode.getName());

            User user = workflowCustomMapper.getWorkFlowNodeUserByNodeId(currentWorkflowNode.getId());
            if(user != null){
                workflowControlVO.setCurrentNodeUserName(user.getName());
            }
        }
        // 流程審批狀態
        workflowControlVO.setStateName(Optional.ofNullable(WorkflowControlState.getWorkflowControlState(workflowControl.getState())).map(WorkflowControlState::getName).orElse(null));

        return workflowControlVO;
    }

    public int setUrgentEditable(WorkFlowControlUrgentEditableVO vo) {
        WorkflowControl updateWorkflowControl = workflowControlMapper.selectByPrimaryKey(vo.getId());
        updateWorkflowControl.setIsUrgentEditable(vo.getIsUrgentEditable());
        return updateByPrimaryKeySelective(updateWorkflowControl);
    }

    public List<UnSubmitWorkflowExportData> selectUnSubmitWorkflowControl(WorkflowControlQO qo) {
        checkExist(qo.getYear(), "年份不能为空");
        checkExist(qo.getUserId(), "用户id不能为空");
        if("undefined".equals(qo.getFormId())) {
            qo.setFormId(null);
        }
        if("undefined".equals(qo.getOrganizationId())) {
            qo.setOrganizationId(null);
        }
        return workflowCustomMapper.selectUnSubmitWorkflowExportData(qo);
    }

    /**
     * 将指定流程记录设置为无效
     *
     * @param workflowControlId
     */
    public void inactiveWorkflowControl(String workflowControlId) {
        WorkflowControl workflowControl = workflowControlMapper.selectByPrimaryKey(workflowControlId);
        if (workflowControl == null) {
            throw new ServiceException("流程控制记录不存在");
        }
        workflowControl.setIsActive(Boolean.FALSE);
        workflowControlMapper.updateByPrimaryKeySelective(workflowControl);
    }

    public Map<String, Long> listWorkflowControlStateSummary(WorkflowControlQO workflowControlQO) {
        workflowControlQO.setUserId(ContextUtils.getCurrentUser().getId());
        List<WorkflowControlVO> lstWorkflowControlVO = workflowCustomMapper.selectWorkflowControl(workflowControlQO);
        lstWorkflowControlVO.forEach(x -> {
            x.setStateName(Optional.ofNullable(WorkflowControlState.getWorkflowControlState(x.getState())).map(WorkflowControlState::getName).orElse(null));
            if (x.getState() == 0 && x.getLastUpdateVersion() > 1) {
                x.setStateName("已保存");
            } else if (x.getState() == 0 && x.getLastUpdateVersion() <= 1) {
                x.setStateName("未開始");
            }
        });
        return lstWorkflowControlVO.stream().collect(Collectors.groupingBy(WorkflowControlVO::getStateName, Collectors.counting()));
    }
}
