package com.csci.susdev.service;

import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.csci.cohl.model.EmpCommutingDetail;
import com.csci.cohl.model.EmpCommutingHead;
import com.csci.common.exception.ServiceException;
import com.csci.susdev.constant.SusDevConsts;
import com.csci.susdev.constant.WorkflowControlState;
import com.csci.susdev.mapper.*;
import com.csci.susdev.model.*;
import com.csci.susdev.qo.EmissionReductionQO;
import com.csci.susdev.qo.EmpCommutingQO;
import com.csci.susdev.util.ConvertBeanUtils;
import com.csci.susdev.util.SpringContextUtil;
import com.csci.susdev.util.redis.RedisLockUtil;
import com.csci.susdev.vo.*;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.*;

import static com.csci.susdev.service.ServiceHelper.checkExist;

/**
 * @description:
 * @author: barry
 * @create: 2024-11-27 14:34
 */
@Service
public class EmpCommutingService {

    /**
     * 日志记录对象
     */
    private static final Logger logger = LoggerFactory.getLogger(EmpCommutingService.class);
    /**
     * 初始化记录的过期时间
     */
    @Value("${lock.init.head.expire.milis:1000}")
    private long lockInitHeadExpireMilis;
    /**
     * 查询head记录时，获取锁失败循环等待的次数
     */
    @Value("${query.head.wait.loop.times:5}")
    private int lockQueryHeadWaitLoopTimes;
    /**
     * 查询head记录时，获取锁失败循环时每次休眠的时间
     */
    @Value("${query.head.wait.sleep.milis:50}")
    private int lockQueryHeadWaitSleepMilis;

    @Resource
    private EmpCommutingHeadMapper empCommutingHeadMapper;

    @Resource
    private EmpCommutingDetailMapper empCommutingDetailMapper;

    /**
     * 根据查询条件查询员工通勤信息
     * 只会查询 active = true 的记录
     *
     * @param organizationId 组织 id
     * @param year           年份
     * @param month          月份
     * @return
     */
    EmpCommutingHead getEmpCommutingHeadBy(String organizationId, Integer year, Integer month) {
        LambdaQueryWrapper<EmpCommutingHead> queryWrapper = Wrappers.<EmpCommutingHead>lambdaQuery().eq(EmpCommutingHead::getOrganizationId, organizationId)
                .eq(EmpCommutingHead::getYear, year)
                .eq(EmpCommutingHead::getMonth, month)
                .eq(EmpCommutingHead::getIsActive, Boolean.TRUE);
        return empCommutingHeadMapper.selectOne(queryWrapper);
    }

    /**
     * @param qo
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    public EmpCommutingHead createEmpCommutingHead(EmpCommutingQO qo) {
        EmpCommutingHead empCommutingHead = getEmpCommutingHeadBy(qo.getOrganizationId(), qo.getYear(),
                qo.getMonth());
        if (empCommutingHead == null) {
            empCommutingHead = new EmpCommutingHead();
            empCommutingHead.setOrganizationId(qo.getOrganizationId());
            empCommutingHead.setYear(qo.getYear());
            empCommutingHead.setMonth(qo.getMonth());
            empCommutingHead.setIsActive(Boolean.TRUE);
            empCommutingHead.setLastUpdateVersion(0);
            empCommutingHeadMapper.insert(empCommutingHead);
        }
        return empCommutingHead;
    }

    /**
     * 根据查询条件查询员工通勤信息，如果不存在则创建
     *
     * @param empCommutingQO 查询条件
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    public EmpCommutingHeadVO getOrInit(EmpCommutingQO empCommutingQO) {
        checkExist(empCommutingQO.getOrganizationId(), "组织编号不能为空");
        checkExist(empCommutingQO.getYear(), "年份不能为空");
        if(ObjectUtils.isEmpty(empCommutingQO.getMonth())) {
            empCommutingQO.setMonth(12);
        }

        if (empCommutingQO.getYear() < 1900 || empCommutingQO.getYear() > 2100) {
            throw new ServiceException("年份必须在 1900-2100 之间");
        }

        //查询或初始化记录，如果获取失败，直接抛出异常退出
        EmpCommutingHead empCommutingHead = getOrInitHeadRecord(empCommutingQO);

        // 查询流程审批状态
        WorkflowControlService workflowControlService = SpringContextUtil.getBean(WorkflowControlService.class);
        WorkflowControl workflowControl = workflowControlService.findWorkflowControlByBusinessId(empCommutingHead.getId());
        EmpCommutingHeadVO empCommutingHeadVO = ConvertBeanUtils.convert(empCommutingHead, EmpCommutingHeadVO.class);
        if (Objects.nonNull(workflowControl)) {
            empCommutingHeadVO.setWorkflowControlState(workflowControl.getState());
            empCommutingHeadVO.setWorkflowControlStateName(Optional.ofNullable(WorkflowControlState.getWorkflowControlState(workflowControl.getState())).map(WorkflowControlState::getName).orElse(null));
        }
        return empCommutingHeadVO;
    }

    /**
     * 查询或初始化记录，如果获取失败，直接抛出异常退出
     * @param
     * @return
     */
    private EmpCommutingHead getOrInitHeadRecord(EmpCommutingQO empCommutingQO) {
        EmpCommutingHead empCommutingHead = getEmpCommutingHeadBy(empCommutingQO.getOrganizationId(),
                empCommutingQO.getYear(), empCommutingQO.getMonth());
        if (Objects.isNull(empCommutingHead)) {
            //分布式锁的key：分别表示前缀:业务:组织ID:年:月
            String key = StrUtil.format(SusDevConsts.DISTRIBUTE_LOCK_KEY_FORMAT, SusDevConsts.PROJECT_PREFIX,
                    "EmpCommutingHead:getOrInit", empCommutingQO.getOrganizationId(),
                    empCommutingQO.getYear(), empCommutingQO.getMonth());
            //超时时间1s
            boolean redisLock = RedisLockUtil.lock(key, lockInitHeadExpireMilis);
            if (!redisLock) {
                //获取锁失败，停顿50ms一次，停顿5次
                int count = lockQueryHeadWaitLoopTimes;
                while(count-- > 0){
                    empCommutingHead = getEmpCommutingHeadBy(empCommutingQO.getOrganizationId(),
                            empCommutingQO.getYear(), empCommutingQO.getMonth());
                    if(empCommutingHead != null){
                        break;
                    }
                    try {
                        Thread.sleep(lockQueryHeadWaitSleepMilis);
                    } catch (InterruptedException e) {
                        throw new RuntimeException(e);
                    }
                }
            }else{
                try{
                    empCommutingHead = createEmpCommutingHead(empCommutingQO);
                }
                finally {
                    RedisLockUtil.unlock(key);
                }
            }
        }
        //如果最终没拿到数据，直接返回
        if(empCommutingHead == null){
            throw new ServiceException("生成记录失败, 请稍后再尝试！");
        }
        return empCommutingHead;
    }

    public static void deleteDetailByHeadId(String headId) {
        checkExist(headId, "headId is null");
        EmpCommutingDetailMapper EmpCommutingDetailMapper = SpringContextUtil.getBean(EmpCommutingDetailMapper.class);
        EmpCommutingDetailMapper.delete(Wrappers.<EmpCommutingDetail>lambdaQuery().eq(EmpCommutingDetail::getHeadId, headId));
    }

    /**
     * 保存员工通勤明细信息
     *
     * @param empCommutingHeadVO 员工通勤头信息
     */
    @Transactional(rollbackFor = Exception.class)
    public void updateEmpCommutingWithDetail(EmpCommutingHeadVO empCommutingHeadVO) {
        WorkflowControlService workflowControlService = SpringContextUtil.getBean(WorkflowControlService.class);
        if (workflowControlService.isNotAllowEdit(empCommutingHeadVO.getId())) {
            throw new ServiceException("已经发起审批流程的单据不允许进行修改");
        }
        // 更新主表信息
        updateHead(empCommutingHeadVO);
        EmpCommutingDetail empCommutingDetail = empCommutingHeadVO.getEmpCommutingDetail();
        if (empCommutingDetail != null) {
            String id = empCommutingDetail.getId();
            if (ObjectUtils.isEmpty(id)) {
                empCommutingDetail.setId(UUID.randomUUID().toString());
                empCommutingDetail.setHeadId(empCommutingHeadVO.getId());
                empCommutingDetailMapper.insert(empCommutingDetail);
            } else {
                empCommutingDetailMapper.updateById(empCommutingDetail);
            }
        }

    }

    public void deleteEmpCommutingWithDetail(EmpCommutingHeadVO empCommutingHeadVO) {
        WorkflowControlService workflowControlService = SpringContextUtil.getBean(WorkflowControlService.class);
        if (workflowControlService.isNotAllowEdit(empCommutingHeadVO.getId())) {
            throw new ServiceException("已经发起审批流程的单据不允许进行修改");
        }
        // 更新主表信息
        updateHead(empCommutingHeadVO);
        EmpCommutingDetail empCommutingDetail = empCommutingHeadVO.getEmpCommutingDetail();
        if (empCommutingDetail != null) {
            String id = empCommutingDetail.getId();
            if (!ObjectUtils.isEmpty(id)) {
                empCommutingDetailMapper.deleteById(id);
            }
        }
    }
    /**
     * 提交员工通勤
     *
     * @param idVersionVO
     */
    public void submit(IdVersionVO idVersionVO) {
        // 验证逻辑---------
        checkExist(idVersionVO.getId(), "员工通勤主表id不能为空");
        checkExist(idVersionVO.getLastUpdateVersion(), "员工通勤主表版本号不能为空");

        EmpCommutingHead existedRecord = empCommutingHeadMapper.selectById(idVersionVO.getId());
        Optional.ofNullable(existedRecord).orElseThrow(() -> new ServiceException("指定的员工通勤记录不存在"));
        // 验证逻辑---------

        existedRecord.setLastUpdateVersion(idVersionVO.getLastUpdateVersion());
        int count = empCommutingHeadMapper.update(existedRecord, Wrappers.<EmpCommutingHead>lambdaUpdate()
                .eq(EmpCommutingHead::getId, idVersionVO.getId())
                .eq(EmpCommutingHead::getLastUpdateVersion, idVersionVO.getLastUpdateVersion()));
        if (count == 0) {
            throw new ServiceException("员工通勤已经被修改，请刷新后重试");
        }
        // todo 增加流程审批记录
    }

    public void addDetails(String headId, List<EmpCommutingDetail> empCommutingDetails) {
        if (CollectionUtils.isEmpty(empCommutingDetails)) {
            throw new ServiceException("员工通勤明细不能为空");
        }
        for (EmpCommutingDetail EmpCommutingDetail : empCommutingDetails) {
            EmpCommutingDetail.setId(UUID.randomUUID().toString());
            EmpCommutingDetail.setHeadId(headId);
            empCommutingDetailMapper.insert(EmpCommutingDetail);
        }
    }

    private void updateHead(EmpCommutingHeadVO empCommutingHeadVO) {
        checkExist(empCommutingHeadVO.getId(), "员工通勤主表id不能为空");
        checkExist(empCommutingHeadVO.getOrganizationId(), "组织编号不能为空");
        checkExist(empCommutingHeadVO.getLastUpdateVersion(), "版本号不能为空");

        EmpCommutingHead empCommutingHead = ConvertBeanUtils.convert(empCommutingHeadVO, EmpCommutingHead.class);
        LambdaUpdateWrapper<EmpCommutingHead> updateWrapper = Wrappers.<EmpCommutingHead>lambdaUpdate()
                .eq(EmpCommutingHead::getId, empCommutingHeadVO.getId())
                .eq(EmpCommutingHead::getLastUpdateVersion, empCommutingHeadVO.getLastUpdateVersion());
        int updateCount = empCommutingHeadMapper.update(empCommutingHead, updateWrapper);
        if (updateCount == 0) {
            throw new ServiceException("员工通勤主表信息已经被修改，请刷新后重试");
        }
    }

    /**
     * proxy for mapper method
     *
     * @param id 员工通勤主表id
     * @return
     */
    public EmpCommutingHead selectByPrimaryKey(String id) {
        return empCommutingHeadMapper.selectById(id);
    }


    public List<EmpCommutingDetail> listEmpCommutingDetail(String headId) {
        List<EmpCommutingDetail> empCommutingDetails = empCommutingDetailMapper.selectList(
                Wrappers.<EmpCommutingDetail>lambdaQuery()
                        .eq(EmpCommutingDetail::getHeadId, headId)
//                        .orderByAsc(EmpCommutingDetail::getSeq)  //不按seq排序
                        .orderByDesc(EmpCommutingDetail::getCreationTime)
        );
        return empCommutingDetails;
    }

    /**
     * 批量保存员工通勤
     * <AUTHOR>
     * @date 2024/12/23 16:14
     * @param empCommutingHeadVO
     */
    @Transactional(rollbackFor = Exception.class)
    public void batchSaveEmpCommutingWithDetail(EmpCommutingHeadVO empCommutingHeadVO) {
        WorkflowControlService workflowControlService = SpringContextUtil.getBean(WorkflowControlService.class);
        if (workflowControlService.isNotAllowEdit(empCommutingHeadVO.getId())) {
            throw new ServiceException("已经发起审批流程的单据不允许进行修改");
        }
        // 更新主表信息
        updateHead(empCommutingHeadVO);
        // 批量保存员工通勤明细数据
        List<EmpCommutingDetail> empCommutingDetailList = empCommutingHeadVO.getEmpCommutingDetailList();
        if (CollectionUtils.isNotEmpty(empCommutingDetailList)) {
//            int seq = 0;
            //倒序排列，按相反的方向插入，使得查询结果和导入的excel顺序一致
            Collections.reverse(empCommutingDetailList);
            for (EmpCommutingDetail empCommutingDetail : empCommutingDetailList) {
                String id = empCommutingDetail.getId();
//                empCommutingDetail.setSeq(seq++);
                if (ObjectUtils.isEmpty(id)) {
                    empCommutingDetail.setId(UUID.randomUUID().toString());
                    empCommutingDetail.setHeadId(empCommutingHeadVO.getId());
                    empCommutingDetailMapper.insert(empCommutingDetail);
                } else {
                    empCommutingDetailMapper.updateById(empCommutingDetail);
                }
            }
        }
        if (CollectionUtils.isNotEmpty(empCommutingHeadVO.getDeleteDetailIdList())) {
            empCommutingDetailMapper.deleteBatchIds(empCommutingHeadVO.getDeleteDetailIdList());
        }
    }

}
