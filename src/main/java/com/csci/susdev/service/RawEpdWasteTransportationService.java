package com.csci.susdev.service;

import com.csci.susdev.annotation.DS;
import com.csci.susdev.annotation.LogMethod;
import com.csci.susdev.constant.DatasourceContextEnum;
import com.csci.susdev.util.ExcelUtils;
import com.csci.susdev.util.SslUtils;
import com.csci.tzh.mapper.RawEpdWasteTransportationMapper;
import com.csci.tzh.mapper.RawEpdWasteTransportationSyncLogMapper;
import com.csci.tzh.mapper.TzhProjectDetailMapper;
import com.csci.tzh.mapper.TzhProjectInfoMapper;
import com.csci.tzh.model.*;
import org.apache.commons.lang3.StringUtils;
import org.apache.poi.ss.usermodel.*;
import org.jsoup.Jsoup;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.io.IOException;
import java.io.PrintWriter;
import java.io.StringWriter;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.UUID;

@Service
@DS(DatasourceContextEnum.TANZHONGHE)
@LogMethod
public class RawEpdWasteTransportationService {

	@Autowired
	private FCdmsWasteTransportationCarbonFactorService wasteTransportationService;
	@Autowired
	private TzhProjectDetailMapper projectDetailMapper;
	@Autowired
	private RawEpdWasteTransportationMapper dataMapper;
	@Autowired
	private RawEpdWasteTransportationSyncLogMapper syncLogMapper;
	private static final Logger logger = LoggerFactory.getLogger(RawEpdWasteTransportationService.class);


	public void downloadData() throws Exception {
		SslUtils.ignoreSsl();
		List<String> lstLinks = this.getRelatedLinks();
		// List<String> lstLinks = this.getFilePaths();
		List<RawEpdWasteTransportation> lstModel = new ArrayList<>();

		for(String link : lstLinks) {
			RawEpdWasteTransportationSyncLogExample startedExample = null;
			try {
				int recordCount = 0;
				int insertCount = 0;
				String fileName = link.replace("https://www.epd.gov.hk/epd/misc/cdm/", "");
				// String fileName = link.replace("D:\\EPD\\", "");

				// 查詢已完成的記錄
				RawEpdWasteTransportationSyncLogExample endedExample = new RawEpdWasteTransportationSyncLogExample();
				RawEpdWasteTransportationSyncLogExample.Criteria endedCriteria = endedExample.or();
				endedCriteria.andFilenameEqualTo(fileName);
				endedCriteria.andEnddatetimeIsNotNull();
				List<RawEpdWasteTransportationSyncLog> lstSyncLog = syncLogMapper.selectByExample(endedExample);

				if(lstSyncLog.size() > 0) {
					continue;
				} else {
					// 查詢未完成的記錄
					startedExample = new RawEpdWasteTransportationSyncLogExample();
					RawEpdWasteTransportationSyncLogExample.Criteria startedCriteria = startedExample.or();
					startedCriteria.andFilenameEqualTo(fileName);
					startedCriteria.andStartdatetimeIsNotNull();
					startedCriteria.andEnddatetimeIsNull();

					List<RawEpdWasteTransportationSyncLog> lstStartedSyncLog = syncLogMapper.selectByExample(startedExample);
					List<String> lstSyncLogId = new ArrayList<>();
					for(RawEpdWasteTransportationSyncLog syncLog : lstStartedSyncLog) {
						lstSyncLogId.add(syncLog.getId());
					}
					// 刪除未完成的記錄
					syncLogMapper.deleteByExample(startedExample);

					// 刪除沒有完成的數據(避免不完整數據儲存)
					if(lstSyncLogId.size() > 0) {
						RawEpdWasteTransportationExample dataExample = new RawEpdWasteTransportationExample();
						RawEpdWasteTransportationExample.Criteria dataCriteria = dataExample.or();
						dataCriteria.andSynclogidIn(lstSyncLogId);
						dataMapper.deleteByExample(dataExample);
					}

					// 創建新記錄
					RawEpdWasteTransportationSyncLog syncLog = new RawEpdWasteTransportationSyncLog();
					syncLog.setId(UUID.randomUUID().toString());
					syncLog.setFilename(fileName);
					syncLog.setStartdatetime(LocalDateTime.now());
					syncLog.setRecordcount(0);
					syncLog.setInsertcount(0);
					syncLog.setUpdatecount(0);
					syncLogMapper.insertSelective(syncLog);

					// 數據抓取
					try(Workbook workbook = ExcelUtils.getWorkbookFromUrl(link)) {
						// Workbook workbook = ExcelUtils.getWorkbookFromLocal(link);
						DataFormatter dataFormatter = new DataFormatter();
						FormulaEvaluator formulaEvaluator = workbook.getCreationHelper().createFormulaEvaluator();

						int sheetCount = workbook.getNumberOfSheets();
						for(int i=1; i<sheetCount; i++) {
							Sheet sheet = workbook.getSheetAt(i);
							int rowCount = sheet.getLastRowNum() + 1;
							for(int j=1; j<rowCount; j++) {
								Row row = sheet.getRow(j);
								RawEpdWasteTransportation model = new RawEpdWasteTransportation();
								model.setSynclogid(syncLog.getId());

								int curCell = 0;

								model.setFacility(dataFormatter.formatCellValue(row.getCell(curCell++), formulaEvaluator));
								model.setTransactiondate(ExcelUtils.getStringValue(row.getCell(curCell++)));
								model.setVehicleno(dataFormatter.formatCellValue(row.getCell(curCell++), formulaEvaluator));
								model.setAccountno(dataFormatter.formatCellValue(row.getCell(curCell++), formulaEvaluator));
								model.setChitno(dataFormatter.formatCellValue(row.getCell(curCell++), formulaEvaluator));
								model.setTimein(dataFormatter.formatCellValue(row.getCell(curCell++), formulaEvaluator));
								model.setTimeout(dataFormatter.formatCellValue(row.getCell(curCell++), formulaEvaluator));
								model.setWastedepth(dataFormatter.formatCellValue(row.getCell(curCell++), formulaEvaluator));
								model.setWeightin(dataFormatter.formatCellValue(row.getCell(curCell++), formulaEvaluator));
								model.setWeightout(dataFormatter.formatCellValue(row.getCell(curCell++), formulaEvaluator));
								model.setNetweight(dataFormatter.formatCellValue(row.getCell(curCell++), formulaEvaluator));

								// 如數據不存在則儲存
								insertCount += this.save(model);
								recordCount++;

								RawEpdWasteTransportationSyncLog startedSyncLog = new RawEpdWasteTransportationSyncLog();
								startedSyncLog.setRecordcount(recordCount);
								startedSyncLog.setInsertcount(insertCount);
								syncLogMapper.updateByExampleSelective(startedSyncLog, startedExample);
							}
						}

						if(syncLogMapper.countByExample(startedExample) > 0) {
							// 更新未完成的記錄 -> 已完成
							RawEpdWasteTransportationSyncLog startedSyncLog = new RawEpdWasteTransportationSyncLog();
							startedSyncLog.setEnddatetime(LocalDateTime.now());
							syncLogMapper.updateByExampleSelective(startedSyncLog, startedExample);
						}
					}

				}
			} catch (Exception ex) {
				// 記錄錯誤日誌
				StringWriter sw = new StringWriter();
				ex.printStackTrace(new PrintWriter(sw));
				logger.error(sw.toString());

				if(startedExample != null) {
					RawEpdWasteTransportationSyncLog startedSyncLog = new RawEpdWasteTransportationSyncLog();
					startedSyncLog.setErrorlog(sw.toString());
					startedSyncLog.setErrordatetime(LocalDateTime.now());
					syncLogMapper.updateByExampleSelective(startedSyncLog, startedExample);
				}
			}
		}
	}


	@Transactional(rollbackFor = Exception.class)
	public void extractData() throws Exception {
		// 查詢項目資訊
		TzhProjectDetailExample projectDetailExample = new TzhProjectDetailExample();
		TzhProjectDetailExample.Criteria projectDetailCriteria = projectDetailExample.or();
		projectDetailCriteria.andEpdwasteaccountIsNotNull();
		projectDetailCriteria.andEpdwasteaccountNotEqualTo("");
		projectDetailCriteria.andIsdeletedEqualTo(false);
		List<TzhProjectDetail> lstProjectDetail = projectDetailMapper.selectByExample(projectDetailExample);

		for (TzhProjectDetail projectDetail : lstProjectDetail) {
			// 查詢未處理數據
			RawEpdWasteTransportationExample dataExample = new RawEpdWasteTransportationExample();
			RawEpdWasteTransportationExample.Criteria dataCriteria = dataExample.or();
			dataCriteria.andAccountnoEqualTo(projectDetail.getEpdwasteaccount());
			dataCriteria.andProcessdatetimeIsNull();
			List<RawEpdWasteTransportation> lstData = dataMapper.selectByExample(dataExample);

			for (RawEpdWasteTransportation data : lstData) {
				try {
					FCdmsWasteTransportationCarbonFactor factor = new FCdmsWasteTransportationCarbonFactor();
					factor.setSitename(projectDetail.getSitename());
					LocalDate recordDate = LocalDate.parse(data.getTransactiondate().replace(" 00:00:00", ""));
					factor.setProtocolid(projectDetail.getProtocolid());
					factor.setRecordyearmonth(recordDate.getYear()*100+recordDate.getMonthValue());
					factor.setRecorddate(recordDate);
					factor.setVehicleno(data.getVehicleno());
					factor.setChitno(data.getChitno());
					factor.setHandlemethod("直接送往堆填區");
					factor.setTransportationtype("重型柴油貨車運輸（載重18t）");
					factor.setHandlelocation(data.getFacility());
					factor.setQty(new BigDecimal(data.getNetweight()));
					factor.setQtyUnit("ton");
					factor.setTransportfactor(new BigDecimal(0));
					factor.setTransportfactorunit("kg co2e/ton");
					factor.setTransportscope("廢棄物運輸");
					factor.setDatasource("網絡爬蟲");
					factor.setRawdatasourceid(data.getId());
					factor.setCreatedby("system");
					factor.setCreatedtime(LocalDateTime.now());
					factor.setIsdeleted(false);
					wasteTransportationService.save(factor);

					RawEpdWasteTransportationExample errorDataExample = new RawEpdWasteTransportationExample();
					RawEpdWasteTransportationExample.Criteria errorDataCriteria = errorDataExample.or();
					errorDataCriteria.andIdEqualTo(data.getId());
					data.setProcessdatetime(LocalDateTime.now());
					dataMapper.updateByExampleSelective(data, errorDataExample);

				} catch (Exception ex) {
					// 記錄錯誤日誌
					StringWriter sw = new StringWriter();
					ex.printStackTrace(new PrintWriter(sw));
					logger.error(sw.toString());

					RawEpdWasteTransportationExample errorDataExample = new RawEpdWasteTransportationExample();
					RawEpdWasteTransportationExample.Criteria errorDataCriteria = errorDataExample.or();
					errorDataCriteria.andIdEqualTo(data.getId());
					data.setProcesserrorlog(sw.toString());
					dataMapper.updateByExampleSelective(data, errorDataExample);
				}
			}
		}
	}


	@Transactional(rollbackFor = Exception.class)
	public List<String> getRelatedLinks() throws IOException {
		List<String> relatedLinks = new ArrayList<>();

		String html = Jsoup.connect("https://www.epd.gov.hk/epd/misc/cdm/b5_scheme.htm").get().html();
		String linkOpen = "chit/CWDCS2";
		String linkEnd = ".xls";

		String[] linkMiddles = StringUtils.substringsBetween(html, linkOpen, linkEnd);
		for(String linkMiddle : linkMiddles) {
			relatedLinks.add("https://www.epd.gov.hk/epd/misc/cdm/" + linkOpen + linkMiddle + linkEnd);
		}

		return relatedLinks;
	}

	@Transactional(rollbackFor = Exception.class)
	public int save(RawEpdWasteTransportation model) {
		int result = 0;
		RawEpdWasteTransportationExample example = new RawEpdWasteTransportationExample();
		RawEpdWasteTransportationExample.Criteria criteria = example.or();
		criteria.andFacilityEqualTo(model.getFacility());
		criteria.andTransactiondateEqualTo(model.getTransactiondate());
		criteria.andVehiclenoEqualTo(model.getVehicleno());
		criteria.andChitnoEqualTo(model.getChitno());
		criteria.andAccountnoEqualTo(model.getAccountno());

		List<RawEpdWasteTransportation> lst = dataMapper.selectByExample(example);

		LocalDateTime now = LocalDateTime.now();
		if(lst.size() == 0) {
			model.setId(UUID.randomUUID().toString());
			model.setInputdatetime(now);
			model.setSyncdatetime(now);
			result += dataMapper.insertSelective(model);
		}
		return result;
	}
}
