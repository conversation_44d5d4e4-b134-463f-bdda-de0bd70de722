package com.csci.susdev.service;

import com.csci.common.exception.ServiceException;
import com.csci.susdev.annotation.LogMethod;
import com.csci.susdev.mapper.FcCarbonFactorFlcMapper;
import com.csci.susdev.model.*;
import com.csci.susdev.modelcovt.FcCarbonFactorFlcConverter;
import com.csci.susdev.modelcovt.FcCarbonFactorGbt51366Converter;
import com.csci.susdev.qo.FcCarbonFactorFlcQO;
import com.csci.susdev.util.NamingConventionUtils;
import com.csci.susdev.util.SimpTradUtil;
import com.csci.susdev.vo.FcCarbonFactorFlcVO;
import com.github.pagehelper.PageHelper;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.List;
import java.util.stream.Collectors;

import static com.csci.susdev.service.ServiceHelper.checkExist;

@Service
@LogMethod
public class FcCarbonFactorFlcService {

    @Resource
    private FcCarbonFactorFlcMapper fcCarbonFactorFlcMapper;


    @Transactional(rollbackFor = Exception.class)
    public int insertSelective(FcCarbonFactorFlc record) {
        return fcCarbonFactorFlcMapper.insertSelective(record);
    }

    public FcCarbonFactorFlc selectByPrimaryKey(String id) {
        return fcCarbonFactorFlcMapper.selectByPrimaryKey(id);
    }

    public FcCarbonFactorFlcVO getFcCarbonFactorFlc(String id) {
        checkExist(id, "id不能为空");
        FcCarbonFactorFlc fcCarbonFactorFlc = selectByPrimaryKey(id);
        checkExist(fcCarbonFactorFlc, "未找到对应的记录");
        if(fcCarbonFactorFlc.getIsDeleted()) {
            throw new ServiceException("紀錄已被刪除");
        }
        return FcCarbonFactorFlcConverter.convertToVO(fcCarbonFactorFlc);
    }


    @Transactional(rollbackFor = Exception.class)
    public String saveFcCarbonFactorFlc(FcCarbonFactorFlcVO fcCarbonFactorFlcVO) {
        if (StringUtils.isBlank(fcCarbonFactorFlcVO.getId())) {
            // 新增
            return doAdd(fcCarbonFactorFlcVO);
        } else {
            checkExist(fcCarbonFactorFlcVO.getLastUpdateVersion(), "更新时版本号不能为空");
            // 修改
            return doUpdate(fcCarbonFactorFlcVO);
        }

    }

    public ResultPage<FcCarbonFactorFlcVO> listFcCarbonFactorFlc(FcCarbonFactorFlcQO fcCarbonFactorFlcQO) {
        FcCarbonFactorFlcExample example = new FcCarbonFactorFlcExample();

        SimpTradUtil simpTradUtil = new SimpTradUtil();
        String chineseNameTc = simpTradUtil.convert2Trad(fcCarbonFactorFlcQO.getChineseName());
        String chineseNameSc = simpTradUtil.convert2Simp(fcCarbonFactorFlcQO.getChineseName());

        if(StringUtils.isNotBlank(fcCarbonFactorFlcQO.getChineseName())) {
            example.or().andChineseNameLike("%" + chineseNameTc + "%").andIsDeletedEqualTo(false);
            example.or().andChineseNameLike("%" + chineseNameSc + "%").andIsDeletedEqualTo(false);
        } else {
            example.or().andIsDeletedEqualTo(false);
        }

        if (StringUtils.isNotBlank(fcCarbonFactorFlcQO.getOrderBy())) {
            example.setOrderByClause(NamingConventionUtils.snakeCaseToCamelCase(fcCarbonFactorFlcQO.getOrderBy()));
        }

        PageHelper.startPage(fcCarbonFactorFlcQO.getCurPage(), fcCarbonFactorFlcQO.getPageSize());
        List<FcCarbonFactorFlc> fcCarbonFactorFlcs = fcCarbonFactorFlcMapper.selectByExample(example);
        return new ResultPage<>(fcCarbonFactorFlcs, fcCarbonFactorFlcs.stream().map(FcCarbonFactorFlcConverter::convertToVO).collect(Collectors.toList()));
    }

    @Transactional(rollbackFor = Exception.class)
    public void deleteFcCarbonFactorFlc(String id) {
        FcCarbonFactorFlc record = selectByPrimaryKey(id);
        record.setIsDeleted(true);
        fcCarbonFactorFlcMapper.updateByPrimaryKeySelective(record);
    }

    private String doAdd(FcCarbonFactorFlcVO fcCarbonFactorFlcVO) {
        FcCarbonFactorFlc fcCarbonFactorFlc = FcCarbonFactorFlcConverter.convertToModel(fcCarbonFactorFlcVO);
        fcCarbonFactorFlcMapper.insertSelective(fcCarbonFactorFlc);
        return fcCarbonFactorFlc.getId();
    }

    private String doUpdate(FcCarbonFactorFlcVO fcCarbonFactorFlcVO) {
        FcCarbonFactorFlc originalRecord = selectByPrimaryKey(fcCarbonFactorFlcVO.getId());
        FcCarbonFactorFlc fcCarbonFactorFlc = FcCarbonFactorFlcConverter.convertToModelWithBase(fcCarbonFactorFlcVO, originalRecord);

        FcCarbonFactorFlcExample example = new FcCarbonFactorFlcExample();
        example.or().andIdEqualTo(fcCarbonFactorFlcVO.getId()).andLastUpdateVersionEqualTo(fcCarbonFactorFlcVO.getLastUpdateVersion());
        int updateCount = fcCarbonFactorFlcMapper.updateByExample(fcCarbonFactorFlc, example);
        if (updateCount == 0) {
            throw new ServiceException("更新失败，可能是数据已被修改，请刷新后重试");
        }
        return fcCarbonFactorFlc.getId();
    }

    public List<FcCarbonFactorFlc> selectByExample(FcCarbonFactorFlcExample example) {
        return fcCarbonFactorFlcMapper.selectByExample(example);
    }
}
