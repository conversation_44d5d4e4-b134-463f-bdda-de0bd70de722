package com.csci.susdev.service;

import java.time.LocalDateTime;
import java.util.*;

import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.csci.common.exception.ServiceException;
import com.csci.susdev.annotation.DS;
import com.csci.susdev.annotation.LogMethod;
import com.csci.susdev.constant.DatasourceContextEnum;
import com.csci.susdev.mapper.*;
import com.csci.susdev.model.*;
import com.csci.susdev.model.PermissionExample.Criteria;
import com.csci.susdev.qo.PermissionPageableQO;
import com.csci.susdev.util.context.ContextUtils;
import com.csci.susdev.util.context.model.UserInfo;
import com.github.pagehelper.PageHelper;

@Service
@DS(DatasourceContextEnum.SUSDEV)
@LogMethod
public class PermissionService {

    @Autowired
    private PermissionMenuService permissionMenuService;

    @Autowired
    private PermissionMapper mapper;

    @Autowired
    private RoleMapper roleMapper;
    
    @Autowired
    private RolePermissionMapper rolePermissionMapper;

    public List<Permission> selectByExample(PermissionExample example) {
        return mapper.selectByExample(example);
    }
    
    /**
     * 權限 数据列表
     *
     * @param permissionQO
     * @return
     */
    public ResultPage<Permission> listPermission(PermissionPageableQO permissionQO) {
    	ResultPage<Permission> resultPage;
    	
    	PermissionExample example = new PermissionExample();
        Criteria criteria = example.or();
    	
    	if(StringUtils.isNotBlank(permissionQO.getRoleCode())) {

        	List<String> lstPermissionId = new ArrayList<>();
        	Role role = ServiceHelper.getRoleByCode(permissionQO.getRoleCode());
        	
        	if(role == null)
        		return new ResultPage<>(new ArrayList<Permission>());
        	
        	List<RolePermission> lstRolePermission = ServiceHelper.listRolePermission(role.getId(), rolePermissionMapper);
        	
        	for(RolePermission rolePermission: lstRolePermission) {
        		lstPermissionId.add(rolePermission.getPermissionId());
        	}
        	
            if(lstPermissionId.size() > 0) {
                criteria.andIdIn(lstPermissionId);
            }
    	}
    	
        example.setOrderByClause("code");

        PageHelper.startPage(permissionQO.getCurPage(), permissionQO.getPageSize());
        List<Permission> lstPermission = mapper.selectByExample(example);
	    resultPage = new ResultPage<>(lstPermission, true);
        return resultPage;
    }

    public List<Permission> getByMenuId(String menuId) {
        List<PermissionMenu> lstPermissionMenu = permissionMenuService.getByMenuId(menuId);
        List<Permission> lstPermission = new ArrayList<>();

        for(PermissionMenu permissionMenu: lstPermissionMenu) {
            lstPermission.add(mapper.selectByPrimaryKey(permissionMenu.getPermissionId()));
        }
        return lstPermission;
    }


    /**
     * 保存 權限 信息, 如果是已经存在的记录就保存，否则新增
     * 传入的参数id为空表示新增，否则表示更新
     *
     * @param permissionLst
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    public List<Permission> savePermissionList(List<Permission> permissionLst) {
    	List<Permission> lst = new ArrayList<>();
    	for(Permission permission : permissionLst) {
            lst.add(this.savePermission(permission));
    	}
        return lst;
    }

    /**
     * 保存 權限 信息, 如果是已经存在的记录就保存，否则新增
     * 传入的参数id为空表示新增，否则表示更新
     *
     * @param permission
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    public Permission savePermission(Permission permission) {
    	UserInfo currentUser = ContextUtils.getCurrentUser();
        LocalDateTime now = LocalDateTime.now();
        if (StringUtils.isBlank(permission.getId())) {
            permission.setId(UUID.randomUUID().toString());
            permission.setCreationTime(now);
            permission.setCreateUsername(currentUser.getUsername());
            permission.setLastUpdateTime(now);
            permission.setLastUpdateUsername(currentUser.getUsername());
            mapper.insertSelective(permission);
        } else {
            permission.setLastUpdateTime(now);
            permission.setLastUpdateUsername(currentUser.getUsername());
            mapper.updateByPrimaryKeySelective(permission);
        }
        return permission;
    }

    @Transactional(rollbackFor = Exception.class)
    public int deletePermission(String id) {
        return mapper.deleteByPrimaryKey(id);
    }

    public List<Permission> selectByPermissionIds(Set<String> permissionIds) {
        PermissionExample example = new PermissionExample();
        Criteria criteria = example.or();
        criteria.andIdIn(new ArrayList<>(permissionIds));
        return mapper.selectByExample(example);
    }
}
