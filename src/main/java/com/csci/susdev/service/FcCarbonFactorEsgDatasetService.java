package com.csci.susdev.service;

import com.csci.common.exception.ServiceException;
import com.csci.susdev.annotation.LogMethod;
import com.csci.susdev.mapper.FcCarbonFactorEsgDatasetMapper;
import com.csci.susdev.model.*;
import com.csci.susdev.modelcovt.FcCarbonFactorEsgDatasetConverter;
import com.csci.susdev.modelcovt.FcCarbonFactorFlcConverter;
import com.csci.susdev.qo.FcCarbonFactorEsgDatasetQO;
import com.csci.susdev.util.NamingConventionUtils;
import com.csci.susdev.util.SimpTradUtil;
import com.csci.susdev.vo.FcCarbonFactorEsgDatasetVO;
import com.github.pagehelper.PageHelper;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.List;
import java.util.stream.Collectors;

import static com.csci.susdev.service.ServiceHelper.checkExist;

@Service
@LogMethod
public class FcCarbonFactorEsgDatasetService {

    @Resource
    private FcCarbonFactorEsgDatasetMapper fcCarbonFactorEsgDatasetMapper;


    @Transactional(rollbackFor = Exception.class)
    public int insertSelective(FcCarbonFactorEsgDataset record) {
        return fcCarbonFactorEsgDatasetMapper.insertSelective(record);
    }

    public FcCarbonFactorEsgDataset selectByPrimaryKey(String id) {
        return fcCarbonFactorEsgDatasetMapper.selectByPrimaryKey(id);
    }

    public FcCarbonFactorEsgDatasetVO getFcCarbonFactorEsgDataset(String id) {
        checkExist(id, "id不能为空");
        FcCarbonFactorEsgDataset fcCarbonFactorEsgDataset = selectByPrimaryKey(id);
        checkExist(fcCarbonFactorEsgDataset, "未找到对应的记录");
        return FcCarbonFactorEsgDatasetConverter.convertToVO(fcCarbonFactorEsgDataset);
    }


    @Transactional(rollbackFor = Exception.class)
    public String saveFcCarbonFactorEsgDataset(FcCarbonFactorEsgDatasetVO fcCarbonFactorEsgDatasetVO) {
        if (StringUtils.isBlank(fcCarbonFactorEsgDatasetVO.getId())) {
            // 新增
            return doAdd(fcCarbonFactorEsgDatasetVO);
        } else {
            checkExist(fcCarbonFactorEsgDatasetVO.getLastUpdateVersion(), "更新时版本号不能为空");
            // 修改
            return doUpdate(fcCarbonFactorEsgDatasetVO);
        }

    }

    public ResultPage<FcCarbonFactorEsgDatasetVO> listFcCarbonFactorEsgDataset(FcCarbonFactorEsgDatasetQO fcCarbonFactorEsgDatasetQO) {
        FcCarbonFactorEsgDatasetExample example = new FcCarbonFactorEsgDatasetExample();

        SimpTradUtil simpTradUtil = new SimpTradUtil();
        String chineseNameTc = simpTradUtil.convert2Trad(fcCarbonFactorEsgDatasetQO.getChineseName());
        String chineseNameSc = simpTradUtil.convert2Simp(fcCarbonFactorEsgDatasetQO.getChineseName());

        if(StringUtils.isNotBlank(fcCarbonFactorEsgDatasetQO.getChineseName())) {
            example.or().andChineseNameLike("%" + chineseNameTc + "%").andIsDeletedEqualTo(false);
            example.or().andChineseNameLike("%" + chineseNameSc + "%").andIsDeletedEqualTo(false);
        } else {
            example.or().andIsDeletedEqualTo(false);
        }

        if (StringUtils.isNotBlank(fcCarbonFactorEsgDatasetQO.getOrderBy())) {
            example.setOrderByClause(NamingConventionUtils.snakeCaseToCamelCase(fcCarbonFactorEsgDatasetQO.getOrderBy()));
        }

        PageHelper.startPage(fcCarbonFactorEsgDatasetQO.getCurPage(), fcCarbonFactorEsgDatasetQO.getPageSize());
        List<FcCarbonFactorEsgDataset> fcCarbonFactorEsgDatasets = fcCarbonFactorEsgDatasetMapper.selectByExample(example);
        return new ResultPage<>(fcCarbonFactorEsgDatasets, fcCarbonFactorEsgDatasets.stream().map(FcCarbonFactorEsgDatasetConverter::convertToVO).collect(Collectors.toList()));
    }

    @Transactional(rollbackFor = Exception.class)
    public void deleteFcCarbonFactorEsgDataset(String id) {
        FcCarbonFactorEsgDataset record = selectByPrimaryKey(id);
        record.setIsDeleted(true);
        fcCarbonFactorEsgDatasetMapper.updateByPrimaryKeySelective(record);
    }

    private String doAdd(FcCarbonFactorEsgDatasetVO fcCarbonFactorEsgDatasetVO) {
        FcCarbonFactorEsgDataset fcCarbonFactorEsgDataset = FcCarbonFactorEsgDatasetConverter.convertToModel(fcCarbonFactorEsgDatasetVO);
        fcCarbonFactorEsgDatasetMapper.insertSelective(fcCarbonFactorEsgDataset);
        return fcCarbonFactorEsgDataset.getId();
    }

    private String doUpdate(FcCarbonFactorEsgDatasetVO fcCarbonFactorEsgDatasetVO) {
        FcCarbonFactorEsgDataset originalRecord = selectByPrimaryKey(fcCarbonFactorEsgDatasetVO.getId());
        FcCarbonFactorEsgDataset fcCarbonFactorEsgDataset = FcCarbonFactorEsgDatasetConverter.convertToModelWithBase(fcCarbonFactorEsgDatasetVO, originalRecord);

        FcCarbonFactorEsgDatasetExample example = new FcCarbonFactorEsgDatasetExample();
        example.or().andIdEqualTo(fcCarbonFactorEsgDatasetVO.getId()).andLastUpdateVersionEqualTo(fcCarbonFactorEsgDatasetVO.getLastUpdateVersion());
        int updateCount = fcCarbonFactorEsgDatasetMapper.updateByExample(fcCarbonFactorEsgDataset, example);
        if (updateCount == 0) {
            throw new ServiceException("更新失败，可能是数据已被修改，请刷新后重试");
        }
        return fcCarbonFactorEsgDataset.getId();
    }

    public List<FcCarbonFactorEsgDataset> selectByExample(FcCarbonFactorEsgDatasetExample example) {
        return fcCarbonFactorEsgDatasetMapper.selectByExample(example);
    }
}
