package com.csci.susdev.service;

import com.csci.susdev.annotation.DS;
import com.csci.susdev.annotation.LogMethod;
import com.csci.susdev.constant.DatasourceContextEnum;
import com.csci.susdev.model.ResultPage;
import com.csci.susdev.util.context.ContextUtils;
import com.csci.susdev.util.context.model.UserInfo;
import com.csci.tzh.mapper.FCdmsGasCarbonFactorCustomMapper;
import com.csci.tzh.mapper.FCdmsGasCarbonFactorMapper;
import com.csci.tzh.model.FCdmsGasCarbonFactor;
import com.csci.tzh.model.FCdmsGasCarbonFactorExample;
import com.csci.tzh.model.FCdmsGasCarbonFactorExample.Criteria;
import com.csci.tzh.qo.FCdmsGasCarbonFactorPageableQO;
import com.csci.tzh.vo.FCdmsGasCarbonFactorVO;
import com.github.pagehelper.PageHelper;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.List;
import java.util.UUID;

@Service
@DS(DatasourceContextEnum.TANZHONGHE)
@LogMethod
public class FCdmsGasCarbonFactorService {

	@Autowired
	private FCdmsGasCarbonFactorMapper mapper;

	@Autowired
	private FCdmsGasCarbonFactorCustomMapper customMapper;

	public List<FCdmsGasCarbonFactor> selectByExample(FCdmsGasCarbonFactorExample example) {
		return mapper.selectByExample(example);
	}

	public ResultPage<FCdmsGasCarbonFactorVO> list(FCdmsGasCarbonFactorPageableQO qo) {
		PageHelper.startPage(qo.getCurPage(), qo.getPageSize(), "GCF.RecordYearMonth, CEL.Name");
		List<FCdmsGasCarbonFactorVO> lst = customMapper.list(
				qo.getRegion(),
				qo.getSiteName(),
				qo.getProtocol(),
				qo.getCarbonEmissionLocation(),
				qo.getRecordYearMonth()
		);
		ResultPage<FCdmsGasCarbonFactorVO> resultPage = new ResultPage<>(lst, true);
		return resultPage;
	}

	@Transactional(rollbackFor = Exception.class)
	public FCdmsGasCarbonFactor save(FCdmsGasCarbonFactor newModel) {
		UserInfo currentUser = ContextUtils.getCurrentUser();

		if(newModel.getIsdeleted() == false) {
			if(StringUtils.isNotBlank(newModel.getId())) {
				// 備份已刪除數據
				FCdmsGasCarbonFactorExample originalExample = new FCdmsGasCarbonFactorExample();
				Criteria originalCriteria = originalExample.or();
				originalCriteria.andIdEqualTo(newModel.getId());
				List<FCdmsGasCarbonFactor> lstOriginalModel = mapper.selectByExample(originalExample);
				if(lstOriginalModel.size() > 0) {
					FCdmsGasCarbonFactor originalModel = lstOriginalModel.get(0);
					originalModel.setId(UUID.randomUUID().toString());
					originalModel.setIsdeleted(true);
					originalModel.setDeletedby(currentUser.getUsername());
					originalModel.setDeletedtime(LocalDateTime.now());
					mapper.insertSelective(originalModel);
				}

				// 新增數據(保留舊ID)
				FCdmsGasCarbonFactorExample newExample = new FCdmsGasCarbonFactorExample();
				Criteria newCriteria = newExample.or();
				newCriteria.andIdEqualTo(newModel.getId());
				newCriteria.andIsdeletedEqualTo(false);
				newModel.setCreatedby(currentUser.getUsername());
				newModel.setCreatedtime(LocalDateTime.now());
				newModel.setDeletedby(null);
				newModel.setDeletedtime(null);
				mapper.deleteByExample(newExample);
				mapper.insertSelective(newModel);
			} else {
				// 新增數據(新建ID)
				newModel.setId(UUID.randomUUID().toString());
				newModel.setCreatedby(currentUser.getUsername());
				newModel.setCreatedtime(LocalDateTime.now());
				mapper.insertSelective(newModel);
			}
		} else {
			// 刪除數據
			FCdmsGasCarbonFactorExample example = new FCdmsGasCarbonFactorExample();
			Criteria criteria = example.or();
			criteria.andIdEqualTo(newModel.getId());
			newModel.setIsdeleted(true);
			newModel.setDeletedby(currentUser.getUsername());
			newModel.setDeletedtime(LocalDateTime.now());
			mapper.updateByExampleSelective(newModel, example);
		}

		return newModel;
	}
}
