package com.csci.susdev.service;

import com.csci.susdev.annotation.DS;
import com.csci.susdev.annotation.LogMethod;
import com.csci.susdev.constant.DatasourceContextEnum;
import com.csci.susdev.util.context.ContextUtils;
import com.csci.susdev.util.context.model.UserInfo;
import com.csci.tzh.mapper.TzhPlanningCustomMapper;
import com.csci.tzh.mapper.TzhPlanningMapper;
import com.csci.tzh.model.TzhPlanning;
import com.csci.tzh.model.TzhPlanningExample;
import com.csci.tzh.qo.SiteNameProtocolQO;
import com.csci.tzh.vo.TzhPlanningVO;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.List;
import java.util.UUID;

@Service
@DS(DatasourceContextEnum.TANZHONGHE)
@LogMethod
public class TzhPlanningService {

	@Autowired
	private TzhPlanningMapper mapper;

	@Autowired
	private TzhPlanningCustomMapper customMapper;

	public List<TzhPlanning> selectByExample(TzhPlanningExample example) {
		return mapper.selectByExample(example);
	}

	public List<TzhPlanningVO> list(SiteNameProtocolQO qo) {
		List<TzhPlanningVO> lst = customMapper.list(qo.getSiteName(), qo.getProtocol());
		return lst;
	}

	@Transactional(rollbackFor = Exception.class)
	public TzhPlanning save(TzhPlanning newModel) {
		UserInfo currentUser = ContextUtils.getCurrentUser();

		if(newModel.getIsdeleted() == false) {
			if(StringUtils.isNotBlank(newModel.getId())) {
				// 創建備份已刪除數據
				TzhPlanningExample originalExample = new TzhPlanningExample();
				TzhPlanningExample.Criteria originalCriteria = originalExample.or();
				originalCriteria.andIdEqualTo(newModel.getId());
				List<TzhPlanning> lstOriginalModel = mapper.selectByExample(originalExample);
				if(lstOriginalModel.size() > 0) {
					TzhPlanning originalModel = lstOriginalModel.get(0);
					originalModel.setId(UUID.randomUUID().toString());
					originalModel.setIsdeleted(true);
					originalModel.setDeletedby(currentUser.getUsername());
					originalModel.setDeletedtime(LocalDateTime.now());
					mapper.insertSelective(originalModel);
				}

				// 新增數據(保留舊ID)
				TzhPlanningExample newExample = new TzhPlanningExample();
				TzhPlanningExample.Criteria newCriteria = newExample.or();
				newCriteria.andIdEqualTo(newModel.getId());
				newCriteria.andIsdeletedEqualTo(false);
				newModel.setCreatedby(currentUser.getUsername());
				newModel.setCreatedtime(LocalDateTime.now());
				newModel.setDeletedby(null);
				newModel.setDeletedtime(null);
				mapper.deleteByExample(newExample);
				mapper.insertSelective(newModel);
			} else {
				// 新增數據(新建ID)
				newModel.setId(UUID.randomUUID().toString());
				newModel.setCreatedby(currentUser.getUsername());
				newModel.setCreatedtime(LocalDateTime.now());
				mapper.insertSelective(newModel);
			}
		} else {
			// 刪除數據
			TzhPlanningExample example = new TzhPlanningExample();
			TzhPlanningExample.Criteria criteria = example.or();
			criteria.andIdEqualTo(newModel.getId());
			newModel.setIsdeleted(false);
			newModel.setDeletedby(currentUser.getUsername());
			newModel.setDeletedtime(LocalDateTime.now());
			mapper.updateByExampleSelective(newModel, example);
		}

		return newModel;
	}
}
