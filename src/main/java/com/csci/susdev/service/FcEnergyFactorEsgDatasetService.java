package com.csci.susdev.service;

import com.csci.common.exception.ServiceException;
import com.csci.susdev.annotation.LogMethod;
import com.csci.susdev.mapper.FcEnergyFactorEsgDatasetMapper;
import com.csci.susdev.model.FcCarbonFactorHk;
import com.csci.susdev.model.FcEnergyFactorEsgDataset;
import com.csci.susdev.model.FcEnergyFactorEsgDatasetExample;
import com.csci.susdev.model.ResultPage;
import com.csci.susdev.modelcovt.FcEnergyFactorEsgDatasetConverter;
import com.csci.susdev.qo.FcEnergyFactorEsgDatasetQO;
import com.csci.susdev.util.NamingConventionUtils;
import com.csci.susdev.util.SimpTradUtil;
import com.csci.susdev.vo.FcEnergyFactorEsgDatasetVO;
import com.github.pagehelper.PageHelper;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.List;
import java.util.stream.Collectors;

import static com.csci.susdev.service.ServiceHelper.checkExist;

@Service
@LogMethod
public class FcEnergyFactorEsgDatasetService {

    @Resource
    private FcEnergyFactorEsgDatasetMapper fcEnergyFactorEsgDatasetMapper;


    @Transactional(rollbackFor = Exception.class)
    public int insertSelective(FcEnergyFactorEsgDataset record) {
        return fcEnergyFactorEsgDatasetMapper.insertSelective(record);
    }

    public FcEnergyFactorEsgDataset selectByPrimaryKey(String id) {
        return fcEnergyFactorEsgDatasetMapper.selectByPrimaryKey(id);
    }

    public FcEnergyFactorEsgDatasetVO getFcEnergyFactorEsgDataset(String id) {
        checkExist(id, "id不能为空");
        FcEnergyFactorEsgDataset fcEnergyFactorEsgDataset = selectByPrimaryKey(id);
        checkExist(fcEnergyFactorEsgDataset, "未找到对应的记录");
        if(fcEnergyFactorEsgDataset.getIsDeleted()) {
            throw new ServiceException("紀錄已被刪除");
        }
        return FcEnergyFactorEsgDatasetConverter.convertToVO(fcEnergyFactorEsgDataset);
    }


    @Transactional(rollbackFor = Exception.class)
    public String saveFcEnergyFactorEsgDataset(FcEnergyFactorEsgDatasetVO fcEnergyFactorEsgDatasetVO) {
        if (StringUtils.isBlank(fcEnergyFactorEsgDatasetVO.getId())) {
            // 新增
            return doAdd(fcEnergyFactorEsgDatasetVO);
        } else {
            checkExist(fcEnergyFactorEsgDatasetVO.getLastUpdateVersion(), "更新时版本号不能为空");
            // 修改
            return doUpdate(fcEnergyFactorEsgDatasetVO);
        }

    }

    public ResultPage<FcEnergyFactorEsgDatasetVO> listFcEnergyFactorEsgDataset(FcEnergyFactorEsgDatasetQO fcEnergyFactorEsgDatasetQO) {
        FcEnergyFactorEsgDatasetExample example = new FcEnergyFactorEsgDatasetExample();

        SimpTradUtil simpTradUtil = new SimpTradUtil();
        String chineseNameTc = simpTradUtil.convert2Trad(fcEnergyFactorEsgDatasetQO.getChineseName());
        String chineseNameSc = simpTradUtil.convert2Simp(fcEnergyFactorEsgDatasetQO.getChineseName());

        if(StringUtils.isNotBlank(fcEnergyFactorEsgDatasetQO.getChineseName())) {
            example.or().andChineseNameLike("%" + chineseNameTc + "%").andIsDeletedEqualTo(false);
            example.or().andChineseNameLike("%" + chineseNameSc + "%").andIsDeletedEqualTo(false);
        } else {
            example.or().andIsDeletedEqualTo(false);
        }

        if (StringUtils.isNotBlank(fcEnergyFactorEsgDatasetQO.getOrderBy())) {
            example.setOrderByClause(NamingConventionUtils.snakeCaseToCamelCase(fcEnergyFactorEsgDatasetQO.getOrderBy()));
        }

        PageHelper.startPage(fcEnergyFactorEsgDatasetQO.getCurPage(), fcEnergyFactorEsgDatasetQO.getPageSize());
        List<FcEnergyFactorEsgDataset> fcEnergyFactorEsgDatasets = fcEnergyFactorEsgDatasetMapper.selectByExample(example);
        return new ResultPage<>(fcEnergyFactorEsgDatasets, fcEnergyFactorEsgDatasets.stream().map(FcEnergyFactorEsgDatasetConverter::convertToVO).collect(Collectors.toList()));
    }

    @Transactional(rollbackFor = Exception.class)
    public void deleteFcEnergyFactorEsgDataset(String id) {
        FcEnergyFactorEsgDataset record = selectByPrimaryKey(id);
        record.setIsDeleted(true);
        fcEnergyFactorEsgDatasetMapper.updateByPrimaryKeySelective(record);
    }

    private String doAdd(FcEnergyFactorEsgDatasetVO fcEnergyFactorEsgDatasetVO) {
        FcEnergyFactorEsgDataset fcEnergyFactorEsgDataset = FcEnergyFactorEsgDatasetConverter.convertToModel(fcEnergyFactorEsgDatasetVO);
        fcEnergyFactorEsgDatasetMapper.insertSelective(fcEnergyFactorEsgDataset);
        return fcEnergyFactorEsgDataset.getId();
    }

    private String doUpdate(FcEnergyFactorEsgDatasetVO fcEnergyFactorEsgDatasetVO) {
        FcEnergyFactorEsgDataset originalRecord = selectByPrimaryKey(fcEnergyFactorEsgDatasetVO.getId());
        FcEnergyFactorEsgDataset fcEnergyFactorEsgDataset = FcEnergyFactorEsgDatasetConverter.convertToModelWithBase(fcEnergyFactorEsgDatasetVO, originalRecord);

        FcEnergyFactorEsgDatasetExample example = new FcEnergyFactorEsgDatasetExample();
        example.or().andIdEqualTo(fcEnergyFactorEsgDatasetVO.getId()).andLastUpdateVersionEqualTo(fcEnergyFactorEsgDatasetVO.getLastUpdateVersion());
        int updateCount = fcEnergyFactorEsgDatasetMapper.updateByExample(fcEnergyFactorEsgDataset, example);
        if (updateCount == 0) {
            throw new ServiceException("更新失败，可能是数据已被修改，请刷新后重试");
        }
        return fcEnergyFactorEsgDataset.getId();
    }

    public List<FcEnergyFactorEsgDataset> selectByExample(FcEnergyFactorEsgDatasetExample example) {
        return fcEnergyFactorEsgDatasetMapper.selectByExample(example);
    }
}
