package com.csci.susdev.service;

import com.baidubce.http.ApiExplorerClient;
import com.baidubce.http.HttpMethodName;
import com.baidubce.model.ApiExplorerRequest;
import com.baidubce.model.ApiExplorerResponse;
import com.csci.susdev.annotation.DS;
import com.csci.susdev.annotation.LogMethod;
import com.csci.susdev.constant.DatasourceContextEnum;
import com.csci.susdev.vo.OcrElectricityBillVO;
import com.csci.susdev.vo.OcrNaturalGasBillVO;
import com.csci.susdev.vo.OcrResultVO;
import com.csci.susdev.vo.OcrWaterBillVO;
import com.github.houbb.opencc4j.util.ZhConverterUtil;
import com.google.gson.Gson;

import net.bytebuddy.dynamic.scaffold.MethodRegistry.Handler.ForAbstractMethod;

import org.apache.commons.codec.binary.Base64;
import org.apache.commons.validator.GenericValidator;
import org.apache.pdfbox.Loader;
import org.apache.pdfbox.pdmodel.PDDocument;
import org.apache.pdfbox.rendering.ImageType;
import org.apache.pdfbox.rendering.PDFRenderer;
import org.json.JSONObject;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.core.io.InputStreamResource;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import java.util.Objects;

import javax.imageio.ImageIO;
import javax.imageio.stream.ImageOutputStream;

import java.awt.image.BufferedImage;
import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import java.io.InputStream;
import java.net.URLEncoder;

@Service
@DS(DatasourceContextEnum.SUSDEV)
@LogMethod
public class OcrService {

    @Value("${baidu.ocr.url}")
    private String url;
    
    @Value("${baidu.ocr.clientId}")
    private String clientId;

    @Value("${baidu.ocr.clientSecret}")
    private String clientSecret;

    @Value("${baidu.ocr.grantType}")
    private String grantType;
    
	public String getBaiduAccessToken() throws Exception {
        ApiExplorerRequest request = new ApiExplorerRequest(HttpMethodName.POST, url);
        
        request.addHeaderParameter("Content-Type", "application/json;charset=UTF-8");
        request.addQueryParameter("client_id", clientId);
        request.addQueryParameter("client_secret", clientSecret);
        request.addQueryParameter("grant_type", grantType);

        ApiExplorerClient client = new ApiExplorerClient();

        try {
          ApiExplorerResponse response = client.sendRequest(request);
          JSONObject jsonObject = new JSONObject(response.getResult());
          return jsonObject.getString("access_token");
        } catch (Exception ex) {
          throw new Exception("無法取得 Access Token", ex);
        }
    }
    
    public OcrWaterBillVO extractWaterBillInfo(String json) throws Exception {
        OcrResultVO ocrResultVO = new Gson().fromJson(json, OcrResultVO.class);
        OcrWaterBillVO ocrWaterBillVO = new OcrWaterBillVO();
        String dateFormat = "dd/MM/yyyy";
        
        //提取ChargeNumber
        int iChargeNumber = 0;
        for(int i=0; i<ocrResultVO.getWords_result().length; i++) {
        	if(ocrResultVO.getWords_result()[i] != null && "Charge No.".equals(ocrResultVO.getWords_result()[i].getWords())) {
        		iChargeNumber = i + 1;
        		break;
        	}
        }
        if(iChargeNumber != 0) {
        	ocrWaterBillVO.setChargeNumber(ocrResultVO.getWords_result()[iChargeNumber].getWords());
        }
        //提取iAccountNumber
        int iAccountNumber = 0;
        for(int i=0; i<ocrResultVO.getWords_result().length; i++) {
        	if(ocrResultVO.getWords_result()[i] != null && ocrResultVO.getWords_result()[i].getWords().indexOf("Account Number:") >= 0) {
        		iAccountNumber = i;
        		break;
        	}
        }
        if(iChargeNumber != 0) {
        	ocrWaterBillVO.setAccountNumber(ocrResultVO.getWords_result()[iAccountNumber].getWords().replace("Account Number:", "").replace(" ", ""));
        }

        //提取TotalAmountDue
        int iTotalAmountDue = 0;
        for(int i=0; i<ocrResultVO.getWords_result().length; i++) {
        	if(ocrResultVO.getWords_result()[i] != null && "Total Amount Due".equals(ocrResultVO.getWords_result()[i].getWords().trim())) {
        		iTotalAmountDue = i + 1;
        		break;
        	}
        }
        if(iTotalAmountDue != 0 && isCurrency(ocrResultVO.getWords_result()[iTotalAmountDue].getWords())) {
        	ocrWaterBillVO.setTotalAmountDue(ocrResultVO.getWords_result()[iTotalAmountDue].getWords());
        }
        
        //提取TotalCharges
        int iTotalCharges = 0;
        for(int i=0; i<ocrResultVO.getWords_result().length; i++) {
        	if(ocrResultVO.getWords_result()[i] != null && "Total Charges".equals(ocrResultVO.getWords_result()[i].getWords().trim())) {
        		iTotalCharges = i + 1;
        		break;
        	}
        }
        if(iTotalCharges != 0 && isCurrency("$" + ocrResultVO.getWords_result()[iTotalCharges].getWords())) {
        	ocrWaterBillVO.setTotalCharges("$" + ocrResultVO.getWords_result()[iTotalCharges].getWords());
        }
        
        //提取LastPaymentDate, LastPayment, DepositHeld, DisputeAmount, InstalmentAmount
        int iLastPaymentDate = 0;
        for(int i=0; i<ocrResultVO.getWords_result().length; i++) {
        	if(ocrResultVO.getWords_result()[i] != null && "Last payment date".equals(ocrResultVO.getWords_result()[i].getWords().trim())) {
        		iLastPaymentDate = i + 5;
        		break;
        	}
        }
        if(iLastPaymentDate != 0) {
        	if(isDate(ocrResultVO.getWords_result()[iLastPaymentDate].getWords(), dateFormat)) {
            	ocrWaterBillVO.setLastPaymentDate(ocrResultVO.getWords_result()[iLastPaymentDate].getWords());
        	}
        	if(isCurrency(ocrResultVO.getWords_result()[iLastPaymentDate + 1].getWords())) {
            	ocrWaterBillVO.setLastPayment(ocrResultVO.getWords_result()[iLastPaymentDate + 1].getWords());
        	}
        	if(isCurrency(ocrResultVO.getWords_result()[iLastPaymentDate + 2].getWords())) {
        		ocrWaterBillVO.setDepositHeld(ocrResultVO.getWords_result()[iLastPaymentDate + 2].getWords());
        	}
        	if(isCurrency(ocrResultVO.getWords_result()[iLastPaymentDate + 3].getWords())) {
        		ocrWaterBillVO.setDisputeAmount(ocrResultVO.getWords_result()[iLastPaymentDate + 3].getWords());
        	}
        	if(isCurrency(ocrResultVO.getWords_result()[iLastPaymentDate + 4].getWords())) {
        		ocrWaterBillVO.setInstalmentAmount(ocrResultVO.getWords_result()[iLastPaymentDate + 4].getWords());
        	}
        }
        
        //提取MeterNo, DateStart, ReadingStart, DateEnd, ReadingEnd, Consumption, AverageDailyConsumption
        int iMeterNo = 0;
        for(int i=0; i<ocrResultVO.getWords_result().length; i++) {
        	if(ocrResultVO.getWords_result()[i] != null && "Meter No.".equals(ocrResultVO.getWords_result()[i].getWords().trim())) {
        		iMeterNo = i + 7;
        		break;
        	}
        }
        if(iMeterNo != 0) {
    		ocrWaterBillVO.setMeterNo(ocrResultVO.getWords_result()[iMeterNo].getWords());
    		
        	if(isDate(ocrResultVO.getWords_result()[iMeterNo + 1].getWords(), dateFormat)) {
        		ocrWaterBillVO.setDateStart(ocrResultVO.getWords_result()[iMeterNo + 1].getWords());
        	}
        	
        	ocrWaterBillVO.setReadingStart(ocrResultVO.getWords_result()[iMeterNo + 2].getWords());
        	
        	if(isDate(ocrResultVO.getWords_result()[iMeterNo + 3].getWords(), dateFormat)) {
        		ocrWaterBillVO.setDateEnd(ocrResultVO.getWords_result()[iMeterNo + 3].getWords());
        	}
        	
        	ocrWaterBillVO.setReadingEnd(ocrResultVO.getWords_result()[iMeterNo + 4].getWords());
        	
    		ocrWaterBillVO.setConsumption(ocrResultVO.getWords_result()[iMeterNo + 5].getWords());
    		
        	ocrWaterBillVO.setAverageDailyConsumption(ocrResultVO.getWords_result()[iMeterNo + 6].getWords());
        }

        return ocrWaterBillVO;
    }
    
    public OcrElectricityBillVO extractElectricityBillInfo(String json) throws Exception {
        OcrResultVO ocrResultVO = new Gson().fromJson(json, OcrResultVO.class);
        OcrElectricityBillVO ocrElectricityBillVO = new OcrElectricityBillVO();
        String dateFormat = "dd-MM-yy";
        
        //提取AccountNumber
        int iAccountNumber = 0;
        for(int i=0; i<ocrResultVO.getWords_result().length; i++) {
        	if(ocrResultVO.getWords_result()[i] != null && ocrResultVO.getWords_result()[i].getWords().indexOf("Account Number") >= 0) {
        		iAccountNumber = i + 1;
        		break;
        	}
        }
        if(iAccountNumber != 0) {
        	ocrElectricityBillVO.setAccountNumber(ocrResultVO.getWords_result()[iAccountNumber].getWords());
        }
        
        //提取AccountNumber2
        int iAccountNumber2 = 0;
        for(int i=0; i<ocrResultVO.getWords_result().length; i++) {
        	if(ocrResultVO.getWords_result()[i] != null && ZhConverterUtil.toTraditional(ocrResultVO.getWords_result()[i].getWords()).indexOf("號碼：") >= 0) {
        		iAccountNumber2 = i + 1;
        		break;
        	}
        }
        if(iAccountNumber2 != 0) {
        	ocrElectricityBillVO.setAccountNumber2(ocrResultVO.getWords_result()[iAccountNumber2].getWords());
        }
        
        //提取TotalCharges
        int iTotalCharges = 0;
        for(int i=0; i<ocrResultVO.getWords_result().length; i++) {
        	if(ocrResultVO.getWords_result()[i] != null && ZhConverterUtil.toTraditional(ocrResultVO.getWords_result()[i].getWords()).indexOf("總數") >= 0 && ZhConverterUtil.toTraditional(ocrResultVO.getWords_result()[i].getWords()).indexOf(":") < 0) {
        		iTotalCharges = i + 4;
        		break;
        	}
        }
        if(iTotalCharges != 0 && isCurrency(ocrResultVO.getWords_result()[iTotalCharges].getWords())) {
        	ocrElectricityBillVO.setTotalCharges(ocrResultVO.getWords_result()[iTotalCharges].getWords());
        }

        //提取TotalCharges2
        int iTotalCharges2 = 0;
        for(int i=0; i<ocrResultVO.getWords_result().length; i++) {
        	if(ocrResultVO.getWords_result()[i] != null && ZhConverterUtil.toTraditional(ocrResultVO.getWords_result()[i].getWords()).indexOf("總數：") >= 0) {
        		iTotalCharges2 = i + 1;
        		break;
        	} 	
        }
        if(iTotalCharges2 != 0 && isCurrency(ocrResultVO.getWords_result()[iTotalCharges2].getWords())) {
        	ocrElectricityBillVO.setTotalCharges2(ocrResultVO.getWords_result()[iTotalCharges2].getWords());
        }
        
        //提取ConsumptionInkWh
        int iConsumptionInkWh = 0;
        for(int i=0; i<ocrResultVO.getWords_result().length; i++) {
        	if(ocrResultVO.getWords_result()[i] != null && ZhConverterUtil.toTraditional(ocrResultVO.getWords_result()[i].getWords()).indexOf("小計") >= 0 && ZhConverterUtil.toTraditional(ocrResultVO.getWords_result()[i].getWords()).indexOf("度") >= 0) {
        		iConsumptionInkWh = i;
        		break;
        	}
        }
        if(iConsumptionInkWh != 0) {
    		ocrElectricityBillVO.setConsumptionInkWh(ZhConverterUtil.toTraditional(ocrResultVO.getWords_result()[iConsumptionInkWh].getWords())
    				.replaceAll("[^0-9]", ""));
        }
        
        
        //提取DateStart, DateEnd
        int iDate = 0;
        for(int i=0; i<ocrResultVO.getWords_result().length; i++) {
        	if(ocrResultVO.getWords_result()[i] != null && ZhConverterUtil.toTraditional(ocrResultVO.getWords_result()[i].getWords()).indexOf("由") >= 0) {
        		iDate = i;
        		break;
        	}
        }
        if(iDate != 0) {
        	int iFromIdx = ZhConverterUtil.toTraditional(ocrResultVO.getWords_result()[iDate].getWords()).indexOf("由");
        	int iToIdx = ZhConverterUtil.toTraditional(ocrResultVO.getWords_result()[iDate].getWords()).indexOf("至");
        	
        	String dateStart = ocrResultVO.getWords_result()[iDate].getWords().substring(iFromIdx + 1, iToIdx);
        	String dateEnd = ocrResultVO.getWords_result()[iDate].getWords().substring(iToIdx + 1);
        	
        	if(isDate(dateStart, dateFormat)) {
            	ocrElectricityBillVO.setDateStart(dateStart);
        	}
        	if(isDate(dateEnd, dateFormat)) {
            	ocrElectricityBillVO.setDateEnd(dateEnd);
        	}
        }

        //提取ConsumptionInCo2KgPerkWh
        int iConsumptionInCo2KgPerkWh = 0;
        for(int i=0; i<ocrResultVO.getWords_result().length; i++) {
        	if(ocrResultVO.getWords_result()[i] != null && ZhConverterUtil.toTraditional(ocrResultVO.getWords_result()[i].getWords()).indexOf("千克") >= 0) {
        		iConsumptionInCo2KgPerkWh = i;
        		break;
        	}
        }
        if(iConsumptionInCo2KgPerkWh != 0) {
    		ocrElectricityBillVO.setConsumptionInCo2KgPerkWh(ZhConverterUtil.toTraditional(ocrResultVO.getWords_result()[iConsumptionInCo2KgPerkWh].getWords())
    				.replace("千克", "").replace(" ", ""));
        }

        return ocrElectricityBillVO;
    }
    
    public OcrNaturalGasBillVO extractNaturalGasBillInfo(String json) throws Exception {
        OcrResultVO ocrResultVO = new Gson().fromJson(json, OcrResultVO.class);
        OcrNaturalGasBillVO ocrNaturalGasBillVO = new OcrNaturalGasBillVO();
        String dateFormat = "dd/MM/yyyy";
        
        //提取InvoiceNumber
        int iInvoiceNumber = 0;
        for(int i=0; i<ocrResultVO.getWords_result().length; i++) {
        	if(ocrResultVO.getWords_result()[i] != null && ocrResultVO.getWords_result()[i].getWords().indexOf("Invoice No") >= 0) {
        		iInvoiceNumber = i + 1;
        		break;
        	}
        }
        if(iInvoiceNumber != 0) {
        	ocrNaturalGasBillVO.setInvoiceNumber(ocrResultVO.getWords_result()[iInvoiceNumber].getWords());
        }
        //提取Date
        int iDate = 0;
        for(int i=0; i<ocrResultVO.getWords_result().length; i++) {
        	if(ocrResultVO.getWords_result()[i] != null && ocrResultVO.getWords_result()[i].getWords().indexOf("Date") >= 0) {
        		iDate = i + 1;
        		break;
        	}
        }
        if(iDate != 0 && isCurrency(ocrResultVO.getWords_result()[iDate].getWords())) {
        	ocrNaturalGasBillVO.setDate(ocrResultVO.getWords_result()[iDate].getWords());
        }
        
        //提取KgPerUnit, Quantity
        int iKgPerUnit = 0;
        for(int i=0; i<ocrResultVO.getWords_result().length; i++) {
        	if(ocrResultVO.getWords_result()[i] != null && ZhConverterUtil.toTraditional(ocrResultVO.getWords_result()[i].getWords()).indexOf("kg") >= 0) {
        		iKgPerUnit = i;
        		break;
        	}
        }
        if(iKgPerUnit != 0) {
        	String s = ocrResultVO.getWords_result()[iKgPerUnit].getWords().replaceAll("[^kg0-9]", " ");
        	String[] sArr = s.split(" ");
        	String sKgPerUnit = "";
        	for(String str: sArr) {
        		if(str.indexOf("kg") >= 0) {
        			sKgPerUnit = str.replace("kg", "");
        			break;
        		}
        	}
        	ocrNaturalGasBillVO.setKgPerUnit(sKgPerUnit);
        	
        	String sQuantity = ocrResultVO.getWords_result()[iKgPerUnit + 1].getWords();
        	if(sQuantity.replaceAll("[^0-9]", "").length() > 0) {
            	ocrNaturalGasBillVO.setQuantity(sQuantity);
        	}
        }
        
        return ocrNaturalGasBillVO;
    }
    
    private boolean isDate(String date, String format) {
    	return GenericValidator.isDate(date, format, true);
    }
    
    private boolean isCurrency(String string) {
    	return string.indexOf(".") >= 0 && string.indexOf("$") >= 0;
    }
    
    public String convertPdf2Json(MultipartFile pdf) throws Exception {
        return convertPng2Json(convertPdf2Png(pdf));
    }

    private String convertPng2Json(byte[] png) throws Exception {
        String path = "https://aip.baidubce.com/rest/2.0/ocr/v1/general";
        ApiExplorerRequest request = new ApiExplorerRequest(HttpMethodName.POST, path);
        
        request.addHeaderParameter("Content-Type", "application/x-www-form-urlencoded;charset=UTF-8");
        request.addQueryParameter("access_token", getBaiduAccessToken());

    	String jsonBody = "image=" + URLEncoder.encode(new String(Base64.encodeBase64(png)), "UTF-8");
        request.setJsonBody(jsonBody);

        ApiExplorerClient client = new ApiExplorerClient();
        try {
          ApiExplorerResponse response = client.sendRequest(request);
          System.out.println("getResult" + response.getResult());
          return response.getResult();
        } catch (Exception ex) {
            throw new Exception("png转换json失败", ex);
        }
    }
    
    private byte[] convertPdf2Png(MultipartFile pdf) throws Exception {
        try (final PDDocument document = Loader.loadPDF(pdf.getInputStream())) {
            PDFRenderer pdfRenderer = new PDFRenderer(document);
            BufferedImage pdfImage = null;
            int pageSize = document.getNumberOfPages();
            int y = 0;
            for (int i = 0; i < pageSize; ++i) {
                BufferedImage bim = pdfRenderer.renderImageWithDPI(i, 300, ImageType.RGB);
                if (Objects.isNull(pdfImage)) {
                    pdfImage = new BufferedImage(bim.getWidth(),
                        bim.getHeight() * pageSize, BufferedImage.TYPE_INT_ARGB);
                }
                pdfImage.getGraphics().drawImage(bim, 0, y, null);
                y += bim.getHeight();
            }
            assert pdfImage != null;
            ByteArrayOutputStream imgBos = new ByteArrayOutputStream();
            ImageOutputStream imgOs = ImageIO.createImageOutputStream(imgBos);
            ImageIO.write(pdfImage, "png", imgOs);
            return imgBos.toByteArray();
        } catch (Exception ex) {
            throw new Exception("pdf转换png失败", ex);
        }
    }
}
