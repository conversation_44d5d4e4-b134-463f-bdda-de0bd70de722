package com.csci.susdev.service;

import com.csci.susdev.controller.menu.PageDTO;
import com.csci.susdev.model.ResultBean;
import com.csci.susdev.model.ResultPage;
import com.csci.susdev.model.TzhBsOrgMenu;
import com.csci.susdev.qo.TzhBsOrgMenuQO;

public interface ITzhBsOrgMenuService {

    /**
     * 编辑
     * @return
     */
    ResultBean edit(TzhBsOrgMenu tzhBsOrgMenu);

    /**
     * 删除
     * @return
     */
    ResultBean delete(TzhBsOrgMenu tzhBsOrgMenu);
    /**
     *
     * 新增：点击新增按钮，直接弹窗，直接新增某组织机构的一条页面配置，如左侧截图。其中除了备注外，都是必填项。
     * 点击确定后，新增写入数据库的判断：组织机构+展厅模块+协议+排序，不能够重复；组织机构+展厅模块+协议+页签路径（通过页签名称（英文）关联获取），不能够重复。
     * @param tzhBsOrgMenu
     * @return
     */
    ResultBean save(TzhBsOrgMenu tzhBsOrgMenu);

    ResultPage getAll(PageDTO<TzhBsOrgMenuQO> pageDTO);

    ResultBean deleteById(String id);
}
