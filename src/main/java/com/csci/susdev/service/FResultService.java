package com.csci.susdev.service;

import com.csci.common.exception.ServiceException;
import com.csci.susdev.annotation.DS;
import com.csci.susdev.annotation.LogMethod;
import com.csci.susdev.constant.DatasourceContextEnum;
import com.csci.tzh.mapper.*;
import com.csci.tzh.model.*;
import com.csci.tzh.model.FResultExample.Criteria;
import com.csci.susdev.model.ResultPage;
import com.csci.tzh.qo.*;
import com.csci.tzh.vo.*;
import com.github.pagehelper.PageHelper;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

@Service
@DS(DatasourceContextEnum.TANZHONGHE)
@LogMethod
public class FResultService {

	@Autowired
	private FResultMapper mapper;
	
	@Autowired
	private FResultCustomMapper customMapper;

	public List<FResult> selectByExample(FResultExample example) {
		return mapper.selectByExample(example);
	}

	/**
	 * 月度排放 数据列表
	 *
	 * @param qo
	 * @return
	 */
	public ResultPage<FResultVO> listFResult(FResultPageableQO qo) {
		if(qo.getRecordYearMonthFrom() == null) {
			qo.setRecordYearMonthFrom(0);
		}
		if(qo.getRecordYearMonthTo() == null) {
			qo.setRecordYearMonthTo(999999);
		}

		PageHelper.startPage(qo.getCurPage(), qo.getPageSize());
		List<FResultVO> lst = customMapper.listFResult(qo.getSiteName(), qo.getProtocol(),
				qo.getRecordYearMonthFrom(), qo.getRecordYearMonthTo());
		ResultPage<FResultVO> resultPage = new ResultPage<>(lst, true);

		return resultPage;
	}

	/**
	 * 月度排放 数据列表
	 *
	 * @param qo
	 * @return
	 */
	public ResultPage<FResultTotalVO> listFResultTotal(FResultPageableQO qo) {
		PageHelper.startPage(qo.getCurPage(), qo.getPageSize());
		List<FResultTotalVO> lst = customMapper.listFResultTotal(qo.getSiteName(), qo.getRecordYearMonthFrom(), qo.getRecordYearMonthTo());
		ResultPage<FResultTotalVO> resultPage = new ResultPage<>(lst, true);
		return resultPage;
	}
	
}
