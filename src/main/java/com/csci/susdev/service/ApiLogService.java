package com.csci.susdev.service;

import com.csci.common.exception.ServiceException;
import com.csci.susdev.annotation.LogMethod;
import com.csci.susdev.constant.NamingConventionEnum;
import com.csci.susdev.constant.SusDevConsts;
import com.csci.susdev.mapper.ApiLogCustomMapper;
import com.csci.susdev.mapper.ApiLogMapper;
import com.csci.susdev.model.*;
import com.csci.susdev.model.ApiLog;
import com.csci.susdev.qo.ApiLogQO;
import com.csci.susdev.qo.ListApiLogByHeadIdQO;
import com.csci.susdev.util.ClientInfoUtils;
import com.csci.susdev.util.MybatisHelper;
import com.csci.susdev.util.RsaUtil;
import com.csci.susdev.util.context.ContextUtils;
import com.csci.susdev.vo.ApiLogVO;
import com.github.pagehelper.PageHelper;
import io.netty.util.internal.StringUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.scheduling.annotation.Async;
import org.springframework.scheduling.annotation.EnableAsync;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.Arrays;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

import static com.csci.susdev.service.ServiceHelper.checkExist;

@EnableAsync
@Service
@LogMethod
@Slf4j
public class ApiLogService  {

    private Logger logger = LoggerFactory.getLogger(ApiLogService.class);

    @Autowired
    private HttpServletRequest request;

    @Resource
    private ApiLogCustomMapper apiLogCustomMapper;

    @Resource
    private ApiLogMapper apiLogMapper;

    @Resource
    private MenuService menuService;

    @Value(("${rsa.ext.privateKey}"))
    private String privateExtKeyString;


    private Integer before = 3600 * 1000;

    public void log(String method, String code, String parameter, String requestJson, String responseJson, long startTime, long endTime) throws ParseException {
        logHandle(method, code, parameter, requestJson, responseJson, "NORMAL", startTime, endTime);
    }

    public void logHandle(String method, String code, String parameter, String requestJson, String responseJson, String type, long startTime, long endTime) throws ParseException {
        String username = ContextUtils.getCurrentUser().getUsername();

        String menuRoutePath = request.getHeader(SusDevConsts.Menu_Route_Path);
        String page = "";
        if(StringUtils.isNotBlank(menuRoutePath)) {
          //  Menu menu = menuService.getByRoutePath(menuRoutePath);
           //老的查询只查询了云平台的表数据,现做出修改(加上了碳中和大屏菜单表的查询)
            String menName = menuService.getAllMenuDataByExample(menuRoutePath);

            if(StringUtils.isNotEmpty(menName)) {
                page = menName;
                //環境績效有多个重复菜单，做特殊处理
                if("環境績效".equals(page)){
                    Menu menu = menuService.getMenuByRoutePath(menuRoutePath);
                    if(menu != null){
                        if("ec37efbb-5e19-42bf-857c-f65d4c6d6103".equals(menu.getParentId())) {
                            page = "環境績效(數據分析)";
                        } else if("ec37efbb-5e19-42bf-857c-f65d4c6d6120".equals(menu.getParentId())){
                            page = "環境績效(環境績效)";
                        }
                    }
                }
            }
        }
        String token = request.getHeader("X-Auth-Token");
        String appId = request.getHeader("External-App-Id");
        String appKey = request.getHeader("External-App-Key");

        String ip = ClientInfoUtils.getIpAddress(request);
        String uri = request.getRequestURI();

        ApiLog log = new ApiLog();
        log.setCode(code);
        log.setUsername(username);
        log.setPage(page);
        log.setToken(token);
        if (StringUtils.isNotEmpty(appId) && StringUtils.isNotEmpty(appKey)) {
            try {
                log.setAppId(RsaUtil.decrypt(appId,privateExtKeyString));
                log.setAppKey(RsaUtil.decrypt(appKey,privateExtKeyString));
            } catch (Exception e) {
                e.printStackTrace();
            }
        }
        log.setMethod(method);
        log.setApi(uri);
        log.setIp(ip);
        log.setType(type);
        SimpleDateFormat sf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss.SSS");
        log.setParameter(parameter);
        log.setRequest(requestJson);
        log.setResponse(responseJson);
        log.setStartTime(sf.format(new Date(startTime)));
        log.setEndTime(sf.format(new Date(endTime)));
        logCreate(log);
    }

    @Async
    public void logCreate(ApiLog log) throws ParseException {
        SimpleDateFormat format = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        Date date = format.parse(log.getStartTime());
        String time = format.format(date.getTime() - before);
        String mac = apiLogCustomMapper.getRecentMac(log.getIp(), time);
        if (StringUtil.isNullOrEmpty(mac)) {
            mac = ClientInfoUtils.getMacAddress(log.getIp());
        }
        log.setMac(mac);
        apiLogMapper.insertSelective(log);
    }

    public ApiLog selectByPrimaryKey(String id) {
        return apiLogMapper.selectByPrimaryKey(id);
    }

    public ApiLog getApiLog(String id) {
        checkExist(id, "id不能为空");
        ApiLog apiLog = selectByPrimaryKey(id);
        checkExist(apiLog, "未找到对应的记录");
        return apiLog;
    }


    public ResultPage<ApiLogVO> listApiLog(ApiLogQO apiLogQO) {
        if (StringUtils.isNotBlank(apiLogQO.getOrderBy())) {
            List<String> fieldNames = Arrays.stream(ApiLogVO.class.getDeclaredFields()).map(field -> field.getName()).collect(Collectors.toList());
            if(MybatisHelper.checkSqlInjectionForOrderBy(apiLogQO.getOrderBy(), fieldNames, NamingConventionEnum.CAMO)) {
                throw new ServiceException("orderBy 有SQL注入行為");
            }
        }

        PageHelper.startPage(apiLogQO.getCurPage(), apiLogQO.getPageSize());
        if (StringUtils.equals(apiLogQO.getCode(), "0")) {
            apiLogQO.setCode("0,200");
        }
        List<ApiLogVO> apiLogVOS = apiLogCustomMapper.list(apiLogQO);
        return new ResultPage<>(apiLogVOS, true);
    }

    /**
     * 根据headId查询操作日志（尽量是操作日志，不需要查询日志）
     * @param qo
     * @return
     */
    public ResultPage<ApiLogVO> listApiLogByHeadId(ListApiLogByHeadIdQO qo) {
        PageHelper.startPage(qo.getCurPage(), qo.getPageSize());
        List<ApiLogVO> apiLogVOS = apiLogCustomMapper.listApiLogByHeadId(qo.getHeadId());
        return new ResultPage<>(apiLogVOS, true);
    }
}

