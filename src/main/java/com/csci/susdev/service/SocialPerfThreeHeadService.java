package com.csci.susdev.service;

import cn.hutool.core.util.StrUtil;
import com.alibaba.excel.EasyExcel;
import com.csci.common.exception.ServiceException;
import com.csci.susdev.annotation.LogMethod;
import com.csci.susdev.constant.SusDevConsts;
import com.csci.susdev.listener.SocialPerfThreeListener;
import com.csci.susdev.mapper.SocialPerfThreeDetailMapper;
import com.csci.susdev.mapper.SocialPerfThreeHeadMapper;
import com.csci.susdev.model.*;
import com.csci.susdev.modelcovt.SocialPerfThreeHeadConverter;
import com.csci.susdev.modelcovt.SocialPerfThreeTableDataConverter;
import com.csci.susdev.util.SpringContextUtil;
import com.csci.susdev.util.redis.RedisLockUtil;
import com.csci.susdev.vo.SocialPerfThreeHeadVO;
import com.csci.susdev.vo.SocialPerfThreeImportDataVO;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.io.IOException;
import java.io.InputStream;
import java.util.List;
import java.util.Objects;
import java.util.UUID;
import java.util.stream.Collectors;

import static com.csci.susdev.service.ServiceHelper.checkExist;

@Service
@LogMethod
public class SocialPerfThreeHeadService {
    /**
     * 初始化记录的过期时间
     */
    @Value("${lock.init.head.expire.milis:1000}")
    private long lockInitHeadExpireMilis;
    /**
     * 查询head记录时，获取锁失败循环等待的次数
     */
    @Value("${query.head.wait.loop.times:5}")
    private int lockQueryHeadWaitLoopTimes;
    /**
     * 查询head记录时，获取锁失败循环时每次休眠的时间
     */
    @Value("${query.head.wait.sleep.milis:50}")
    private int lockQueryHeadWaitSleepMilis;
    @Resource
    private SocialPerfThreeHeadMapper socialPerfThreeHeadMapper;

    @Resource
    private SocialPerfThreeDetailMapper socialPerfThreeDetailMapper;

    @Transactional(rollbackFor = Exception.class)
    public SocialPerfThreeHeadVO getOrInitSocialPerfThree(String organizationId, Integer year, Integer month) {
        checkExist(organizationId, "组织机构编号不能为空");
        checkExist(year, "年份不能为空");
        if(ObjectUtils.isEmpty(month)) {
            month = 12;
        }

        SocialPerfThreeHead head = getOrInitHeadRecord(organizationId, year, month);

        return SocialPerfThreeHeadConverter.convert(head);
    }

    /**
     * 查询或初始化记录，如果获取失败，直接抛出异常退出
     * @param
     * @return
     */
    private SocialPerfThreeHead getOrInitHeadRecord(String organizationId, Integer year, Integer month) {
        SocialPerfThreeHead head = findSocialPerfThreeHead(organizationId, year, month);
        if (Objects.isNull(head)) {
            //分布式锁的key：分别表示前缀:业务:组织ID:年:月
            String key = StrUtil.format(SusDevConsts.DISTRIBUTE_LOCK_KEY_FORMAT, SusDevConsts.PROJECT_PREFIX,
                    "SocialPerfThreeHead:getOrInit", organizationId, year, month);
            //超时时间1s
            boolean redisLock = RedisLockUtil.lock(key, lockInitHeadExpireMilis);
            if (!redisLock) {
                //获取锁失败，停顿50ms一次，停顿5次
                int count = lockQueryHeadWaitLoopTimes;
                while(count-- > 0){
                    head = findSocialPerfThreeHead(organizationId, year, month);
                    if(head != null){
                        break;
                    }
                    try {
                        Thread.sleep(lockQueryHeadWaitSleepMilis);
                    } catch (InterruptedException e) {
                        throw new RuntimeException(e);
                    }
                }
            }else{
                try{
                    head = createSocialPerfThreeHead(organizationId, year, month);
                }
                finally {
                    RedisLockUtil.unlock(key);
                }
            }
        }
        //如果最终没拿到数据，直接返回
        if(head == null){
            throw new ServiceException("生成记录失败, 请稍后再尝试！");
        }
        return head;
    }


    public SocialPerfThreeHead getPrevHead(String organizationId, Integer year, Integer month) {
        SocialPerfThreeHeadExample example = new SocialPerfThreeHeadExample();
        example.or().andIsActiveEqualTo(true).andOrganizationIdEqualTo(organizationId)
                .andYearEqualTo(year).andMonthLessThan(month);
        example.setOrderByClause("month desc");
        List<SocialPerfThreeHead> lst = selectByExample(example);
        if(lst.size() > 0) {
            return lst.get(0);
        } else {
            return null;
        }
    }

    void initDetailsWithPrevData(String prevHeadId, String curHeadId) {
        SocialPerfThreeDetailExample example = new SocialPerfThreeDetailExample();
        example.or().andHeadIdEqualTo(prevHeadId);
        List<SocialPerfThreeDetail> details = socialPerfThreeDetailMapper.selectByExample(example);
        for(SocialPerfThreeDetail detail : details) {
            detail.setId(null);
            detail.setHeadId(curHeadId);
            socialPerfThreeDetailMapper.insert(detail);
        }
    }

    /**
     * 创建社会绩效三主表记录
     * 同时初始化明细数据
     *
     * @param organizationId 组织机构编号
     * @param year           年份
     * @param month          月份
     * @return
     */
    private SocialPerfThreeHead createSocialPerfThreeHead(String organizationId, Integer year, Integer month) {
        SocialPerfThreeHead head = findSocialPerfThreeHead(organizationId, year, month);
        if (Objects.nonNull(head)) {
            return head;
        }

        head = new SocialPerfThreeHead();
        head.setOrganizationId(organizationId);
        head.setYear(year);
        head.setMonth(month);
        head.setIsActive(Boolean.TRUE);
        socialPerfThreeHeadMapper.insertSelective(head);

        // 在这里进行明细数据初始化是因为放在同步方法里面避免并发问题
        SocialPerfThreeHead prevHead = getPrevHead(organizationId, year, month);
        if(ObjectUtils.isNotEmpty(prevHead)) {
            initDetailsWithPrevData(prevHead.getId(), head.getId());
        } else {
            initSocialPerfThreeDetail(head.getId());
        }

        return head;
    }

    /**
     * 根据组织机构编号、年份、月份查询社会绩效考核表头信息
     *
     * @param organizationId 组织机构编号
     * @param year           年份
     * @param month          月份
     * @return
     */
    public SocialPerfThreeHead findSocialPerfThreeHead(String organizationId, Integer year, Integer month) {
        SocialPerfThreeHeadExample example = new SocialPerfThreeHeadExample();
        example.or().andOrganizationIdEqualTo(organizationId).andYearEqualTo(year)
                .andMonthEqualTo(month).andIsActiveEqualTo(Boolean.TRUE);
        List<SocialPerfThreeHead> lstSocialPerfThreeHead = socialPerfThreeHeadMapper.selectByExample(example);
        if (CollectionUtils.isEmpty(lstSocialPerfThreeHead)) {
            return null;
        }
        if (lstSocialPerfThreeHead.size() > 1) {
            throw new ServiceException("社会绩效三表头信息不唯一");
        }
        return lstSocialPerfThreeHead.get(0);
    }

    void initSocialPerfThreeDetail(String headId) {
        try (InputStream is = this.getClass().getClassLoader().getResourceAsStream("template/social_perf_two_init.xlsx")) {
            EasyExcel.read(is, SocialPerfThreeImportDataVO.class, new SocialPerfThreeListener(headId)).sheet().doRead();
        } catch (IOException e) {
            throw new ServiceException("數據初始化出錯", e);
        }
    }

    /**
     * 更新社会绩效三主表及明细信息
     *
     * @param socialPerfThreeHeadVO
     */
    @Transactional(rollbackFor = Exception.class)
    public void updateSocialPerfThree(SocialPerfThreeHeadVO socialPerfThreeHeadVO) {
        checkExist(socialPerfThreeHeadVO.getId(), "社会绩效三表头ID不能为空");
        checkExist(socialPerfThreeHeadVO.getLastUpdateVersion(), "社会绩效三表头版本号不能为空");

        WorkflowControlService workflowControlService = SpringContextUtil.getBean(WorkflowControlService.class);
        if (workflowControlService.isNotAllowEdit(socialPerfThreeHeadVO.getId())) {
            throw new ServiceException("已经发起审批流程的单据不允许进行修改");
        }

        if (CollectionUtils.isEmpty(socialPerfThreeHeadVO.getDetails())) {
            throw new ServiceException("社会绩效三明细不能为空");
        }

        // 更新表头
        SocialPerfThreeHead socialPerfThreeHead = SocialPerfThreeHeadConverter.convert(socialPerfThreeHeadVO);
        SocialPerfThreeHeadExample headExample = new SocialPerfThreeHeadExample();
        headExample.or().andIsActiveEqualTo(Boolean.TRUE).andIdEqualTo(socialPerfThreeHeadVO.getId()).andLastUpdateVersionEqualTo(socialPerfThreeHeadVO.getLastUpdateVersion());
        int updateCount = socialPerfThreeHeadMapper.updateByExampleSelective(socialPerfThreeHead, headExample);
        if (updateCount != 1) {
            throw new ServiceException("社会绩效三表头信息已被修改，请刷新后重试");
        }

        // 更新明细
        // 先删除
        SocialPerfThreeDetailExample detailExample = new SocialPerfThreeDetailExample();
        detailExample.or().andHeadIdEqualTo(socialPerfThreeHeadVO.getId());
        socialPerfThreeDetailMapper.deleteByExample(detailExample);
        // 再新增
        List<SocialPerfThreeDetail> details = socialPerfThreeHeadVO.getDetails().stream().map(SocialPerfThreeTableDataConverter::convert).collect(Collectors.toList());
        int seq = 1;
        for (SocialPerfThreeDetail detail : details) {
            detail.setHeadId(socialPerfThreeHeadVO.getId());
            detail.setId(UUID.randomUUID().toString());
            detail.setSeq(seq++);
            socialPerfThreeDetailMapper.insertSelective(detail);
        }
    }

    /**
     * 查询指定编号的社会绩效三记录
     *
     * @param id 社会绩效三编号
     * @return
     */
    public SocialPerfThreeHead selectByPrimaryKey(String id) {
        return socialPerfThreeHeadMapper.selectByPrimaryKey(id);
    }

    public List<SocialPerfThreeHead> selectByExample(SocialPerfThreeHeadExample example) {
        return socialPerfThreeHeadMapper.selectByExample(example);
    }
}
