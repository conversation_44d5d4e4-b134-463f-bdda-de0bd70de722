package com.csci.susdev.service;

import com.csci.common.exception.ServiceException;
import com.csci.susdev.annotation.LogMethod;
import com.csci.susdev.mapper.ProtocolManagementCustomMapper;
import com.csci.susdev.mapper.ProtocolMapper;
import com.csci.susdev.model.*;
import com.csci.susdev.modelcovt.ProtocolConverter;
import com.csci.susdev.qo.ProtocolQO;
import com.csci.susdev.util.NamingConventionUtils;
import com.csci.susdev.util.SimpTradUtil;
import com.csci.susdev.vo.ProtocolVO;
import com.github.pagehelper.PageHelper;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.List;
import java.util.stream.Collectors;

import static com.csci.susdev.service.ServiceHelper.checkExist;

@Service
@LogMethod
public class ProtocolService {

    @Resource
    private ProtocolMapper protocolMapper;

    @Resource
    private ProtocolCategoryService protocolCategoryService;

    @Resource
    private ProtocolSubCategoryService protocolSubCategoryService;

    @Resource
    private ProtocolConfigurationService protocolConfigurationService;

    @Resource
    private ProtocolManagementCustomMapper protocolManagementCustomMapper;


    @Transactional(rollbackFor = Exception.class)
    public int insertSelective(Protocol record) {
        return protocolMapper.insertSelective(record);
    }

    public Protocol selectByPrimaryKey(String id) {
        return protocolMapper.selectByPrimaryKey(id);
    }

    public ProtocolVO getProtocol(String id) {
        checkExist(id, "id不能为空");
        Protocol protocol = selectByPrimaryKey(id);
        checkExist(protocol, "未找到对应的记录");
        if(protocol.getIsDeleted()) {
            throw new ServiceException("紀錄已被刪除");
        }
        return ProtocolConverter.convertToVO(protocol);
    }


    @Transactional(rollbackFor = Exception.class)
    public String saveProtocol(ProtocolVO protocolVO) {
        if (StringUtils.isBlank(protocolVO.getId())) {
            // 新增
            return doAdd(protocolVO);
        } else {
            checkExist(protocolVO.getLastUpdateVersion(), "更新时版本号不能为空");
            // 修改
            return doUpdate(protocolVO);
        }

    }

    public ResultPage<ProtocolVO> listProtocol(ProtocolQO protocolQO) {
        ProtocolExample example = new ProtocolExample();

        SimpTradUtil simpTradUtil = new SimpTradUtil();
        String nameTc = simpTradUtil.convert2Trad(protocolQO.getName());
        String nameSc = simpTradUtil.convert2Simp(protocolQO.getName());

        if(StringUtils.isNotBlank(protocolQO.getName())) {
            example.or().andNameLike("%" + nameTc + "%").andIsDeletedEqualTo(false);
            example.or().andNameScLike("%" + nameSc + "%").andIsDeletedEqualTo(false);
            example.or().andNameEnLike("%" + protocolQO.getName() + "%").andIsDeletedEqualTo(false);
        } else {
            example.or().andIsDeletedEqualTo(false);
        }

        if (StringUtils.isNotBlank(protocolQO.getOrderBy())) {
            example.setOrderByClause(NamingConventionUtils.snakeCaseToCamelCase(protocolQO.getOrderBy()));
        }

        PageHelper.startPage(protocolQO.getCurPage(), protocolQO.getPageSize());
        List<Protocol> protocols = protocolMapper.selectByExample(example);
        return new ResultPage<>(protocols, protocols.stream().map(ProtocolConverter::convertToVO).collect(Collectors.toList()));
    }

    @Transactional(rollbackFor = Exception.class)
    public void deleteProtocol(String id) {
        Protocol record = selectByPrimaryKey(id);

        // 查询大类
        ProtocolCategoryExample protocolCategoryExample = new ProtocolCategoryExample();
        protocolCategoryExample.or().andProtocolIdEqualTo(id).andIsDeletedEqualTo(false);
        List<ProtocolCategory> protocolCategoryList = protocolCategoryService.selectByExample(protocolCategoryExample);
        if(CollectionUtils.isNotEmpty(protocolCategoryList)) {
            // 查询小类
            List<String> categoryIds = protocolCategoryList.stream().map(ProtocolCategory::getId).collect(Collectors.toList());
            ProtocolSubCategoryExample example = new ProtocolSubCategoryExample();
            example.or().andCategoryIdIn(categoryIds).andIsDeletedEqualTo(false);
            List<ProtocolSubCategory> protocolSubCategoryList = protocolSubCategoryService.selectByExample(example);
            if(CollectionUtils.isNotEmpty(protocolSubCategoryList)) {
                List<String> subCategoryIds = protocolSubCategoryList.stream().map(ProtocolSubCategory::getId).collect(Collectors.toList());
                ProtocolConfigurationExample configurationExample = new ProtocolConfigurationExample();
                configurationExample.or().andSubCategoryIdIn(subCategoryIds).andIsDeletedEqualTo(false);
                List<ProtocolConfiguration> protocolConfigurations = protocolConfigurationService.selectByExample(configurationExample);
                if (CollectionUtils.isNotEmpty(protocolConfigurations)) {
                    throw new ServiceException("该协议对应的小类已在协议配置中被使用，不能删除！");
                }
                // 批量删除小类
                protocolManagementCustomMapper.deleteSubCategoryByIds(subCategoryIds);
            }
            // 批量删大类
            protocolManagementCustomMapper.deleteCategoryByIds(categoryIds);
        }

        record.setIsDeleted(true);
        protocolMapper.updateByPrimaryKeySelective(record);
    }

    private String doAdd(ProtocolVO protocolVO) {
        Protocol protocol = ProtocolConverter.convertToModel(protocolVO);
        protocolMapper.insertSelective(protocol);
        return protocol.getId();
    }

    private String doUpdate(ProtocolVO protocolVO) {
        Protocol originalRecord = selectByPrimaryKey(protocolVO.getId());
        Protocol protocol = ProtocolConverter.convertToModelWithBase(protocolVO, originalRecord);

        ProtocolCategoryExample protocolCategoryExample = new ProtocolCategoryExample();
        protocolCategoryExample.or().andProtocolIdEqualTo(protocolVO.getId()).andIsDeletedEqualTo(false);
        List<ProtocolCategory> protocolCategories = protocolCategoryService.selectByExample(protocolCategoryExample);
        if(protocolCategories.size() > 0) {
            throw new ServiceException("已在協議大类中被使用，不能修改");
        }

        ProtocolExample example = new ProtocolExample();
        example.or().andIdEqualTo(protocolVO.getId()).andLastUpdateVersionEqualTo(protocolVO.getLastUpdateVersion());
        int updateCount = protocolMapper.updateByExample(protocol, example);
        if (updateCount == 0) {
            throw new ServiceException("更新失败，可能是数据已被修改，请刷新后重试");
        }
        return protocol.getId();
    }

    public List<Protocol> selectByExample(ProtocolExample example) {
        return protocolMapper.selectByExample(example);
    }
}
