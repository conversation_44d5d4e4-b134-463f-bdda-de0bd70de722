package com.csci.susdev.service;

import com.csci.susdev.annotation.DS;
import com.csci.susdev.annotation.LogMethod;
import com.csci.susdev.constant.DatasourceContextEnum;
import com.csci.susdev.model.ResultPage;
import com.csci.susdev.util.context.ContextUtils;
import com.csci.susdev.util.context.model.UserInfo;
import com.csci.tzh.mapper.TzhOrgMaterialCarbonFactorGBT51366CustomMapper;
import com.csci.tzh.mapper.TzhOrgMaterialCarbonFactorGBT51366Mapper;
import com.csci.tzh.model.TzhOrgMaterialCarbonFactorGBT51366;
import com.csci.tzh.model.TzhOrgMaterialCarbonFactorGBT51366Example;
import com.csci.tzh.qo.TzhOrgMaterialCarbonFactorGBT51366PageableQO;
import com.github.pagehelper.PageHelper;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.List;
import java.util.UUID;

@Service
@DS(DatasourceContextEnum.TANZHONGHE)
@LogMethod
public class TzhOrgMaterialCarbonFactorGBT51366Service {

	@Autowired
	private TzhOrgMaterialCarbonFactorGBT51366Mapper mapper;

	@Autowired
	private TzhOrgMaterialCarbonFactorGBT51366CustomMapper customMapper;

	public List<TzhOrgMaterialCarbonFactorGBT51366> selectByExample(TzhOrgMaterialCarbonFactorGBT51366Example example) {
		return mapper.selectByExample(example);
	}

	public ResultPage<TzhOrgMaterialCarbonFactorGBT51366> list(TzhOrgMaterialCarbonFactorGBT51366PageableQO qo) {
		PageHelper.startPage(qo.getCurPage(), qo.getPageSize(), "OMCF.ChineseName");

		List<TzhOrgMaterialCarbonFactorGBT51366> lst = customMapper.list(qo.getChineseName());
		ResultPage<TzhOrgMaterialCarbonFactorGBT51366> resultPage = new ResultPage<>(lst, true);

		return resultPage;
	}

	@Transactional(rollbackFor = Exception.class)
	public TzhOrgMaterialCarbonFactorGBT51366 save(TzhOrgMaterialCarbonFactorGBT51366 newModel) {
		UserInfo currentUser = ContextUtils.getCurrentUser();

		if(newModel.getIsdeleted() == false) {
			if(StringUtils.isNotBlank(newModel.getId())) {
				// 備份已刪除數據
				TzhOrgMaterialCarbonFactorGBT51366Example originalExample = new TzhOrgMaterialCarbonFactorGBT51366Example();
				TzhOrgMaterialCarbonFactorGBT51366Example.Criteria originalCriteria = originalExample.or();
				originalCriteria.andIdEqualTo(newModel.getId());
				List<TzhOrgMaterialCarbonFactorGBT51366> lstOriginalModel = mapper.selectByExample(originalExample);
				if(lstOriginalModel.size() > 0) {
					TzhOrgMaterialCarbonFactorGBT51366 originalModel = lstOriginalModel.get(0);
					originalModel.setId(UUID.randomUUID().toString());
					originalModel.setIsdeleted(true);
					originalModel.setDeletedby(currentUser.getUsername());
					originalModel.setDeletedtime(LocalDateTime.now());
					mapper.insertSelective(originalModel);
				}

				// 新增數據(保留舊ID)
				TzhOrgMaterialCarbonFactorGBT51366Example newExample = new TzhOrgMaterialCarbonFactorGBT51366Example();
				TzhOrgMaterialCarbonFactorGBT51366Example.Criteria newCriteria = newExample.or();
				newCriteria.andIdEqualTo(newModel.getId());
				newCriteria.andIsdeletedEqualTo(false);
				newModel.setCreatedby(currentUser.getUsername());
				newModel.setCreatedtime(LocalDateTime.now());
				newModel.setDeletedby(null);
				newModel.setDeletedtime(null);
				mapper.deleteByExample(newExample);
				mapper.insertSelective(newModel);
			} else {
				// 新增數據(新建ID)
				newModel.setId(UUID.randomUUID().toString());
				newModel.setCreatedby(currentUser.getUsername());
				newModel.setCreatedtime(LocalDateTime.now());
				mapper.insertSelective(newModel);
			}
		} else {
			// 刪除數據
			TzhOrgMaterialCarbonFactorGBT51366Example example = new TzhOrgMaterialCarbonFactorGBT51366Example();
			TzhOrgMaterialCarbonFactorGBT51366Example.Criteria criteria = example.or();
			criteria.andIdEqualTo(newModel.getId());
			newModel.setIsdeleted(true);
			newModel.setDeletedby(currentUser.getUsername());
			newModel.setDeletedtime(LocalDateTime.now());
			mapper.updateByExampleSelective(newModel, example);
		}

		return newModel;
	}
}
