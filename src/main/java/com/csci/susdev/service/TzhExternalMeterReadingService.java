package com.csci.susdev.service;

import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import com.csci.common.exception.ServiceException;
import com.csci.susdev.annotation.DS;
import com.csci.susdev.annotation.LogMethod;
import com.csci.susdev.constant.DatasourceContextEnum;
import com.csci.susdev.qo.TzhExternalMeterReadingQO;
import com.csci.tzh.mapper.TzhExternalAccessMapper;
import com.csci.tzh.mapper.TzhExternalMeterInfoMapper;
import com.csci.tzh.mapper.TzhExternalMeterReadingMapper;
import com.csci.tzh.model.*;
import io.netty.util.internal.StringUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.RoundingMode;
import java.time.LocalDateTime;
import java.util.List;

@Service
@DS(DatasourceContextEnum.TANZHONGHE)
@LogMethod
public class TzhExternalMeterReadingService {
	@Autowired
	private TzhExternalMeterReadingMapper mapper;
	@Autowired
	private TzhExternalMeterInfoMapper emiMapper;
	@Autowired
	private TzhExternalAccessMapper eaMapper;

	@Transactional(rollbackFor = Exception.class)
	public TzhExternalMeterReading save(TzhExternalMeterReadingQO qo) {
		TzhExternalAccess access = getExternalAccess(qo.getAccessid());
		if(access == null || !checkValidNumber1(qo) || !checkValidNumber2(qo)) {
			throw new ServiceException("沒有訪問權限。");
		}

		TzhExternalMeterInfoExample emiExample = new TzhExternalMeterInfoExample();
		TzhExternalMeterInfoExample.Criteria emiCriteria = emiExample.or();
		emiCriteria.andMeternoEqualTo(qo.getMeterno());
		emiCriteria.andSitenameEqualTo(access.getSitename());
		if(!StringUtil.isNullOrEmpty(qo.getMaterialname())) {
			emiCriteria.andMaterialnameEqualTo(qo.getMaterialname());
		}
		List<TzhExternalMeterInfo> lstMeterInfo = emiMapper.selectByExample(emiExample);
		TzhExternalMeterInfo meterInfo = lstMeterInfo.size() > 0 ? lstMeterInfo.get(0) : null;
		if(meterInfo == null) {
			throw new ServiceException("找不到物聯網裝置資料");
		}

		TzhExternalMeterReadingExample emrExample = new TzhExternalMeterReadingExample();
		TzhExternalMeterReadingExample.Criteria emrCriteria = emrExample.or();
		emrCriteria.andMeternoEqualTo(qo.getMeterno());
		emrCriteria.andRecorddateEqualTo(qo.getRecorddate());
		emrCriteria.andSitenameEqualTo(access.getSitename());

		TzhExternalMeterReading model = new TzhExternalMeterReading();
		model.setId(java.util.UUID.randomUUID().toString());
		model.setMeterno(qo.getMeterno());
		model.setMeterreading(qo.getMeterreading());
		model.setRegion(meterInfo.getRegion());
		model.setSitename(meterInfo.getSitename());
		model.setMaterialname(meterInfo.getMaterialname());
		model.setCarbonemissionlocation(meterInfo.getCarbonemissionlocation());
		model.setRecorddate(qo.getRecorddate());
		model.setCreatedby(qo.getAccessid());
		model.setCreatedtime(LocalDateTime.now());
		if(mapper.countByExample(emrExample) == 0) {
			mapper.insertSelective(model);
		}

		return model;
	}

	private TzhExternalAccess getExternalAccess(String id) {
		TzhExternalAccessExample example = new TzhExternalAccessExample();
		TzhExternalAccessExample.Criteria criteria = example.or();
		criteria.andIdEqualTo(id);
		criteria.andFunctionnameEqualTo("/api/tzh/external/meter-reading/save");
		criteria.andIsdeletedEqualTo(false);
		List<TzhExternalAccess> lst = eaMapper.selectByExample(example);
		return lst.size() > 0 ? lst.get(0) : null;
	}
	private boolean checkValidNumber1(TzhExternalMeterReadingQO qo) {
		DateTimeFormatter dtf = DateTimeFormatter.ofPattern("yyyy-MM-dd");
		int meterReading = qo.getMeterreading().setScale(0, RoundingMode.DOWN).intValue();
		int date = Integer.valueOf(qo.getRecorddate().format(dtf).replace("-", ""));
		return (meterReading + date + 31) % 53 == Integer.valueOf(qo.getVn1());
	}

	private boolean checkValidNumber2(TzhExternalMeterReadingQO qo) {
		DateTimeFormatter dtf = DateTimeFormatter.ofPattern("yyyy-MM-dd");
		int meterReading = qo.getMeterreading().setScale(0, RoundingMode.DOWN).intValue();
		int date = Integer.valueOf(qo.getRecorddate().format(dtf).replace("-", ""));
		return (meterReading + date + 3311) % 77 == Integer.valueOf(qo.getVn2());
	}

}
