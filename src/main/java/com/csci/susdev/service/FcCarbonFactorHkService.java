package com.csci.susdev.service;

import com.csci.common.exception.ServiceException;
import com.csci.susdev.annotation.LogMethod;
import com.csci.susdev.mapper.FcCarbonFactorHkMapper;
import com.csci.susdev.model.*;
import com.csci.susdev.modelcovt.FcCarbonFactorHkConverter;
import com.csci.susdev.modelcovt.FcEnergyFactorEsgDatasetConverter;
import com.csci.susdev.qo.FcCarbonFactorHkQO;
import com.csci.susdev.util.NamingConventionUtils;
import com.csci.susdev.util.SimpTradUtil;
import com.csci.susdev.vo.FcCarbonFactorHkVO;
import com.github.pagehelper.PageHelper;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.List;
import java.util.stream.Collectors;

import static com.csci.susdev.service.ServiceHelper.checkExist;

@Service
@LogMethod
public class FcCarbonFactorHkService {

    @Resource
    private FcCarbonFactorHkMapper fcCarbonFactorHkMapper;


    @Transactional(rollbackFor = Exception.class)
    public int insertSelective(FcCarbonFactorHk record) {
        return fcCarbonFactorHkMapper.insertSelective(record);
    }

    public FcCarbonFactorHk selectByPrimaryKey(String id) {
        return fcCarbonFactorHkMapper.selectByPrimaryKey(id);
    }

    public FcCarbonFactorHkVO getFcCarbonFactorHk(String id) {
        checkExist(id, "id不能为空");
        FcCarbonFactorHk fcCarbonFactorHk = selectByPrimaryKey(id);
        checkExist(fcCarbonFactorHk, "未找到对应的记录");
        if(fcCarbonFactorHk.getIsDeleted()) {
            throw new ServiceException("紀錄已被刪除");
        }
        return FcCarbonFactorHkConverter.convertToVO(fcCarbonFactorHk);
    }


    @Transactional(rollbackFor = Exception.class)
    public String saveFcCarbonFactorHk(FcCarbonFactorHkVO fcCarbonFactorHkVO) {
        if (StringUtils.isBlank(fcCarbonFactorHkVO.getId())) {
            // 新增
            return doAdd(fcCarbonFactorHkVO);
        } else {
            checkExist(fcCarbonFactorHkVO.getLastUpdateVersion(), "更新时版本号不能为空");
            // 修改
            return doUpdate(fcCarbonFactorHkVO);
        }

    }

    public ResultPage<FcCarbonFactorHkVO> listFcCarbonFactorHk(FcCarbonFactorHkQO fcCarbonFactorHkQO) {
        FcCarbonFactorHkExample example = new FcCarbonFactorHkExample();

        SimpTradUtil simpTradUtil = new SimpTradUtil();
        String chineseNameTc = simpTradUtil.convert2Trad(fcCarbonFactorHkQO.getChineseName());
        String chineseNameSc = simpTradUtil.convert2Simp(fcCarbonFactorHkQO.getChineseName());

        if(StringUtils.isNotBlank(fcCarbonFactorHkQO.getChineseName())) {
            example.or().andChineseNameLike("%" + chineseNameTc + "%").andIsDeletedEqualTo(false);
            example.or().andChineseNameLike("%" + chineseNameSc + "%").andIsDeletedEqualTo(false);
        } else {
            example.or().andIsDeletedEqualTo(false);
        }

        if (StringUtils.isNotBlank(fcCarbonFactorHkQO.getOrderBy())) {
            example.setOrderByClause(NamingConventionUtils.snakeCaseToCamelCase(fcCarbonFactorHkQO.getOrderBy()));
        }

        PageHelper.startPage(fcCarbonFactorHkQO.getCurPage(), fcCarbonFactorHkQO.getPageSize());
        List<FcCarbonFactorHk> fcCarbonFactorHks = fcCarbonFactorHkMapper.selectByExample(example);
        return new ResultPage<>(fcCarbonFactorHks, fcCarbonFactorHks.stream().map(FcCarbonFactorHkConverter::convertToVO).collect(Collectors.toList()));
    }

    @Transactional(rollbackFor = Exception.class)
    public void deleteFcCarbonFactorHk(String id) {
        FcCarbonFactorHk record = selectByPrimaryKey(id);
        record.setIsDeleted(true);
        fcCarbonFactorHkMapper.updateByPrimaryKeySelective(record);
    }

    private String doAdd(FcCarbonFactorHkVO fcCarbonFactorHkVO) {
        FcCarbonFactorHk fcCarbonFactorHk = FcCarbonFactorHkConverter.convertToModel(fcCarbonFactorHkVO);
        fcCarbonFactorHkMapper.insertSelective(fcCarbonFactorHk);
        return fcCarbonFactorHk.getId();
    }

    private String doUpdate(FcCarbonFactorHkVO fcCarbonFactorHkVO) {
        FcCarbonFactorHk originalRecord = selectByPrimaryKey(fcCarbonFactorHkVO.getId());
        FcCarbonFactorHk fcCarbonFactorHk = FcCarbonFactorHkConverter.convertToModelWithBase(fcCarbonFactorHkVO, originalRecord);

        FcCarbonFactorHkExample example = new FcCarbonFactorHkExample();
        example.or().andIdEqualTo(fcCarbonFactorHkVO.getId()).andLastUpdateVersionEqualTo(fcCarbonFactorHkVO.getLastUpdateVersion());
        int updateCount = fcCarbonFactorHkMapper.updateByExample(fcCarbonFactorHk, example);
        if (updateCount == 0) {
            throw new ServiceException("更新失败，可能是数据已被修改，请刷新后重试");
        }
        return fcCarbonFactorHk.getId();
    }

    public List<FcCarbonFactorHk> selectByExample(FcCarbonFactorHkExample example) {
        return fcCarbonFactorHkMapper.selectByExample(example);
    }
}
