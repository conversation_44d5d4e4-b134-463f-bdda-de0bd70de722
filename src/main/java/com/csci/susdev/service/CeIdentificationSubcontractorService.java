package com.csci.susdev.service;

import com.csci.common.exception.ServiceException;
import com.csci.susdev.annotation.LogMethod;
import com.csci.susdev.mapper.CeIdentificationSubcontractorMapper;
import com.csci.susdev.model.ResultPage;
import com.csci.susdev.model.CeIdentificationSubcontractor;
import com.csci.susdev.model.CeIdentificationSubcontractorExample;
import com.csci.susdev.modelcovt.CeIdentificationSubcontractorConverter;
import com.csci.susdev.qo.CeIdentificationSubcontractorQO;
import com.csci.susdev.util.SpringContextUtil;
import com.csci.susdev.vo.CeIdentificationSubcontractorVO;
import com.github.pagehelper.PageHelper;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;
import java.util.stream.Collectors;

import static com.csci.susdev.service.ServiceHelper.checkExist;
import static com.csci.susdev.service.ServiceHelper.validateMonth;

@Service
@LogMethod
public class CeIdentificationSubcontractorService {

    @Resource
    private CeIdentificationSubcontractorMapper ceIdentificationSubcontractorMapper;

    /**
     * 保存分判商记录
     *
     * @param ceIdentificationSubcontractorVO
     * @return
     */
    public String saveCeIdentificationSubcontractor(CeIdentificationSubcontractorVO ceIdentificationSubcontractorVO) {

        /*
        WorkflowControlService workflowControlService = SpringContextUtil.getBean(WorkflowControlService.class);
        workflowControlService.throwIfHasWorkflowControl(ceIdentificationSubcontractorVO.getAmbientHeadId(), "本期分判商数据已经提交，不能进行新增或修改");
        */

        if (StringUtils.isBlank(ceIdentificationSubcontractorVO.getId())) {
            // 新增
            return doAdd(ceIdentificationSubcontractorVO);
        } else {
            checkExist(ceIdentificationSubcontractorVO.getLastUpdateVersion(), "更新时版本号不能为空");
            // 修改
            return doUpdate(ceIdentificationSubcontractorVO);
        }
    }

    /**
     * 复制指定的分判商记录
     *
     * @param id
     * @return
     */
    public CeIdentificationSubcontractor duplicateCeIdentificationSubcontractor(String id) {
        CeIdentificationSubcontractor ceIdentificationSubcontractor = ceIdentificationSubcontractorMapper.selectByPrimaryKey(id);
        checkExist(ceIdentificationSubcontractor, "未找到对应的分判商记录");

        /*
        WorkflowControlService workflowControlService = SpringContextUtil.getBean(WorkflowControlService.class);
        workflowControlService.throwIfHasWorkflowControl(ceIdentificationSubcontractor.getCeIdentificationHeadId(), "本期分判商数据已经提交，不能进行复制操作");
        */

        ceIdentificationSubcontractor.setId(null);
        ceIdentificationSubcontractor.setCreationTime(null);
        ceIdentificationSubcontractor.setCreateUserId(null);
        ceIdentificationSubcontractor.setCreateUsername(null);
        ceIdentificationSubcontractor.setLastUpdateVersion(0);
        ceIdentificationSubcontractor.setLastUpdateTime(null);
        ceIdentificationSubcontractor.setLastUpdateUserId(null);
        ceIdentificationSubcontractor.setLastUpdateUsername(null);
        ceIdentificationSubcontractorMapper.insert(ceIdentificationSubcontractor);
        return ceIdentificationSubcontractor;
    }

    /**
     * 查询分判商记录
     *
     * @return
     */
    public ResultPage<CeIdentificationSubcontractorVO> listCeIdentificationSubcontractor(CeIdentificationSubcontractorQO ceIdentificationSubcontractorQO) {
        checkExist(ceIdentificationSubcontractorQO.getCeIdentificationHeadId(), "分判商主表记录ID不能为空");
        CeIdentificationSubcontractorExample example = new CeIdentificationSubcontractorExample();
        example.or().andCeIdentificationHeadIdEqualTo(ceIdentificationSubcontractorQO.getCeIdentificationHeadId());

        if (StringUtils.isBlank(ceIdentificationSubcontractorQO.getSortName())) {
            example.setOrderByClause("creation_time desc");
        } else {
            String columnName = getColumnName(ceIdentificationSubcontractorQO.getSortName());
            if (StringUtils.isBlank(columnName)) {
                throw new ServiceException("排序字段不正确");
            }
            if (StringUtils.equals("asc", ceIdentificationSubcontractorQO.getSortOrder())) {
                example.setOrderByClause(columnName + " asc");
            } else {
                example.setOrderByClause(columnName + " desc");
            }
        }

        PageHelper.startPage(ceIdentificationSubcontractorQO.getCurPage(), ceIdentificationSubcontractorQO.getPageSize());
        List<CeIdentificationSubcontractor> ceIdentificationSubcontractors = ceIdentificationSubcontractorMapper.selectByExample(example);
        return new ResultPage<>(ceIdentificationSubcontractors, ceIdentificationSubcontractors.stream().map(CeIdentificationSubcontractorConverter::convert).collect(Collectors.toList()));
    }

    private String getColumnName(String sortName) {
        if (StringUtils.isBlank(sortName)) {
            return null;
        }
        switch (sortName) {
            case "type":
                return "type";
            case "name":
                return "name";
            case "duty":
                return "duty";
            case "dutyType":
                return "duty_type";
            case "ssFossilFuel":
                return "ss_fossil_fuel";
            case "ms_fossil_fuel_qty":
                return "msFossilFuelQty";
            case "ms_fossil_fuel_ton_km":
                return "msFossilFuelTonKm";
            case "ms_fossil_fuel_machine_team":
                return "msFossilFuelMachineTeam";
            case "process_emission":
                return "processEmission";
            case "electricity":
                return "electricity";
            case "outsourced_building_material":
                return "outsourcedBuildingMaterial";
            default:
                return null;
        }
    }

    private String doAdd(CeIdentificationSubcontractorVO ceIdentificationSubcontractorVO) {
        CeIdentificationSubcontractor ceIdentificationSubcontractor = CeIdentificationSubcontractorConverter.convert(ceIdentificationSubcontractorVO);
        ceIdentificationSubcontractorMapper.insertSelective(ceIdentificationSubcontractor);
        return ceIdentificationSubcontractor.getId();
    }

    private String doUpdate(CeIdentificationSubcontractorVO ceIdentificationSubcontractorVO) {
        CeIdentificationSubcontractor ceIdentificationSubcontractor = CeIdentificationSubcontractorConverter.convert(ceIdentificationSubcontractorVO);
        CeIdentificationSubcontractorExample example = new CeIdentificationSubcontractorExample();
        example.or().andIdEqualTo(ceIdentificationSubcontractorVO.getId()).andLastUpdateVersionEqualTo(ceIdentificationSubcontractorVO.getLastUpdateVersion());
        int updateCount = ceIdentificationSubcontractorMapper.updateByExampleSelective(ceIdentificationSubcontractor, example);
        if (updateCount == 0) {
            throw new ServiceException("更新失败，可能是数据已被修改，请刷新后重试");
        }
        return ceIdentificationSubcontractor.getId();
    }

    public CeIdentificationSubcontractorVO getCeIdentificationSubcontractorById(String id) {
        CeIdentificationSubcontractor ceIdentificationSubcontractor = ceIdentificationSubcontractorMapper.selectByPrimaryKey(id);
        if (ceIdentificationSubcontractor == null) {
            throw new ServiceException("数据不存在");
        }
        return CeIdentificationSubcontractorConverter.convert(ceIdentificationSubcontractor);
    }

    public void deleteCeIdentificationSubcontractorById(String id) {
        checkExist(id, "id不能为空");
        ceIdentificationSubcontractorMapper.deleteByPrimaryKey(id);
    }

    public List<CeIdentificationSubcontractor> selectByExample(CeIdentificationSubcontractorExample example) {
        return ceIdentificationSubcontractorMapper.selectByExample(example);
    }

    // 根据组织机构id及年份查询分判商记录
    public List<CeIdentificationSubcontractor> listCeIdentificationSubcontractorByOrgIdAndYear(String orgId, Integer year) {
        // first, query head record by org id and year
        /*AmbientHeadExample headExample = new AmbientHeadExample();
        headExample.or().and

        CeIdentificationSubcontractorExample example = new CeIdentificationSubcontractorExample();
        example.or().andOrgIdEqualTo(orgId).andYearValueEqualTo(year);
        return ceIdentificationSubcontractorMapper.selectByExample(example);*/
        throw new ServiceException("未实现");
    }
}
