package com.csci.susdev.validation;

import javax.validation.Constraint;
import javax.validation.Payload;
import java.lang.annotation.*;

/**
 * Custom validation annotation for file uploads
 * 
 * <AUTHOR>
 * @date 2025-06-20
 */
@Target({ElementType.PARAMETER, ElementType.FIELD})
@Retention(RetentionPolicy.RUNTIME)
@Constraint(validatedBy = FileValidator.class)
@Documented
public @interface FileConstraints {
    
    String message() default "Invalid file";
    
    Class<?>[] groups() default {};
    
    Class<? extends Payload>[] payload() default {};
    
    /**
     * Maximum file size in bytes
     */
    long maxSize() default 25 * 1024 * 1024; // 25MB
    
    /**
     * Allowed file types
     */
    String[] allowedTypes() default {"jpg", "jpeg", "png", "pdf"};
    
    /**
     * Whether empty files are allowed
     */
    boolean allowEmpty() default false;
}
