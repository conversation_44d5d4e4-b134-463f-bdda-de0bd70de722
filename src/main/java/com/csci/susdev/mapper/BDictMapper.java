package com.csci.susdev.mapper;

import com.csci.susdev.model.BDict;
import com.csci.susdev.model.BDictExample;
import java.util.List;
import org.apache.ibatis.annotations.Param;

public interface BDictMapper {
    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table t_b_dict
     *
     * @mbg.generated Mon Mar 31 10:30:41 CST 2025
     */
    long countByExample(BDictExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table t_b_dict
     *
     * @mbg.generated Mon Mar 31 10:30:41 CST 2025
     */
    int deleteByExample(BDictExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table t_b_dict
     *
     * @mbg.generated Mon Mar 31 10:30:41 CST 2025
     */
    int deleteByPrimaryKey(String id);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table t_b_dict
     *
     * @mbg.generated Mon Mar 31 10:30:41 CST 2025
     */
    int insert(BDict record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table t_b_dict
     *
     * @mbg.generated Mon Mar 31 10:30:41 CST 2025
     */
    int insertSelective(BDict record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table t_b_dict
     *
     * @mbg.generated Mon Mar 31 10:30:41 CST 2025
     */
    List<BDict> selectByExample(BDictExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table t_b_dict
     *
     * @mbg.generated Mon Mar 31 10:30:41 CST 2025
     */
    BDict selectByPrimaryKey(String id);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table t_b_dict
     *
     * @mbg.generated Mon Mar 31 10:30:41 CST 2025
     */
    int updateByExampleSelective(@Param("record") BDict record, @Param("example") BDictExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table t_b_dict
     *
     * @mbg.generated Mon Mar 31 10:30:41 CST 2025
     */
    int updateByExample(@Param("record") BDict record, @Param("example") BDictExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table t_b_dict
     *
     * @mbg.generated Mon Mar 31 10:30:41 CST 2025
     */
    int updateByPrimaryKeySelective(BDict record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table t_b_dict
     *
     * @mbg.generated Mon Mar 31 10:30:41 CST 2025
     */
    int updateByPrimaryKey(BDict record);
}