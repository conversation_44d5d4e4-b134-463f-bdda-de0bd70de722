package com.csci.susdev.mapper;

import com.csci.susdev.model.MinioAttachment;
import com.csci.susdev.model.MinioAttachmentExample;
import java.util.List;
import org.apache.ibatis.annotations.Param;

public interface MinioAttachmentMapper {
    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table t_minio_attachment
     *
     * @mbg.generated Wed Dec 18 15:54:10 CST 2024
     */
    long countByExample(MinioAttachmentExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table t_minio_attachment
     *
     * @mbg.generated Wed Dec 18 15:54:10 CST 2024
     */
    int deleteByExample(MinioAttachmentExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table t_minio_attachment
     *
     * @mbg.generated Wed Dec 18 15:54:10 CST 2024
     */
    int insert(MinioAttachment record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table t_minio_attachment
     *
     * @mbg.generated Wed Dec 18 15:54:10 CST 2024
     */
    int insertSelective(MinioAttachment record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table t_minio_attachment
     *
     * @mbg.generated Wed Dec 18 15:54:10 CST 2024
     */
    List<MinioAttachment> selectByExample(MinioAttachmentExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table t_minio_attachment
     *
     * @mbg.generated Wed Dec 18 15:54:10 CST 2024
     */
    int updateByExampleSelective(@Param("record") MinioAttachment record, @Param("example") MinioAttachmentExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table t_minio_attachment
     *
     * @mbg.generated Wed Dec 18 15:54:10 CST 2024
     */
    int updateByExample(@Param("record") MinioAttachment record, @Param("example") MinioAttachmentExample example);
}