package com.csci.susdev.mapper;

import com.csci.susdev.model.FactorSelection;
import com.csci.susdev.qo.FactorSelectionQO;
import com.csci.susdev.vo.FactorSelectionVO;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;

public interface FactorSelectionCustomMapper {

    @Select("""
            <script>
                select * from (
                    select fsl.id,
                    fsl.factor_scope_id as factorScopeId,
                    fsl.fc_carbon_factor_type as fcCarbonFactorType,
                    fsl.fc_carbon_factor_datasource as fcCarbonFactorDatasource,
                    fsl.fc_carbon_factor_id as fcCarbonFactorId,
                    fsl.organization_id as organizationId,
                    fsl.is_active as isActive,
                    fsl.last_update_version as lastUpdateVersion
                    from t_factor_selection fsl
                    left join t_factor_scope fs on fsl.factor_scope_id = fs.id and fs.is_deleted = 0
                    left join t_protocol_detail pd on fs.protocol_detail_id = pd.id and pd.is_deleted = 0
                    left join t_carbon_emission_location cel on pd.carbon_emission_location_id = cel.id and cel.is_deleted = 0
                    left join t_protocol_sub_category psc on pd.sub_category_id = psc.id and psc.is_deleted = 0
                    left join t_protocol_category pc on psc.category_id = pc.id and pc.is_deleted = 0
                    left join t_protocol p on pc.protocol_id = p.id and p.is_deleted = 0
                    left join t_form_detail fd on fs.form_detail_id = fd.id and fd.is_deleted = 0
                    left join t_fc_air_pollution_factor_esg_dataset fc on fsl.fc_carbon_factor_id = fc.id and fc.is_deleted = 0
                    left join t_organization o on fsl.organization_id = o.id and o.is_deleted = 0
                    where fsl.is_deleted = 0
                    
                    <if test="organizationId != null and organizationId != ''">
                    and o.id = #{organizationId}
                    </if>
                    
                    <if test="protocolId != null and protocolId != ''">
                    and p.id = #{protocolId}
                    </if>
                    
                    <if test="carbonEmissionLocationId != null and carbonEmissionLocationId != ''">
                    and cel.id = #{carbonEmissionLocationId}
                    </if>
                    
                    <if test="year != null and year != ''">
                    and fd.year = #{year}
                    </if>
                ) t
                
                <if test="orderBy != null and orderBy != ''">
                order by ${orderBy}
                </if>
            </script>
                """)
    public List<FactorSelection> listModel(
            @Param("organizationId") String organizationId,
            @Param("protocolId") String protocolId,
            @Param("carbonEmissionLocationId") String carbonEmissionLocationId,
            @Param("year") Integer year,
            @Param("orderBy") String orderBy);


    //取得因子數據，如沒有則繼承上級，繼承按最近的層級為優先
    public List<FactorSelectionVO> listVOInherited(FactorSelectionQO factorSelectionQO);

    /*
    @Select("""
            <script>
                select * from (
                    select fsl.id,
                    fsl.factor_scope_id as factorScopeId,
                    fsl.fc_carbon_factor_type as fcCarbonFactorType,
                    fsl.fc_carbon_factor_datasource as fcCarbonFactorDatasource,
                    fsl.fc_carbon_factor_id as fcCarbonFactorId,
                    fsl.organization_id as organizationId,
                    fsl.inherited_from as inheritedFrom,
                    fsl.inherited_from_id as inheritedFromId,
                    fsl.is_active as isActive,
                    fsl.last_update_version as lastUpdateVersionZ
                    from v_factor_selection_inherited fsl
                    left join t_factor_scope fs on fsl.factor_scope_id = fs.id and fs.is_deleted = 0
                    left join t_protocol_detail pd on fs.protocol_detail_id = pd.id and pd.is_deleted = 0
                    left join t_carbon_emission_location cel on pd.carbon_emission_location_id = cel.id and cel.is_deleted = 0
                    left join t_protocol_sub_category psc on pd.sub_category_id = psc.id and psc.is_deleted = 0
                    left join t_protocol_category pc on psc.category_id = pc.id and pc.is_deleted = 0
                    left join t_protocol p on pc.protocol_id = p.id and p.is_deleted = 0
                    left join t_form_detail fd on fs.form_detail_id = fd.id and fd.is_deleted = 0
                    left join t_fc_air_pollution_factor_esg_dataset fc on fsl.fc_carbon_factor_id = fc.id and fc.is_deleted = 0
                    left join t_organization o on fsl.organization_id = o.id and o.is_deleted = 0
                    where 1 = 1
                    
                    <if test="organizationId != null and organizationId != ''">
                    and o.id = #{organizationId}
                    </if>
                    
                    <if test="protocolId != null and protocolId != ''">
                    and p.id = #{protocolId}
                    </if>
                    
                    <if test="carbonEmissionLocationId != null and carbonEmissionLocationId != ''">
                    and cel.id = #{carbonEmissionLocationId}
                    </if>
                    
                    <if test="year != null and year != ''">
                    and fd.year = #{year}
                    </if>
                    <if test="formCode != null and formCode != ''">
                    and fd.form_code = #{formCode}
                    </if>               
                ) t
                
                <if test="orderBy != null and orderBy != ''">
                order by ${orderBy}
                </if>
            </script>
                """)
    public List<FactorSelectionVO> listVOInherited(
            @Param("organizationId") String organizationId,
            @Param("protocolId") String protocolId,
            @Param("carbonEmissionLocationId") String carbonEmissionLocationId,
            @Param("year") Integer year,
            @Param("orderBy") String orderBy,
            @Param("formCode") String formCode);
     */
}
