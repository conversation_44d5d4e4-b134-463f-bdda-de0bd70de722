package com.csci.susdev.mapper;

import com.csci.susdev.model.FcCarbonFactorHk;
import com.csci.susdev.model.FcCarbonFactorHkExample;
import java.util.List;
import org.apache.ibatis.annotations.Param;

public interface FcCarbonFactorHkMapper {
    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table t_fc_carbon_factor_hk
     *
     * @mbg.generated Wed Feb 05 09:32:45 CST 2025
     */
    long countByExample(FcCarbonFactorHkExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table t_fc_carbon_factor_hk
     *
     * @mbg.generated Wed Feb 05 09:32:45 CST 2025
     */
    int deleteByExample(FcCarbonFactorHkExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table t_fc_carbon_factor_hk
     *
     * @mbg.generated Wed Feb 05 09:32:45 CST 2025
     */
    int deleteByPrimaryKey(String id);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table t_fc_carbon_factor_hk
     *
     * @mbg.generated Wed Feb 05 09:32:45 CST 2025
     */
    int insert(FcCarbonFactorHk record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table t_fc_carbon_factor_hk
     *
     * @mbg.generated Wed Feb 05 09:32:45 CST 2025
     */
    int insertSelective(FcCarbonFactorHk record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table t_fc_carbon_factor_hk
     *
     * @mbg.generated Wed Feb 05 09:32:45 CST 2025
     */
    List<FcCarbonFactorHk> selectByExample(FcCarbonFactorHkExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table t_fc_carbon_factor_hk
     *
     * @mbg.generated Wed Feb 05 09:32:45 CST 2025
     */
    FcCarbonFactorHk selectByPrimaryKey(String id);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table t_fc_carbon_factor_hk
     *
     * @mbg.generated Wed Feb 05 09:32:45 CST 2025
     */
    int updateByExampleSelective(@Param("record") FcCarbonFactorHk record, @Param("example") FcCarbonFactorHkExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table t_fc_carbon_factor_hk
     *
     * @mbg.generated Wed Feb 05 09:32:45 CST 2025
     */
    int updateByExample(@Param("record") FcCarbonFactorHk record, @Param("example") FcCarbonFactorHkExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table t_fc_carbon_factor_hk
     *
     * @mbg.generated Wed Feb 05 09:32:45 CST 2025
     */
    int updateByPrimaryKeySelective(FcCarbonFactorHk record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table t_fc_carbon_factor_hk
     *
     * @mbg.generated Wed Feb 05 09:32:45 CST 2025
     */
    int updateByPrimaryKey(FcCarbonFactorHk record);
}