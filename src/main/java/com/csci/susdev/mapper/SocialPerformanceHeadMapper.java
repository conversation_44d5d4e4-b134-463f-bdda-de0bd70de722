package com.csci.susdev.mapper;

import com.csci.susdev.model.SocialPerformanceHead;
import com.csci.susdev.model.SocialPerformanceHeadExample;
import java.util.List;
import org.apache.ibatis.annotations.Param;

public interface SocialPerformanceHeadMapper {
    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table t_social_performance_head
     *
     * @mbg.generated
     */
    long countByExample(SocialPerformanceHeadExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table t_social_performance_head
     *
     * @mbg.generated
     */
    int deleteByExample(SocialPerformanceHeadExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table t_social_performance_head
     *
     * @mbg.generated
     */
    int deleteByPrimaryKey(String id);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table t_social_performance_head
     *
     * @mbg.generated
     */
    int insert(SocialPerformanceHead record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table t_social_performance_head
     *
     * @mbg.generated
     */
    int insertSelective(SocialPerformanceHead record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table t_social_performance_head
     *
     * @mbg.generated
     */
    List<SocialPerformanceHead> selectByExample(SocialPerformanceHeadExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table t_social_performance_head
     *
     * @mbg.generated
     */
    SocialPerformanceHead selectByPrimaryKey(String id);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table t_social_performance_head
     *
     * @mbg.generated
     */
    int updateByExampleSelective(@Param("record") SocialPerformanceHead record, @Param("example") SocialPerformanceHeadExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table t_social_performance_head
     *
     * @mbg.generated
     */
    int updateByExample(@Param("record") SocialPerformanceHead record, @Param("example") SocialPerformanceHeadExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table t_social_performance_head
     *
     * @mbg.generated
     */
    int updateByPrimaryKeySelective(SocialPerformanceHead record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table t_social_performance_head
     *
     * @mbg.generated
     */
    int updateByPrimaryKey(SocialPerformanceHead record);
}