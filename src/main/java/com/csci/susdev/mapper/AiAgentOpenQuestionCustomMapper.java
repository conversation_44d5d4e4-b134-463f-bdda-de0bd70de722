package com.csci.susdev.mapper;

import com.csci.susdev.model.AmbientDetail;
import com.csci.susdev.vo.AiAgentOpenQuestionVO;
import com.csci.susdev.vo.AmbientDetailVO;
import com.csci.susdev.vo.AmbientSubOrgsDataVO;
import org.apache.ibatis.annotations.Insert;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;

@Mapper
public interface AiAgentOpenQuestionCustomMapper {


    @Select("""
        select
            id,
            question,
            seq
        from t_ai_agent_open_question
        where is_deleted = 0
        order by seq asc
    """)
    List<AiAgentOpenQuestionVO> list();
}
