package com.csci.susdev.mapper;

import com.csci.susdev.model.FcCarbonFactorGbt51366;
import com.csci.susdev.model.FcCarbonFactorGbt51366Example;
import java.util.List;
import org.apache.ibatis.annotations.Param;

public interface FcCarbonFactorGbt51366Mapper {
    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table t_fc_carbon_factor_gbt51366
     *
     * @mbg.generated Wed Feb 05 09:42:38 CST 2025
     */
    long countByExample(FcCarbonFactorGbt51366Example example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table t_fc_carbon_factor_gbt51366
     *
     * @mbg.generated Wed Feb 05 09:42:38 CST 2025
     */
    int deleteByExample(FcCarbonFactorGbt51366Example example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table t_fc_carbon_factor_gbt51366
     *
     * @mbg.generated Wed Feb 05 09:42:38 CST 2025
     */
    int deleteByPrimaryKey(String id);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table t_fc_carbon_factor_gbt51366
     *
     * @mbg.generated Wed Feb 05 09:42:38 CST 2025
     */
    int insert(FcCarbonFactorGbt51366 record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table t_fc_carbon_factor_gbt51366
     *
     * @mbg.generated Wed Feb 05 09:42:38 CST 2025
     */
    int insertSelective(FcCarbonFactorGbt51366 record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table t_fc_carbon_factor_gbt51366
     *
     * @mbg.generated Wed Feb 05 09:42:38 CST 2025
     */
    List<FcCarbonFactorGbt51366> selectByExample(FcCarbonFactorGbt51366Example example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table t_fc_carbon_factor_gbt51366
     *
     * @mbg.generated Wed Feb 05 09:42:38 CST 2025
     */
    FcCarbonFactorGbt51366 selectByPrimaryKey(String id);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table t_fc_carbon_factor_gbt51366
     *
     * @mbg.generated Wed Feb 05 09:42:38 CST 2025
     */
    int updateByExampleSelective(@Param("record") FcCarbonFactorGbt51366 record, @Param("example") FcCarbonFactorGbt51366Example example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table t_fc_carbon_factor_gbt51366
     *
     * @mbg.generated Wed Feb 05 09:42:38 CST 2025
     */
    int updateByExample(@Param("record") FcCarbonFactorGbt51366 record, @Param("example") FcCarbonFactorGbt51366Example example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table t_fc_carbon_factor_gbt51366
     *
     * @mbg.generated Wed Feb 05 09:42:38 CST 2025
     */
    int updateByPrimaryKeySelective(FcCarbonFactorGbt51366 record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table t_fc_carbon_factor_gbt51366
     *
     * @mbg.generated Wed Feb 05 09:42:38 CST 2025
     */
    int updateByPrimaryKey(FcCarbonFactorGbt51366 record);
}