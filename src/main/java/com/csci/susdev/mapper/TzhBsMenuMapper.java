package com.csci.susdev.mapper;

import com.csci.susdev.model.TzhBsMenu;
import com.csci.susdev.qo.TzhBsMenuQO;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface TzhBsMenuMapper {
    int deleteByPrimaryKey(Object id);

    int insert(TzhBsMenu record);

    int insertSelective(TzhBsMenu record);

    TzhBsMenu selectByPrimaryKey(Object id);

    int updateByPrimaryKeySelective(TzhBsMenu record);

    int updateByPrimaryKey(TzhBsMenu record);


    List<TzhBsMenu> selectByNameLike(String title);

    int selectByTitleEn(@Param("titleEn") String titleEn);

    List<TzhBsMenu> queryAll(TzhBsMenuQO menuQO);

    int logicallyDelete(String id);
}
