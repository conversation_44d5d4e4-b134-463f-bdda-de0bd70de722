package com.csci.susdev.mapper;

import com.csci.susdev.model.EmissionFactor;
import com.csci.susdev.model.EmissionFactorExample;
import java.util.List;
import org.apache.ibatis.annotations.Param;

public interface EmissionFactorMapper {
    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table t_cf_emission_factor
     *
     * @mbg.generated
     */
    long countByExample(EmissionFactorExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table t_cf_emission_factor
     *
     * @mbg.generated
     */
    int deleteByExample(EmissionFactorExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table t_cf_emission_factor
     *
     * @mbg.generated
     */
    int deleteByPrimaryKey(String id);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table t_cf_emission_factor
     *
     * @mbg.generated
     */
    int insert(EmissionFactor record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table t_cf_emission_factor
     *
     * @mbg.generated
     */
    int insertSelective(EmissionFactor record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table t_cf_emission_factor
     *
     * @mbg.generated
     */
    List<EmissionFactor> selectByExample(EmissionFactorExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table t_cf_emission_factor
     *
     * @mbg.generated
     */
    EmissionFactor selectByPrimaryKey(String id);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table t_cf_emission_factor
     *
     * @mbg.generated
     */
    int updateByExampleSelective(@Param("record") EmissionFactor record, @Param("example") EmissionFactorExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table t_cf_emission_factor
     *
     * @mbg.generated
     */
    int updateByExample(@Param("record") EmissionFactor record, @Param("example") EmissionFactorExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table t_cf_emission_factor
     *
     * @mbg.generated
     */
    int updateByPrimaryKeySelective(EmissionFactor record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table t_cf_emission_factor
     *
     * @mbg.generated
     */
    int updateByPrimaryKey(EmissionFactor record);
}