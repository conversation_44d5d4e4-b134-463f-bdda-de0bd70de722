package com.csci.susdev.mapper;

import com.csci.susdev.model.UserRole;
import com.csci.susdev.model.UserRoleExample;
import java.util.List;
import org.apache.ibatis.annotations.Param;

public interface UserRoleMapper {

	/**
	 * This method was generated by MyBatis Generator. This method corresponds to the database table t_user_role
	 * @mbg.generated  Thu Jun 09 17:06:58 HKT 2022
	 */
	long countByExample(UserRoleExample example);

	/**
	 * This method was generated by MyBatis Generator. This method corresponds to the database table t_user_role
	 * @mbg.generated  Thu Jun 09 17:06:58 HKT 2022
	 */
	int deleteByExample(UserRoleExample example);

	/**
	 * This method was generated by MyBatis Generator. This method corresponds to the database table t_user_role
	 * @mbg.generated  Thu Jun 09 17:06:58 HKT 2022
	 */
	int deleteByPrimaryKey(String id);

	/**
	 * This method was generated by MyBatis Generator. This method corresponds to the database table t_user_role
	 * @mbg.generated  Thu Jun 09 17:06:58 HKT 2022
	 */
	int insert(UserRole row);

	/**
	 * This method was generated by MyBatis Generator. This method corresponds to the database table t_user_role
	 * @mbg.generated  Thu Jun 09 17:06:58 HKT 2022
	 */
	int insertSelective(UserRole row);

	/**
	 * This method was generated by MyBatis Generator. This method corresponds to the database table t_user_role
	 * @mbg.generated  Thu Jun 09 17:06:58 HKT 2022
	 */
	List<UserRole> selectByExample(UserRoleExample example);

	/**
	 * This method was generated by MyBatis Generator. This method corresponds to the database table t_user_role
	 * @mbg.generated  Thu Jun 09 17:06:58 HKT 2022
	 */
	UserRole selectByPrimaryKey(String id);

	/**
	 * This method was generated by MyBatis Generator. This method corresponds to the database table t_user_role
	 * @mbg.generated  Thu Jun 09 17:06:58 HKT 2022
	 */
	int updateByExampleSelective(@Param("row") UserRole row, @Param("example") UserRoleExample example);

	/**
	 * This method was generated by MyBatis Generator. This method corresponds to the database table t_user_role
	 * @mbg.generated  Thu Jun 09 17:06:58 HKT 2022
	 */
	int updateByExample(@Param("row") UserRole row, @Param("example") UserRoleExample example);

	/**
	 * This method was generated by MyBatis Generator. This method corresponds to the database table t_user_role
	 * @mbg.generated  Thu Jun 09 17:06:58 HKT 2022
	 */
	int updateByPrimaryKeySelective(UserRole row);

	/**
	 * This method was generated by MyBatis Generator. This method corresponds to the database table t_user_role
	 * @mbg.generated  Thu Jun 09 17:06:58 HKT 2022
	 */
	int updateByPrimaryKey(UserRole row);
}