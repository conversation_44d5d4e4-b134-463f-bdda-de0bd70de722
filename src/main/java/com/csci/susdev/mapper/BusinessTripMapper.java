package com.csci.susdev.mapper;

import com.csci.susdev.model.BusinessTrip;
import com.csci.susdev.model.BusinessTripExample;
import java.util.List;
import org.apache.ibatis.annotations.Param;

public interface BusinessTripMapper {

	/**
	 * This method was generated by MyBatis Generator. This method corresponds to the database table t_ab_business_trip
	 * @mbg.generated  Thu Aug 10 09:08:11 HKT 2023
	 */
	long countByExample(BusinessTripExample example);

	/**
	 * This method was generated by MyBatis Generator. This method corresponds to the database table t_ab_business_trip
	 * @mbg.generated  Thu Aug 10 09:08:11 HKT 2023
	 */
	int deleteByExample(BusinessTripExample example);

	/**
	 * This method was generated by MyBatis Generator. This method corresponds to the database table t_ab_business_trip
	 * @mbg.generated  Thu Aug 10 09:08:11 HKT 2023
	 */
	int deleteByPrimaryKey(String id);

	/**
	 * This method was generated by MyBatis Generator. This method corresponds to the database table t_ab_business_trip
	 * @mbg.generated  Thu Aug 10 09:08:11 HKT 2023
	 */
	int insert(BusinessTrip row);

	/**
	 * This method was generated by MyBatis Generator. This method corresponds to the database table t_ab_business_trip
	 * @mbg.generated  Thu Aug 10 09:08:11 HKT 2023
	 */
	int insertSelective(BusinessTrip row);

	/**
	 * This method was generated by MyBatis Generator. This method corresponds to the database table t_ab_business_trip
	 * @mbg.generated  Thu Aug 10 09:08:11 HKT 2023
	 */
	List<BusinessTrip> selectByExample(BusinessTripExample example);

	/**
	 * This method was generated by MyBatis Generator. This method corresponds to the database table t_ab_business_trip
	 * @mbg.generated  Thu Aug 10 09:08:11 HKT 2023
	 */
	BusinessTrip selectByPrimaryKey(String id);

	/**
	 * This method was generated by MyBatis Generator. This method corresponds to the database table t_ab_business_trip
	 * @mbg.generated  Thu Aug 10 09:08:11 HKT 2023
	 */
	int updateByExampleSelective(@Param("row") BusinessTrip row, @Param("example") BusinessTripExample example);

	/**
	 * This method was generated by MyBatis Generator. This method corresponds to the database table t_ab_business_trip
	 * @mbg.generated  Thu Aug 10 09:08:11 HKT 2023
	 */
	int updateByExample(@Param("row") BusinessTrip row, @Param("example") BusinessTripExample example);

	/**
	 * This method was generated by MyBatis Generator. This method corresponds to the database table t_ab_business_trip
	 * @mbg.generated  Thu Aug 10 09:08:11 HKT 2023
	 */
	int updateByPrimaryKeySelective(BusinessTrip row);

	/**
	 * This method was generated by MyBatis Generator. This method corresponds to the database table t_ab_business_trip
	 * @mbg.generated  Thu Aug 10 09:08:11 HKT 2023
	 */
	int updateByPrimaryKey(BusinessTrip row);
}