package com.csci.susdev.mapper;

import com.csci.susdev.model.User;
import com.csci.susdev.model.UserExample;
import java.util.List;
import org.apache.ibatis.annotations.Param;

public interface UserMapper {

	/**
	 * This method was generated by MyBatis Generator. This method corresponds to the database table t_user
	 * @mbg.generated  Tue Jan 16 12:02:28 HKT 2024
	 */
	long countByExample(UserExample example);

	/**
	 * This method was generated by MyBatis Generator. This method corresponds to the database table t_user
	 * @mbg.generated  Tue Jan 16 12:02:28 HKT 2024
	 */
	int deleteByExample(UserExample example);

	/**
	 * This method was generated by MyBatis Generator. This method corresponds to the database table t_user
	 * @mbg.generated  Tue Jan 16 12:02:28 HKT 2024
	 */
	int deleteByPrimaryKey(String id);

	/**
	 * This method was generated by MyBatis Generator. This method corresponds to the database table t_user
	 * @mbg.generated  Tue Jan 16 12:02:28 HKT 2024
	 */
	int insert(User row);

	/**
	 * This method was generated by MyBatis Generator. This method corresponds to the database table t_user
	 * @mbg.generated  Tue Jan 16 12:02:28 HKT 2024
	 */
	int insertSelective(User row);

	/**
	 * This method was generated by MyBatis Generator. This method corresponds to the database table t_user
	 * @mbg.generated  Tue Jan 16 12:02:28 HKT 2024
	 */
	List<User> selectByExample(UserExample example);

	/**
	 * This method was generated by MyBatis Generator. This method corresponds to the database table t_user
	 * @mbg.generated  Tue Jan 16 12:02:28 HKT 2024
	 */
	User selectByPrimaryKey(String id);

	/**
	 * This method was generated by MyBatis Generator. This method corresponds to the database table t_user
	 * @mbg.generated  Tue Jan 16 12:02:28 HKT 2024
	 */
	int updateByExampleSelective(@Param("row") User row, @Param("example") UserExample example);

	/**
	 * This method was generated by MyBatis Generator. This method corresponds to the database table t_user
	 * @mbg.generated  Tue Jan 16 12:02:28 HKT 2024
	 */
	int updateByExample(@Param("row") User row, @Param("example") UserExample example);

	/**
	 * This method was generated by MyBatis Generator. This method corresponds to the database table t_user
	 * @mbg.generated  Tue Jan 16 12:02:28 HKT 2024
	 */
	int updateByPrimaryKeySelective(User row);

	/**
	 * This method was generated by MyBatis Generator. This method corresponds to the database table t_user
	 * @mbg.generated  Tue Jan 16 12:02:28 HKT 2024
	 */
	int updateByPrimaryKey(User row);
}