package com.csci.susdev.mapper;

import com.csci.susdev.model.BsConfig;
import com.csci.susdev.model.BsConfigExample;
import java.util.List;
import org.apache.ibatis.annotations.Param;

public interface BsConfigMapper {
    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table t_bs_config
     *
     * @mbg.generated Wed Jun 05 12:21:28 HKT 2024
     */
    long countByExample(BsConfigExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table t_bs_config
     *
     * @mbg.generated Wed Jun 05 12:21:28 HKT 2024
     */
    int deleteByExample(BsConfigExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table t_bs_config
     *
     * @mbg.generated Wed Jun 05 12:21:28 HKT 2024
     */
    int deleteByPrimaryKey(String id);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table t_bs_config
     *
     * @mbg.generated Wed Jun 05 12:21:28 HKT 2024
     */
    int insert(BsConfig record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table t_bs_config
     *
     * @mbg.generated Wed Jun 05 12:21:28 HKT 2024
     */
    int insertSelective(BsConfig record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table t_bs_config
     *
     * @mbg.generated Wed Jun 05 12:21:28 HKT 2024
     */
    List<BsConfig> selectByExample(BsConfigExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table t_bs_config
     *
     * @mbg.generated Wed Jun 05 12:21:28 HKT 2024
     */
    BsConfig selectByPrimaryKey(String id);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table t_bs_config
     *
     * @mbg.generated Wed Jun 05 12:21:28 HKT 2024
     */
    int updateByExampleSelective(@Param("record") BsConfig record, @Param("example") BsConfigExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table t_bs_config
     *
     * @mbg.generated Wed Jun 05 12:21:28 HKT 2024
     */
    int updateByExample(@Param("record") BsConfig record, @Param("example") BsConfigExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table t_bs_config
     *
     * @mbg.generated Wed Jun 05 12:21:28 HKT 2024
     */
    int updateByPrimaryKeySelective(BsConfig record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table t_bs_config
     *
     * @mbg.generated Wed Jun 05 12:21:28 HKT 2024
     */
    int updateByPrimaryKey(BsConfig record);
}