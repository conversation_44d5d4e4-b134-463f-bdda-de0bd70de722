package com.csci.susdev.mapper;

import com.csci.susdev.model.CeOperationDataDetail;
import com.csci.susdev.model.CeOperationDataDetailExample;
import java.util.List;
import org.apache.ibatis.annotations.Param;

public interface CeOperationDataDetailMapper {

	/**
	 * This method was generated by MyBatis Generator. This method corresponds to the database table t_ce_operation_data_detail
	 * @mbg.generated  Fri Sep 15 14:58:05 HKT 2023
	 */
	long countByExample(CeOperationDataDetailExample example);

	/**
	 * This method was generated by MyBatis Generator. This method corresponds to the database table t_ce_operation_data_detail
	 * @mbg.generated  Fri Sep 15 14:58:05 HKT 2023
	 */
	int deleteByExample(CeOperationDataDetailExample example);

	/**
	 * This method was generated by MyBatis Generator. This method corresponds to the database table t_ce_operation_data_detail
	 * @mbg.generated  Fri Sep 15 14:58:05 HKT 2023
	 */
	int deleteByPrimaryKey(String id);

	/**
	 * This method was generated by MyBatis Generator. This method corresponds to the database table t_ce_operation_data_detail
	 * @mbg.generated  Fri Sep 15 14:58:05 HKT 2023
	 */
	int insert(CeOperationDataDetail row);

	/**
	 * This method was generated by MyBatis Generator. This method corresponds to the database table t_ce_operation_data_detail
	 * @mbg.generated  Fri Sep 15 14:58:05 HKT 2023
	 */
	int insertSelective(CeOperationDataDetail row);

	/**
	 * This method was generated by MyBatis Generator. This method corresponds to the database table t_ce_operation_data_detail
	 * @mbg.generated  Fri Sep 15 14:58:05 HKT 2023
	 */
	List<CeOperationDataDetail> selectByExample(CeOperationDataDetailExample example);

	/**
	 * This method was generated by MyBatis Generator. This method corresponds to the database table t_ce_operation_data_detail
	 * @mbg.generated  Fri Sep 15 14:58:05 HKT 2023
	 */
	CeOperationDataDetail selectByPrimaryKey(String id);

	/**
	 * This method was generated by MyBatis Generator. This method corresponds to the database table t_ce_operation_data_detail
	 * @mbg.generated  Fri Sep 15 14:58:05 HKT 2023
	 */
	int updateByExampleSelective(@Param("row") CeOperationDataDetail row,
			@Param("example") CeOperationDataDetailExample example);

	/**
	 * This method was generated by MyBatis Generator. This method corresponds to the database table t_ce_operation_data_detail
	 * @mbg.generated  Fri Sep 15 14:58:05 HKT 2023
	 */
	int updateByExample(@Param("row") CeOperationDataDetail row,
			@Param("example") CeOperationDataDetailExample example);

	/**
	 * This method was generated by MyBatis Generator. This method corresponds to the database table t_ce_operation_data_detail
	 * @mbg.generated  Fri Sep 15 14:58:05 HKT 2023
	 */
	int updateByPrimaryKeySelective(CeOperationDataDetail row);

	/**
	 * This method was generated by MyBatis Generator. This method corresponds to the database table t_ce_operation_data_detail
	 * @mbg.generated  Fri Sep 15 14:58:05 HKT 2023
	 */
	int updateByPrimaryKey(CeOperationDataDetail row);
}