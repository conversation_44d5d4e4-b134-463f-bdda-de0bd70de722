package com.csci.susdev.mapper;

import com.csci.susdev.model.FcCarbonFactorFlc;
import com.csci.susdev.model.FcCarbonFactorFlcExample;
import java.util.List;
import org.apache.ibatis.annotations.Param;

public interface FcCarbonFactorFlcMapper {
    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table t_fc_carbon_factor_flc
     *
     * @mbg.generated Wed Feb 05 09:43:33 CST 2025
     */
    long countByExample(FcCarbonFactorFlcExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table t_fc_carbon_factor_flc
     *
     * @mbg.generated Wed Feb 05 09:43:33 CST 2025
     */
    int deleteByExample(FcCarbonFactorFlcExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table t_fc_carbon_factor_flc
     *
     * @mbg.generated Wed Feb 05 09:43:33 CST 2025
     */
    int deleteByPrimaryKey(String id);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table t_fc_carbon_factor_flc
     *
     * @mbg.generated Wed Feb 05 09:43:33 CST 2025
     */
    int insert(FcCarbonFactorFlc record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table t_fc_carbon_factor_flc
     *
     * @mbg.generated Wed Feb 05 09:43:33 CST 2025
     */
    int insertSelective(FcCarbonFactorFlc record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table t_fc_carbon_factor_flc
     *
     * @mbg.generated Wed Feb 05 09:43:33 CST 2025
     */
    List<FcCarbonFactorFlc> selectByExample(FcCarbonFactorFlcExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table t_fc_carbon_factor_flc
     *
     * @mbg.generated Wed Feb 05 09:43:33 CST 2025
     */
    FcCarbonFactorFlc selectByPrimaryKey(String id);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table t_fc_carbon_factor_flc
     *
     * @mbg.generated Wed Feb 05 09:43:33 CST 2025
     */
    int updateByExampleSelective(@Param("record") FcCarbonFactorFlc record, @Param("example") FcCarbonFactorFlcExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table t_fc_carbon_factor_flc
     *
     * @mbg.generated Wed Feb 05 09:43:33 CST 2025
     */
    int updateByExample(@Param("record") FcCarbonFactorFlc record, @Param("example") FcCarbonFactorFlcExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table t_fc_carbon_factor_flc
     *
     * @mbg.generated Wed Feb 05 09:43:33 CST 2025
     */
    int updateByPrimaryKeySelective(FcCarbonFactorFlc record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table t_fc_carbon_factor_flc
     *
     * @mbg.generated Wed Feb 05 09:43:33 CST 2025
     */
    int updateByPrimaryKey(FcCarbonFactorFlc record);
}