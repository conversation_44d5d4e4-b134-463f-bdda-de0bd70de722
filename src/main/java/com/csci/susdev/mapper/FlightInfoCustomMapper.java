package com.csci.susdev.mapper;

import com.csci.susdev.model.FlightInfo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;

@Mapper
public interface FlightInfoCustomMapper {

    @Select("""
            SELECT TOP 10 t.id, t.record_year AS recordYear, t.start_place AS startPlace, t.destination,
            t.level, t.ticket_type AS ticketType, t.person_count AS personCount, t.flight_distance AS flightDistance,
            t.carbon_emission AS carbonEmission, t.creation_time AS creationTime, t.create_username AS createUsername,
            t.create_user_id AS createUserId, t.last_update_time AS lastUpdateTime, t.last_update_username AS lastUpdateUsername,
            t.last_update_user_id AS lastUpdateUserId
            FROM t_flight_info t
            WHERE t.record_year = (SELECT MAX(record_year) FROM t_flight_info)
            AND t.level = #{level}
            AND t.ticket_type = #{ticketType}
            AND t.person_count = #{personCount}
            AND t.start_place = #{startPlace}
            AND t.destination = #{destination}
            """)
    public FlightInfo get(@Param("level") String level,
                                 @Param("ticketType") Integer ticketType,
                                 @Param("personCount") Integer personCount,
                                 @Param("startPlace") String startPlace,
                                 @Param("destination") String destination);
    /**
     * 批量插入
     * @param list
     * @return
     */
    int batchInsert(List<FlightInfo> list);
}