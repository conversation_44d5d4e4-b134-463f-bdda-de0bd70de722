package com.csci.susdev.mapper;

import com.csci.susdev.model.ProtocolCategory;
import com.csci.susdev.model.ProtocolCategoryExample;
import java.util.List;
import org.apache.ibatis.annotations.Param;

public interface ProtocolCategoryMapper {
    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table t_protocol_category
     *
     * @mbg.generated Wed Apr 10 09:46:06 HKT 2024
     */
    long countByExample(ProtocolCategoryExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table t_protocol_category
     *
     * @mbg.generated Wed Apr 10 09:46:06 HKT 2024
     */
    int deleteByExample(ProtocolCategoryExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table t_protocol_category
     *
     * @mbg.generated Wed Apr 10 09:46:06 HKT 2024
     */
    int deleteByPrimaryKey(String id);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table t_protocol_category
     *
     * @mbg.generated Wed Apr 10 09:46:06 HKT 2024
     */
    int insert(ProtocolCategory record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table t_protocol_category
     *
     * @mbg.generated Wed Apr 10 09:46:06 HKT 2024
     */
    int insertSelective(ProtocolCategory record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table t_protocol_category
     *
     * @mbg.generated Wed Apr 10 09:46:06 HKT 2024
     */
    List<ProtocolCategory> selectByExample(ProtocolCategoryExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table t_protocol_category
     *
     * @mbg.generated Wed Apr 10 09:46:06 HKT 2024
     */
    ProtocolCategory selectByPrimaryKey(String id);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table t_protocol_category
     *
     * @mbg.generated Wed Apr 10 09:46:06 HKT 2024
     */
    int updateByExampleSelective(@Param("record") ProtocolCategory record, @Param("example") ProtocolCategoryExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table t_protocol_category
     *
     * @mbg.generated Wed Apr 10 09:46:06 HKT 2024
     */
    int updateByExample(@Param("record") ProtocolCategory record, @Param("example") ProtocolCategoryExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table t_protocol_category
     *
     * @mbg.generated Wed Apr 10 09:46:06 HKT 2024
     */
    int updateByPrimaryKeySelective(ProtocolCategory record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table t_protocol_category
     *
     * @mbg.generated Wed Apr 10 09:46:06 HKT 2024
     */
    int updateByPrimaryKey(ProtocolCategory record);
}