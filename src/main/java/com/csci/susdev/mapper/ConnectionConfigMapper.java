package com.csci.susdev.mapper;

import com.csci.susdev.model.ConnectionConfig;
import com.csci.susdev.model.ConnectionConfigExample;
import java.util.List;
import org.apache.ibatis.annotations.Param;

public interface ConnectionConfigMapper {
    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table t_connection_config
     *
     * @mbg.generated Wed Apr 17 10:06:38 HKT 2024
     */
    long countByExample(ConnectionConfigExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table t_connection_config
     *
     * @mbg.generated Wed Apr 17 10:06:38 HKT 2024
     */
    int deleteByExample(ConnectionConfigExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table t_connection_config
     *
     * @mbg.generated Wed Apr 17 10:06:38 HKT 2024
     */
    int deleteByPrimaryKey(String id);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table t_connection_config
     *
     * @mbg.generated Wed Apr 17 10:06:38 HKT 2024
     */
    int insert(ConnectionConfig record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table t_connection_config
     *
     * @mbg.generated Wed Apr 17 10:06:38 HKT 2024
     */
    int insertSelective(ConnectionConfig record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table t_connection_config
     *
     * @mbg.generated Wed Apr 17 10:06:38 HKT 2024
     */
    List<ConnectionConfig> selectByExample(ConnectionConfigExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table t_connection_config
     *
     * @mbg.generated Wed Apr 17 10:06:38 HKT 2024
     */
    ConnectionConfig selectByPrimaryKey(String id);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table t_connection_config
     *
     * @mbg.generated Wed Apr 17 10:06:38 HKT 2024
     */
    int updateByExampleSelective(@Param("record") ConnectionConfig record, @Param("example") ConnectionConfigExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table t_connection_config
     *
     * @mbg.generated Wed Apr 17 10:06:38 HKT 2024
     */
    int updateByExample(@Param("record") ConnectionConfig record, @Param("example") ConnectionConfigExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table t_connection_config
     *
     * @mbg.generated Wed Apr 17 10:06:38 HKT 2024
     */
    int updateByPrimaryKeySelective(ConnectionConfig record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table t_connection_config
     *
     * @mbg.generated Wed Apr 17 10:06:38 HKT 2024
     */
    int updateByPrimaryKey(ConnectionConfig record);
}