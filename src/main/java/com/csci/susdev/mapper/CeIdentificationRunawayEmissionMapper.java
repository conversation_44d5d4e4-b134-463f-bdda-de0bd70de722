package com.csci.susdev.mapper;

import com.csci.susdev.model.CeIdentificationRunawayEmission;
import com.csci.susdev.model.CeIdentificationRunawayEmissionExample;
import java.util.List;
import org.apache.ibatis.annotations.Param;

public interface CeIdentificationRunawayEmissionMapper {
    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table t_ce_identification_runaway_emission
     *
     * @mbg.generated Tue Apr 01 14:16:37 CST 2025
     */
    long countByExample(CeIdentificationRunawayEmissionExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table t_ce_identification_runaway_emission
     *
     * @mbg.generated Tue Apr 01 14:16:37 CST 2025
     */
    int deleteByExample(CeIdentificationRunawayEmissionExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table t_ce_identification_runaway_emission
     *
     * @mbg.generated Tue Apr 01 14:16:37 CST 2025
     */
    int deleteByPrimaryKey(String id);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table t_ce_identification_runaway_emission
     *
     * @mbg.generated Tue Apr 01 14:16:37 CST 2025
     */
    int insert(CeIdentificationRunawayEmission record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table t_ce_identification_runaway_emission
     *
     * @mbg.generated Tue Apr 01 14:16:37 CST 2025
     */
    int insertSelective(CeIdentificationRunawayEmission record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table t_ce_identification_runaway_emission
     *
     * @mbg.generated Tue Apr 01 14:16:37 CST 2025
     */
    List<CeIdentificationRunawayEmission> selectByExample(CeIdentificationRunawayEmissionExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table t_ce_identification_runaway_emission
     *
     * @mbg.generated Tue Apr 01 14:16:37 CST 2025
     */
    CeIdentificationRunawayEmission selectByPrimaryKey(String id);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table t_ce_identification_runaway_emission
     *
     * @mbg.generated Tue Apr 01 14:16:37 CST 2025
     */
    int updateByExampleSelective(@Param("record") CeIdentificationRunawayEmission record, @Param("example") CeIdentificationRunawayEmissionExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table t_ce_identification_runaway_emission
     *
     * @mbg.generated Tue Apr 01 14:16:37 CST 2025
     */
    int updateByExample(@Param("record") CeIdentificationRunawayEmission record, @Param("example") CeIdentificationRunawayEmissionExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table t_ce_identification_runaway_emission
     *
     * @mbg.generated Tue Apr 01 14:16:37 CST 2025
     */
    int updateByPrimaryKeySelective(CeIdentificationRunawayEmission record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table t_ce_identification_runaway_emission
     *
     * @mbg.generated Tue Apr 01 14:16:37 CST 2025
     */
    int updateByPrimaryKey(CeIdentificationRunawayEmission record);
}