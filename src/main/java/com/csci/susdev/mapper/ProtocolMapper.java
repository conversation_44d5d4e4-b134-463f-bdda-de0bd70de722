package com.csci.susdev.mapper;

import com.csci.susdev.model.Protocol;
import com.csci.susdev.model.ProtocolExample;
import java.util.List;
import org.apache.ibatis.annotations.Param;

public interface ProtocolMapper {
    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table t_protocol
     *
     * @mbg.generated Tue Apr 09 18:05:16 HKT 2024
     */
    long countByExample(ProtocolExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table t_protocol
     *
     * @mbg.generated Tue Apr 09 18:05:16 HKT 2024
     */
    int deleteByExample(ProtocolExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table t_protocol
     *
     * @mbg.generated Tue Apr 09 18:05:16 HKT 2024
     */
    int deleteByPrimaryKey(String id);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table t_protocol
     *
     * @mbg.generated Tue Apr 09 18:05:16 HKT 2024
     */
    int insert(Protocol record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table t_protocol
     *
     * @mbg.generated Tue Apr 09 18:05:16 HKT 2024
     */
    int insertSelective(Protocol record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table t_protocol
     *
     * @mbg.generated Tue Apr 09 18:05:16 HKT 2024
     */
    List<Protocol> selectByExample(ProtocolExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table t_protocol
     *
     * @mbg.generated Tue Apr 09 18:05:16 HKT 2024
     */
    Protocol selectByPrimaryKey(String id);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table t_protocol
     *
     * @mbg.generated Tue Apr 09 18:05:16 HKT 2024
     */
    int updateByExampleSelective(@Param("record") Protocol record, @Param("example") ProtocolExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table t_protocol
     *
     * @mbg.generated Tue Apr 09 18:05:16 HKT 2024
     */
    int updateByExample(@Param("record") Protocol record, @Param("example") ProtocolExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table t_protocol
     *
     * @mbg.generated Tue Apr 09 18:05:16 HKT 2024
     */
    int updateByPrimaryKeySelective(Protocol record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table t_protocol
     *
     * @mbg.generated Tue Apr 09 18:05:16 HKT 2024
     */
    int updateByPrimaryKey(Protocol record);
}