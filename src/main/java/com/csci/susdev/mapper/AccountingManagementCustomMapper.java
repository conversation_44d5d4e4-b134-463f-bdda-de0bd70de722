package com.csci.susdev.mapper;

import com.csci.susdev.model.AccountingManagement;
import com.csci.susdev.qo.ProtocolConfigurationQO;
import com.csci.susdev.vo.ProtocolConfigurationVO;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;

public interface AccountingManagementCustomMapper {

	@Select("""
	<script>
			select
				t.id,
				CASE compute_symbol
					 WHEN '+' THEN CAST(count_one AS DECIMAL) + CAST(count_two AS DECIMAL)
					 WHEN '-' THEN CAST(count_one AS DECIMAL) - CAST(count_two AS DECIMAL)
					 WHEN '*' THEN CAST(count_one AS DECIMAL) * CAST(count_two AS DECIMAL)
					 WHEN '/' THEN
						 CASE
							 WHEN count_two = 0 THEN NULL -- 避免除以零错误
							 ELSE CAST(count_one AS DECIMAL) / CAST(count_two AS DECIMAL)
						 END
					 ELSE NULL -- 处理未知的运算符
				 END AS calculationResult,
				t.last_update_version
			from t_accounting_management t
			where t.is_deleted = 0
			<if test="idList != null and idList != ''">
                and t.id in
                    <foreach collection="idList" item="item" open="(" separator="," close=")">
                        #{item}
                   </foreach>
			</if>
	 </script>
	""")
	List<AccountingManagement> listCalculation(@Param("idList") List<String> idList);
}
