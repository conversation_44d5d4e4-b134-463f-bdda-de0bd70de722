package com.csci.susdev.mapper;

import com.csci.susdev.model.PermissionOperation;
import com.csci.susdev.model.PermissionOperationExample;
import java.util.List;
import org.apache.ibatis.annotations.Param;

public interface PermissionOperationMapper {

	/**
	 * This method was generated by MyBatis Generator. This method corresponds to the database table t_permission_operation
	 * @mbg.generated  Thu Jun 09 17:05:52 HKT 2022
	 */
	long countByExample(PermissionOperationExample example);

	/**
	 * This method was generated by MyBatis Generator. This method corresponds to the database table t_permission_operation
	 * @mbg.generated  Thu Jun 09 17:05:52 HKT 2022
	 */
	int deleteByExample(PermissionOperationExample example);

	/**
	 * This method was generated by MyBatis Generator. This method corresponds to the database table t_permission_operation
	 * @mbg.generated  Thu Jun 09 17:05:52 HKT 2022
	 */
	int deleteByPrimaryKey(String id);

	/**
	 * This method was generated by MyBatis Generator. This method corresponds to the database table t_permission_operation
	 * @mbg.generated  Thu Jun 09 17:05:52 HKT 2022
	 */
	int insert(PermissionOperation row);

	/**
	 * This method was generated by MyBatis Generator. This method corresponds to the database table t_permission_operation
	 * @mbg.generated  Thu Jun 09 17:05:52 HKT 2022
	 */
	int insertSelective(PermissionOperation row);

	/**
	 * This method was generated by MyBatis Generator. This method corresponds to the database table t_permission_operation
	 * @mbg.generated  Thu Jun 09 17:05:52 HKT 2022
	 */
	List<PermissionOperation> selectByExample(PermissionOperationExample example);

	/**
	 * This method was generated by MyBatis Generator. This method corresponds to the database table t_permission_operation
	 * @mbg.generated  Thu Jun 09 17:05:52 HKT 2022
	 */
	PermissionOperation selectByPrimaryKey(String id);

	/**
	 * This method was generated by MyBatis Generator. This method corresponds to the database table t_permission_operation
	 * @mbg.generated  Thu Jun 09 17:05:52 HKT 2022
	 */
	int updateByExampleSelective(@Param("row") PermissionOperation row,
			@Param("example") PermissionOperationExample example);

	/**
	 * This method was generated by MyBatis Generator. This method corresponds to the database table t_permission_operation
	 * @mbg.generated  Thu Jun 09 17:05:52 HKT 2022
	 */
	int updateByExample(@Param("row") PermissionOperation row, @Param("example") PermissionOperationExample example);

	/**
	 * This method was generated by MyBatis Generator. This method corresponds to the database table t_permission_operation
	 * @mbg.generated  Thu Jun 09 17:05:52 HKT 2022
	 */
	int updateByPrimaryKeySelective(PermissionOperation row);

	/**
	 * This method was generated by MyBatis Generator. This method corresponds to the database table t_permission_operation
	 * @mbg.generated  Thu Jun 09 17:05:52 HKT 2022
	 */
	int updateByPrimaryKey(PermissionOperation row);
}