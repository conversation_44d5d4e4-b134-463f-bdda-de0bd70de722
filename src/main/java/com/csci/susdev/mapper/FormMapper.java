package com.csci.susdev.mapper;

import com.csci.susdev.model.Form;
import com.csci.susdev.model.FormExample;
import java.util.List;
import org.apache.ibatis.annotations.Param;

public interface FormMapper {
    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table t_form
     *
     * @mbg.generated Fri Apr 12 15:51:26 HKT 2024
     */
    long countByExample(FormExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table t_form
     *
     * @mbg.generated Fri Apr 12 15:51:26 HKT 2024
     */
    int deleteByExample(FormExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table t_form
     *
     * @mbg.generated Fri Apr 12 15:51:26 HKT 2024
     */
    int deleteByPrimaryKey(String id);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table t_form
     *
     * @mbg.generated Fri Apr 12 15:51:26 HKT 2024
     */
    int insert(Form record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table t_form
     *
     * @mbg.generated Fri Apr 12 15:51:26 HKT 2024
     */
    int insertSelective(Form record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table t_form
     *
     * @mbg.generated Fri Apr 12 15:51:26 HKT 2024
     */
    List<Form> selectByExample(FormExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table t_form
     *
     * @mbg.generated Fri Apr 12 15:51:26 HKT 2024
     */
    Form selectByPrimaryKey(String id);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table t_form
     *
     * @mbg.generated Fri Apr 12 15:51:26 HKT 2024
     */
    int updateByExampleSelective(@Param("record") Form record, @Param("example") FormExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table t_form
     *
     * @mbg.generated Fri Apr 12 15:51:26 HKT 2024
     */
    int updateByExample(@Param("record") Form record, @Param("example") FormExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table t_form
     *
     * @mbg.generated Fri Apr 12 15:51:26 HKT 2024
     */
    int updateByPrimaryKeySelective(Form record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table t_form
     *
     * @mbg.generated Fri Apr 12 15:51:26 HKT 2024
     */
    int updateByPrimaryKey(Form record);
}