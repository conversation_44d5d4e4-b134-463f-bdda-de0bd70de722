package com.csci.susdev.mapper;

import com.csci.susdev.qo.FormDetailQO;
import com.csci.susdev.vo.FormDetailVO;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;

public interface FormDetailCustomMapper {
    //TODO: 每年手動維護
    /*
    insert into t_form_detail (form_code, year, type_a, type_b, type_c, type_d, type_e)
select distinct 'ambient' as formName,  ah.year, ad.category as typeA, ad.type as typeB,
ad.type2 as typeC, ad.unit as typeD, ad.unit_code as typeE
from t_ambient_head ah
left join t_ambient_detail ad on ah.id = ad.head_id
where ah.year = 2023 and ah.is_active = 1 and ad.category IS NOT NULL and ad.category <> '' and ad.type2 IS NOT NULL
order by ad.unit_code
     */

    @Select("""
            <script>
                select fd.id, fd.form_code as formCode, fd.year, fd.type_a as typeA, fd.type_b as typeB,
                fd.type_c as typeC, fd.type_d as typeD, fd.type_e as typeE, fd.month as month
                from t_form_detail fd
                where fd.is_deleted = 0 
                
                <if test="formDetailQO.year != null and formDetailQO.year != ''">
                and fd.year = #{formDetailQO.year}
                </if>
                <if test="formDetailQO.month != null and formDetailQO.month != ''">
                and fd.month = #{formDetailQO.month}
                </if>
                <if test="formDetailQO.formCode != null and formDetailQO.formCode != ''">
                and fd.form_code = #{formDetailQO.formCode}
                </if>
                <if test="formDetailQO.typeE != null and formDetailQO.typeE != ''">
                and fd.form_code = #{formDetailQO.typeE}
                </if>
                <if test="formDetailQO.orderBy != null and formDetailQO.orderBy != ''">
                order by ${formDetailQO.orderBy}
                </if>
            </script>
                """)
    public List<FormDetailVO> listAmbientFormDetail(@Param("formDetailQO") FormDetailQO formDetailQO);

    @Select("""
            <script>
                select fd.id, fd.form_code as formCode, fd.year, fd.type_a as typeA, fd.type_b as typeB,
                fd.type_c as typeC, fd.type_d as typeD, fd.type_e as typeE
                from t_form_detail fd
                where fd.is_deleted = 0 
                
                <if test="year != null and year != ''">
                and fd.year = #{year}
                </if>
                <if test="formCode != null and formCode != ''">
                and fd.form_code = #{formCode}
                </if>
                and fd.month = (
                    select max([month]) from t_form_detail where is_deleted = 0 
                    <if test="year != null and year != ''">
                        and fd.year = #{year}
                    </if>
                    )
                <if test="orderBy != null and orderBy != ''">
                order by ${orderBy}
                </if>
            </script>
                """)
    List<FormDetailVO> listFormDetailMaxCurrentMonth(@Param("year") Integer year, @Param("orderBy") String orderBy, @Param("formCode") String formCode);
}
