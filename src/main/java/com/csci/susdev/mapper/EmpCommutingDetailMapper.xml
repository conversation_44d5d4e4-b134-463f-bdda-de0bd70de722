<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.csci.susdev.mapper.EmpCommutingDetailMapper">
  <resultMap id="BaseResultMap" type="com.csci.cohl.model.EmpCommutingDetail">
    <!--@mbg.generated-->
    <!--@Table t_emp_commuting_detail-->
    <id column="id" jdbcType="VARCHAR" property="id" />
    <result column="company" jdbcType="VARCHAR" property="company" />
    <result column="department" jdbcType="VARCHAR" property="department" />
    <result column="transportation" jdbcType="VARCHAR" property="transportation" />
    <result column="commute_distance" jdbcType="FLOAT" property="commuteDistance" />
    <result column="one_way_commute_time" jdbcType="INTEGER" property="oneWayCommuteTime" />
    <result column="leave_days" jdbcType="INTEGER" property="leaveDays" />
    <result column="overtime_days" jdbcType="INTEGER" property="overtimeDays" />
    <result column="commute_days" jdbcType="INTEGER" property="commuteDays" />
    <result column="creation_time" jdbcType="TIMESTAMP" property="creationTime" />
    <result column="create_username" jdbcType="VARCHAR" property="createUsername" />
    <result column="create_user_id" jdbcType="VARCHAR" property="createUserId" />
    <result column="last_update_time" jdbcType="TIMESTAMP" property="lastUpdateTime" />
    <result column="last_update_username" jdbcType="VARCHAR" property="lastUpdateUsername" />
    <result column="last_update_user_id" jdbcType="VARCHAR" property="lastUpdateUserId" />
    <result column="last_update_version" jdbcType="INTEGER" property="lastUpdateVersion" />
    <result column="head_id" jdbcType="VARCHAR" property="headId" />
    <result column="remark" jdbcType="VARCHAR" property="remark" />
    <result column="seq" jdbcType="INTEGER" property="seq" />

  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    id, company, department, transportation, commute_distance, one_way_commute_time, 
    leave_days, overtime_days, commute_days, creation_time, create_username, create_user_id, 
    last_update_time, last_update_username, last_update_user_id, last_update_version, 
    head_id,remark,seq
  </sql>
</mapper>