package com.csci.susdev.mapper;

import com.csci.susdev.model.ApiLog;
import com.csci.susdev.model.ApiLogExample;
import java.util.List;
import org.apache.ibatis.annotations.Param;

public interface ApiLogMapper {
    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table t_api_log
     *
     * @mbg.generated Tue Apr 16 15:40:18 HKT 2024
     */
    long countByExample(ApiLogExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table t_api_log
     *
     * @mbg.generated Tue Apr 16 15:40:18 HKT 2024
     */
    int deleteByExample(ApiLogExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table t_api_log
     *
     * @mbg.generated Tue Apr 16 15:40:18 HKT 2024
     */
    int deleteByPrimaryKey(String id);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table t_api_log
     *
     * @mbg.generated Tue Apr 16 15:40:18 HKT 2024
     */
    int insert(ApiLog record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table t_api_log
     *
     * @mbg.generated Tue Apr 16 15:40:18 HKT 2024
     */
    int insertSelective(ApiLog record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table t_api_log
     *
     * @mbg.generated Tue Apr 16 15:40:18 HKT 2024
     */
    List<ApiLog> selectByExample(ApiLogExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table t_api_log
     *
     * @mbg.generated Tue Apr 16 15:40:18 HKT 2024
     */
    ApiLog selectByPrimaryKey(String id);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table t_api_log
     *
     * @mbg.generated Tue Apr 16 15:40:18 HKT 2024
     */
    int updateByExampleSelective(@Param("record") ApiLog record, @Param("example") ApiLogExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table t_api_log
     *
     * @mbg.generated Tue Apr 16 15:40:18 HKT 2024
     */
    int updateByExample(@Param("record") ApiLog record, @Param("example") ApiLogExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table t_api_log
     *
     * @mbg.generated Tue Apr 16 15:40:18 HKT 2024
     */
    int updateByPrimaryKeySelective(ApiLog record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table t_api_log
     *
     * @mbg.generated Tue Apr 16 15:40:18 HKT 2024
     */
    int updateByPrimaryKey(ApiLog record);
}