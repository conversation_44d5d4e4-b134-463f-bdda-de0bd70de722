package com.csci.susdev.mapper;

import com.csci.susdev.qo.ProtocolManagementQO;
import com.csci.susdev.vo.ProtocolDetailVO;
import com.csci.susdev.vo.ProtocolManagementVO;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.Update;

import java.util.List;

public interface ProtocolManagementCustomMapper {

    @Select("""
            <script>
            select * from (
                SELECT
                  p.id AS protocolId,
                  p.name AS protocolName,
                  p.name_sc AS protocolNameSc,
                  p.name_en AS protocolNameEn,
                  p.description as protocolDescription,
                  p.last_update_version AS protocolLastUpdateVersion,
                  pc.id AS categoryId,
                  pc.category_name AS categoryName,
                  pc.category_name_sc AS categoryNameSc,
                  pc.category_name_en AS categoryNameEn,
                  pc.last_update_version AS categoryLastUpdateVersion,
                  psc.id AS subCategoryId,
                  psc.sub_category_name AS subCategoryName,
                  psc.sub_category_name_sc AS subCategoryNameSc,
                  psc.sub_category_name_en AS subCategoryNameEn,
                  psc.last_update_version AS subCategoryLastUpdateVersion
                FROM
                  t_protocol p
                  LEFT JOIN t_protocol_category pc ON pc.protocol_id = p.id
                  AND pc.is_deleted = 0
                  LEFT JOIN t_protocol_sub_category psc ON psc.category_id = pc.id
                  AND psc.is_deleted = 0
                WHERE
                  p.is_deleted = 0
                <if test="protocolManagementQO.protocolId != null and protocolManagementQO.protocolId != ''">
				and p.id = #{protocolManagementQO.protocolId}
                </if>
                <if test="protocolManagementQO.categoryId != null and protocolManagementQO.categoryId != ''">
                    and pc.id = #{protocolManagementQO.categoryId}
                </if>
                <if test="protocolManagementQO.subCategoryId != null and protocolManagementQO.subCategoryId != ''">
                    and psc.id = #{protocolManagementQO.subCategoryId}
                </if>
            ) t
            order by t.protocolId, t.categoryName,t.subCategoryName
            </script>
            """)
    public List<ProtocolManagementVO> list(@Param("protocolManagementQO") ProtocolManagementQO protocolManagementQO);


    @Select("""
            <script>
            select * from (
                SELECT
                  top 1
                  p.id AS protocolId,
                  p.name AS protocolName,
                  p.name_sc AS protocolNameSc,
                  p.name_en AS protocolNameEn,
                  p.description as protocolDescription,
                  p.last_update_version AS protocolLastUpdateVersion,
                  pc.id AS categoryId,
                  pc.category_name AS categoryName,
                  pc.category_name_sc AS categoryNameSc,
                  pc.category_name_en AS categoryNameEn,
                  pc.last_update_version AS categoryLastUpdateVersion,
                  psc.id AS subCategoryId,
                  psc.sub_category_name AS subCategoryName,
                  psc.sub_category_name_sc AS subCategoryNameSc,
                  psc.sub_category_name_en AS subCategoryNameEn,
                  psc.last_update_version AS subCategoryLastUpdateVersion
                FROM
                  t_protocol p
                  LEFT JOIN t_protocol_category pc ON pc.protocol_id = p.id
                  AND pc.is_deleted = 0
                  LEFT JOIN t_protocol_sub_category psc ON psc.category_id = pc.id
                  AND psc.is_deleted = 0
                WHERE
                  p.is_deleted = 0
                  and psc.id = #{subCategoryId}
            ) t
            order by t.protocolId, t.categoryName,t.subCategoryName
            </script>
            """)
    public ProtocolManagementVO getBySubCategoryId(@Param("subCategoryId") String subCategoryId);


    @Update("""
        <script>
        update t_protocol_sub_category
            set is_deleted = 1
        where is_deleted = 0
        and id in
            <foreach collection="ids" item="item" open="(" separator="," close=")">
                        #{item}
            </foreach>
        </script>
    """)
    void deleteSubCategoryByIds(@Param("ids") List<String> ids);

    @Update("""
        <script>
        update t_protocol_category
            set is_deleted = 1
        where is_deleted = 0
        and id in
            <foreach collection="ids" item="item" open="(" separator="," close=")">
                        #{item}
            </foreach>
        </script>
    """)
    void deleteCategoryByIds(@Param("ids") List<String> ids);

}
