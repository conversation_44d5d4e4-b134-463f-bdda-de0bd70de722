package com.csci.susdev.mapper;

import com.csci.susdev.qo.ApiLogQO;
import com.csci.susdev.vo.ApiLogVO;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;

public interface ApiLogCustomMapper {
    @Select({
            "<script>",
            "SELECT TOP 1 mac FROM t_api_log WHERE ip = #{ip} AND start_time > #{startTime} ORDER BY start_time DESC",
            "</script>"
    })
    String getRecentMac(@Param("ip") String ip, @Param("startTime") String startTime);

    @Select("""
            <script>
            select * from (
                SELECT al.id, (u.name + '（' + al.username + '）') as username, 
                al.token, al.app_id as appId, al.app_key as appKey, al.page,
                al.method, al.api, al.ip, al.mac, al.code, al.type, al.start_time as startTime, al.end_time as endTime
                FROM t_api_log al
                left join t_user u on u.username = al.username
                where 1 = 1
                
                <if test="apiLogQO.page != null and apiLogQO.page != ''">
                and al.page = #{apiLogQO.page}
                </if>
                
                <if test="apiLogQO.api != null and apiLogQO.api != ''">
                and al.api LIKE '%' + #{apiLogQO.api} + '%'
                </if>
                
                <if test="apiLogQO.code != null and apiLogQO.code != ''">
                and al.code in 
                    <foreach collection="apiLogQO.code.split(',')" item="item" open="(" separator="," close=")">
                        #{item}
                   </foreach>
                </if>
                
                <if test="apiLogQO.username != null and apiLogQO.username != ''">
                and (al.username = #{apiLogQO.username} or u.name = #{apiLogQO.username})
                </if>
                
                <if test="apiLogQO.startTimeFrom != null and apiLogQO.startTimeFrom != ''">
                and al.start_time &gt;= #{apiLogQO.startTimeFrom}
                </if>
                
                <if test="apiLogQO.startTimeTo != null and apiLogQO.startTimeTo != ''">
                and al.start_time &lt;= concat(#{apiLogQO.startTimeTo} , ' 23:59:59.999')
                </if>
                
                <if test="apiLogQO.parameter != null and apiLogQO.parameter != ''">
                and al.parameter LIKE '%' + #{apiLogQO.parameter} + '%'
                </if>
                
                <if test="apiLogQO.request != null and apiLogQO.request != ''">
                and al.request LIKE '%' + #{apiLogQO.request} + '%'
                </if>
                
            ) t
            
            <if test="apiLogQO.orderBy != null and apiLogQO.orderBy != ''">
            order by ${apiLogQO.orderBy}
            </if>
            
            </script>
            """)
    public List<ApiLogVO> list(@Param("apiLogQO") ApiLogQO apiLogQO);


    /**
     * 根据关键参数查询api日志(只查询变更数据的操作)
     * @param headId
     * @return
     */
    List<ApiLogVO> listApiLogByHeadId(@Param("headId") String headId);
}
