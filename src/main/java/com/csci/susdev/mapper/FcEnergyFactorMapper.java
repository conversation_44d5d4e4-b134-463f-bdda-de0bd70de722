package com.csci.susdev.mapper;

import com.csci.susdev.model.FcEnergyFactor;
import com.csci.susdev.model.FcEnergyFactorExample;
import java.util.List;
import org.apache.ibatis.annotations.Param;

public interface FcEnergyFactorMapper {
    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table t_fc_energy_factor
     *
     * @mbg.generated Mon Mar 17 16:21:10 CST 2025
     */
    long countByExample(FcEnergyFactorExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table t_fc_energy_factor
     *
     * @mbg.generated Mon Mar 17 16:21:10 CST 2025
     */
    int deleteByExample(FcEnergyFactorExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table t_fc_energy_factor
     *
     * @mbg.generated Mon Mar 17 16:21:10 CST 2025
     */
    int deleteByPrimaryKey(String id);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table t_fc_energy_factor
     *
     * @mbg.generated Mon Mar 17 16:21:10 CST 2025
     */
    int insert(FcEnergyFactor record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table t_fc_energy_factor
     *
     * @mbg.generated Mon Mar 17 16:21:10 CST 2025
     */
    int insertSelective(FcEnergyFactor record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table t_fc_energy_factor
     *
     * @mbg.generated Mon Mar 17 16:21:10 CST 2025
     */
    List<FcEnergyFactor> selectByExample(FcEnergyFactorExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table t_fc_energy_factor
     *
     * @mbg.generated Mon Mar 17 16:21:10 CST 2025
     */
    FcEnergyFactor selectByPrimaryKey(String id);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table t_fc_energy_factor
     *
     * @mbg.generated Mon Mar 17 16:21:10 CST 2025
     */
    int updateByExampleSelective(@Param("record") FcEnergyFactor record, @Param("example") FcEnergyFactorExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table t_fc_energy_factor
     *
     * @mbg.generated Mon Mar 17 16:21:10 CST 2025
     */
    int updateByExample(@Param("record") FcEnergyFactor record, @Param("example") FcEnergyFactorExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table t_fc_energy_factor
     *
     * @mbg.generated Mon Mar 17 16:21:10 CST 2025
     */
    int updateByPrimaryKeySelective(FcEnergyFactor record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table t_fc_energy_factor
     *
     * @mbg.generated Mon Mar 17 16:21:10 CST 2025
     */
    int updateByPrimaryKey(FcEnergyFactor record);
}