package com.csci.susdev.mapper;

import com.csci.susdev.model.CeIdentificationSubcontractor;
import com.csci.susdev.model.CeIdentificationSubcontractorExample;
import java.util.List;
import org.apache.ibatis.annotations.Param;

public interface CeIdentificationSubcontractorMapper {

	/**
	 * This method was generated by MyBatis Generator. This method corresponds to the database table t_ce_identification_subcontractor
	 * @mbg.generated  Mon Sep 18 15:40:16 HKT 2023
	 */
	long countByExample(CeIdentificationSubcontractorExample example);

	/**
	 * This method was generated by MyBatis Generator. This method corresponds to the database table t_ce_identification_subcontractor
	 * @mbg.generated  Mon Sep 18 15:40:16 HKT 2023
	 */
	int deleteByExample(CeIdentificationSubcontractorExample example);

	/**
	 * This method was generated by MyBatis Generator. This method corresponds to the database table t_ce_identification_subcontractor
	 * @mbg.generated  Mon Sep 18 15:40:16 HKT 2023
	 */
	int deleteByPrimaryKey(String id);

	/**
	 * This method was generated by MyBatis Generator. This method corresponds to the database table t_ce_identification_subcontractor
	 * @mbg.generated  Mon Sep 18 15:40:16 HKT 2023
	 */
	int insert(CeIdentificationSubcontractor row);

	/**
	 * This method was generated by MyBatis Generator. This method corresponds to the database table t_ce_identification_subcontractor
	 * @mbg.generated  Mon Sep 18 15:40:16 HKT 2023
	 */
	int insertSelective(CeIdentificationSubcontractor row);

	/**
	 * This method was generated by MyBatis Generator. This method corresponds to the database table t_ce_identification_subcontractor
	 * @mbg.generated  Mon Sep 18 15:40:16 HKT 2023
	 */
	List<CeIdentificationSubcontractor> selectByExample(CeIdentificationSubcontractorExample example);

	/**
	 * This method was generated by MyBatis Generator. This method corresponds to the database table t_ce_identification_subcontractor
	 * @mbg.generated  Mon Sep 18 15:40:16 HKT 2023
	 */
	CeIdentificationSubcontractor selectByPrimaryKey(String id);

	/**
	 * This method was generated by MyBatis Generator. This method corresponds to the database table t_ce_identification_subcontractor
	 * @mbg.generated  Mon Sep 18 15:40:16 HKT 2023
	 */
	int updateByExampleSelective(@Param("row") CeIdentificationSubcontractor row,
			@Param("example") CeIdentificationSubcontractorExample example);

	/**
	 * This method was generated by MyBatis Generator. This method corresponds to the database table t_ce_identification_subcontractor
	 * @mbg.generated  Mon Sep 18 15:40:16 HKT 2023
	 */
	int updateByExample(@Param("row") CeIdentificationSubcontractor row,
			@Param("example") CeIdentificationSubcontractorExample example);

	/**
	 * This method was generated by MyBatis Generator. This method corresponds to the database table t_ce_identification_subcontractor
	 * @mbg.generated  Mon Sep 18 15:40:16 HKT 2023
	 */
	int updateByPrimaryKeySelective(CeIdentificationSubcontractor row);

	/**
	 * This method was generated by MyBatis Generator. This method corresponds to the database table t_ce_identification_subcontractor
	 * @mbg.generated  Mon Sep 18 15:40:16 HKT 2023
	 */
	int updateByPrimaryKey(CeIdentificationSubcontractor row);
}