package com.csci.susdev.aspectj;

import com.csci.cohl.exception.BizException;
import com.csci.cohl.exception.CommonEnum;
import com.csci.cohl.exception.NoRuleException;
import com.csci.cohl.exception.ResultBody;
import com.csci.cohl.service.impl.TzhBsApilogServiceImpl;
import com.csci.common.exception.ServiceException;
import com.csci.susdev.constant.SusDevConsts;
import com.csci.susdev.exception.FileProcessingException;
import com.csci.susdev.exception.OcrAnalysisException;
import com.csci.susdev.exception.OcrException;
import com.csci.susdev.model.ResultBean;
import lombok.extern.slf4j.Slf4j;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.dao.DuplicateKeyException;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.http.converter.HttpMessageNotReadableException;
import org.springframework.validation.Errors;
import org.springframework.validation.FieldError;
import org.springframework.web.bind.MethodArgumentNotValidException;
import org.springframework.web.bind.annotation.ControllerAdvice;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.context.request.WebRequest;

import javax.servlet.http.HttpServletRequest;
import java.io.IOException;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.Map;
import java.util.Optional;

/**
 * 通用控制器异常处理配置
 * <p>
 *
 * <AUTHOR> terrytao
 * @date : 10/29/2019
 */
@ControllerAdvice
@Slf4j
public class GlobalExceptionHandler {
    @Autowired
    private TzhBsApilogServiceImpl digitalMapNorthernApilogService;


    /**
     * 日志记录对象
     */
    private static final Logger logger = LoggerFactory.getLogger(GlobalExceptionHandler.class);

    @ExceptionHandler({ServiceException.class, IllegalArgumentException.class, Exception.class})
    @ResponseBody
    public final ResultBean<?> handleException(Exception ex, WebRequest request) {

        logger.error("GlobalExceptionHandler#handleException, 错误记录", ex);
        if (request instanceof HttpServletRequest) {
            HttpServletRequest httpServletRequest = (HttpServletRequest) request;
            logger.error("GlobalExceptionHandler#handleException, 请求路径: {}", httpServletRequest.getRequestURI());
        }

        if (ex instanceof ServiceException || ex instanceof IllegalArgumentException) {
            return new ResultBean<>(SusDevConsts.RespStatus.FAIL, ex.getLocalizedMessage());
        } else if (ex instanceof DuplicateKeyException) {
            return new ResultBean<>(SusDevConsts.RespStatus.FAIL, "唯一键值冲突", ex.getLocalizedMessage());
        }

        // 参数验证失败的时候会抛出此异常
        if (ex instanceof MethodArgumentNotValidException) {
            StringBuilder sbErrorMsg = new StringBuilder();
            Optional.of(((MethodArgumentNotValidException) ex).getBindingResult()).map(Errors::getAllErrors).orElse(new ArrayList<>()).forEach(error -> {
                if (sbErrorMsg.length() > 0) {
                    sbErrorMsg.append(";");
                }
                sbErrorMsg.append(error.getDefaultMessage());
            });
            if (sbErrorMsg.length() == 0) {
                sbErrorMsg.append("参数验证失败");
            }
            return new ResultBean<>(SusDevConsts.RespStatus.FAIL, sbErrorMsg.toString(), ex.getLocalizedMessage());
        }

        return new ResultBean<>(SusDevConsts.RespStatus.FAIL, "出错了，请联系管理员解决");
    }
    @ExceptionHandler(MethodArgumentNotValidException.class)
    public ResponseEntity<Map<String, String>> handleValidationExceptions(MethodArgumentNotValidException ex) {
        Map<String, String> errors = new HashMap<>();
        ex.getBindingResult().getAllErrors().forEach((error) -> {
            String fieldName = ((FieldError) error).getField();
            String errorMessage = error.getDefaultMessage();
            errors.put(fieldName, errorMessage);
        });
        return new ResponseEntity<>(errors, HttpStatus.BAD_REQUEST);
    }

    @ExceptionHandler(HttpMessageNotReadableException.class)
    public ResultBean handleHttpMessageNotReadableException(HttpMessageNotReadableException ex) {
        return ResultBean.fail("请求的数据格式不正确，请检查输入的数据类型。");
    }

    /**
     * Handle OCR-specific exceptions
     */
    @ExceptionHandler(OcrException.class)
    @ResponseBody
    public ResultBean<?> handleOcrException(OcrException ex, WebRequest request) {
        logger.error("OCR异常: {}", ex.getErrorMessage(), ex);

        if (request instanceof HttpServletRequest) {
            HttpServletRequest httpServletRequest = (HttpServletRequest) request;
            logger.error("请求路径: {}", httpServletRequest.getRequestURI());
        }

        return new ResultBean<>(SusDevConsts.RespStatus.FAIL, ex.getErrorMessage(), ex.getErrorCode());
    }

    /**
     * Handle file processing exceptions
     */
    @ExceptionHandler(FileProcessingException.class)
    @ResponseBody
    public ResultBean<?> handleFileProcessingException(FileProcessingException ex, WebRequest request) {
        logger.error("文件处理异常: {}", ex.getErrorMessage(), ex);

        if (request instanceof HttpServletRequest) {
            HttpServletRequest httpServletRequest = (HttpServletRequest) request;
            logger.error("请求路径: {}", httpServletRequest.getRequestURI());
        }

        return new ResultBean<>(SusDevConsts.RespStatus.FAIL, ex.getErrorMessage(), ex.getErrorCode());
    }

    /**
     * Handle OCR analysis exceptions
     */
    @ExceptionHandler(OcrAnalysisException.class)
    @ResponseBody
    public ResultBean<?> handleOcrAnalysisException(OcrAnalysisException ex, WebRequest request) {
        logger.error("OCR分析异常: {}", ex.getErrorMessage(), ex);

        if (request instanceof HttpServletRequest) {
            HttpServletRequest httpServletRequest = (HttpServletRequest) request;
            logger.error("请求路径: {}", httpServletRequest.getRequestURI());
        }

        return new ResultBean<>(SusDevConsts.RespStatus.FAIL, ex.getErrorMessage(), ex.getErrorCode());
    }

    /**
     * 处理自定义的业务异常
     * @param req
     * @param e
     * @return
     */
    @ExceptionHandler(value = BizException.class)
    @ResponseBody
    public ResultBody bizExceptionHandler(HttpServletRequest req, BizException e){
        log.error("发生业务异常！原因是：{}",e.getErrorMsg());
        return ResultBody.error(e.getErrorCode(),e.getErrorMsg());
    }

    /**
     * 处理自定义的无权限异常
     * @param req
     * @param e
     * @return
     */
    @ExceptionHandler(value = NoRuleException.class)
    @ResponseBody
    public ResultBody noRuleExceptionHandler(HttpServletRequest req, NoRuleException e) throws IOException {
        digitalMapNorthernApilogService.log(e.getErrorMsg());
        log.error("发生无权限异常！原因是：{}",e.getErrorMsg());
        return ResultBody.error(e.getErrorCode(),e.getErrorMsg());
    }

    /**
     * 处理空指针的异常
     * @param req
     * @param e
     * @return
     */
    @ExceptionHandler(value =NullPointerException.class)
    @ResponseBody
    public ResultBody exceptionHandler(HttpServletRequest req, NullPointerException e){
        log.error("发生空指针异常！原因是:",e);
        return ResultBody.error(CommonEnum.BODY_NOT_MATCH);
    }
}
