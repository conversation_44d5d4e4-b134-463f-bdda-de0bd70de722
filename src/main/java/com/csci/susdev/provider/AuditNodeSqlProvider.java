package com.csci.susdev.provider;

import java.util.Map;
import java.util.logging.Logger;

import org.apache.commons.lang3.StringUtils;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.jdbc.SQL;

import com.csci.tzh.qo.DProjectPageableQO;

public class AuditNodeSqlProvider {

    public String listAuditNodeDetailSql(Map<String, Object> map){

    	String userId = (String) map.get("userId");
    	
    	String select = "SELECT f.code AS formCode, f.name AS formName, o.id AS organizationId, o.name AS organizationName, (\r\n"
    			+ "	SELECT _u.username FROM t_audit_node _n\r\n"
    			+ "	LEFT JOIN t_organization _o ON _o.id = _n.organization_id\r\n"
    			+ "	LEFT JOIN t_user _u ON _u.id = _n.user_id\r\n"
    			+ "	WHERE _n.form_code = n.form_code AND _n.organization_id = n.organization_id AND _n.form_id = n.form_id\r\n"
    			+ "	AND _n.seq = 1\r\n"
    			+ "	LIMIT 1\r\n"
    			+ ") AS username1, (\r\n"
    			+ "	SELECT _u.name FROM t_audit_node _n\r\n"
    			+ "	LEFT JOIN t_organization _o ON _o.id = _n.organization_id\r\n"
    			+ "	LEFT JOIN t_user _u ON _u.id = _n.user_id\r\n"
    			+ "	WHERE _n.form_code = n.form_code AND _n.organization_id = n.organization_id AND _n.form_id = n.form_id\r\n"
    			+ "	AND _n.seq = 1\r\n"
    			+ "	LIMIT 1\r\n"
    			+ ") AS userRealName1, (\r\n"
    			+ "	SELECT _u.id FROM t_audit_node _n\r\n"
    			+ "	LEFT JOIN t_organization _o ON _o.id = _n.organization_id\r\n"
    			+ "	LEFT JOIN t_user _u ON _u.id = _n.user_id\r\n"
    			+ "	WHERE _n.form_code = n.form_code AND _n.organization_id = n.organization_id AND _n.form_id = n.form_id\r\n"
    			+ "	AND _n.seq = 1\r\n"
    			+ "	LIMIT 1\r\n"
    			+ ") AS userId1, (\r\n"
    			+ "	SELECT _n.is_pass FROM t_audit_node _n\r\n"
    			+ "	LEFT JOIN t_organization _o ON _o.id = _n.organization_id\r\n"
    			+ "	LEFT JOIN t_user _u ON _u.id = _n.user_id\r\n"
    			+ "	WHERE _n.form_code = n.form_code AND _n.organization_id = n.organization_id AND _n.form_id = n.form_id\r\n"
    			+ "	AND _n.seq = 1\r\n"
    			+ "	LIMIT 1\r\n"
    			+ ") AS isPass1, (\r\n"
    			+ "	SELECT _n.remark FROM t_audit_node _n\r\n"
    			+ "	LEFT JOIN t_organization _o ON _o.id = _n.organization_id\r\n"
    			+ "	LEFT JOIN t_user _u ON _u.id = _n.user_id\r\n"
    			+ "	WHERE _n.form_code = n.form_code AND _n.organization_id = n.organization_id AND _n.form_id = n.form_id\r\n"
    			+ "	AND _n.seq = 1\r\n"
    			+ "	LIMIT 1\r\n"
    			+ ") AS remark1, (\r\n"
    			+ "	SELECT _u.username FROM t_audit_node _n\r\n"
    			+ "	LEFT JOIN t_organization _o ON _o.id = _n.organization_id\r\n"
    			+ "	LEFT JOIN t_user _u ON _u.id = _n.user_id\r\n"
    			+ "	WHERE _n.form_code = n.form_code AND _n.organization_id = n.organization_id AND _n.form_id = n.form_id\r\n"
    			+ "	AND _n.seq = 2\r\n"
    			+ "	LIMIT 1\r\n"
    			+ ") AS username2, (\r\n"
    			+ "	SELECT _u.name FROM t_audit_node _n\r\n"
    			+ "	LEFT JOIN t_organization _o ON _o.id = _n.organization_id\r\n"
    			+ "	LEFT JOIN t_user _u ON _u.id = _n.user_id\r\n"
    			+ "	WHERE _n.form_code = n.form_code AND _n.organization_id = n.organization_id AND _n.form_id = n.form_id\r\n"
    			+ "	AND _n.seq = 2\r\n"
    			+ "	LIMIT 1\r\n"
    			+ ") AS userRealName2, (\r\n"
    			+ "	SELECT _u.id FROM t_audit_node _n\r\n"
    			+ "	LEFT JOIN t_organization _o ON _o.id = _n.organization_id\r\n"
    			+ "	LEFT JOIN t_user _u ON _u.id = _n.user_id\r\n"
    			+ "	WHERE _n.form_code = n.form_code AND _n.organization_id = n.organization_id AND _n.form_id = n.form_id\r\n"
    			+ "	AND _n.seq = 2\r\n"
    			+ "	LIMIT 1\r\n"
    			+ ") AS userId2, (\r\n"
    			+ "	SELECT _n.is_pass FROM t_audit_node _n\r\n"
    			+ "	LEFT JOIN t_organization _o ON _o.id = _n.organization_id\r\n"
    			+ "	LEFT JOIN t_user _u ON _u.id = _n.user_id\r\n"
    			+ "	WHERE _n.form_code = n.form_code AND _n.organization_id = n.organization_id AND _n.form_id = n.form_id\r\n"
    			+ "	AND _n.seq = 2\r\n"
    			+ "	LIMIT 1\r\n"
    			+ ") AS isPass2, (\r\n"
    			+ "	SELECT _n.remark FROM t_audit_node _n\r\n"
    			+ "	LEFT JOIN t_organization _o ON _o.id = _n.organization_id\r\n"
    			+ "	LEFT JOIN t_user _u ON _u.id = _n.user_id\r\n"
    			+ "	WHERE _n.form_code = n.form_code AND _n.organization_id = n.organization_id AND _n.form_id = n.form_id\r\n"
    			+ "	AND _n.seq = 2\r\n"
    			+ "	LIMIT 1\r\n"
    			+ ") AS remark2, (\r\n"
    			+ "	SELECT _u.username FROM t_audit_node _n\r\n"
    			+ "	LEFT JOIN t_organization _o ON _o.id = _n.organization_id\r\n"
    			+ "	LEFT JOIN t_user _u ON _u.id = _n.user_id\r\n"
    			+ "	WHERE _n.form_code = n.form_code AND _n.organization_id = n.organization_id AND _n.form_id = n.form_id\r\n"
    			+ "	AND _n.seq = 3\r\n"
    			+ "	LIMIT 1\r\n"
    			+ ") AS username3, (\r\n"
    			+ "	SELECT _u.name FROM t_audit_node _n\r\n"
    			+ "	LEFT JOIN t_organization _o ON _o.id = _n.organization_id\r\n"
    			+ "	LEFT JOIN t_user _u ON _u.id = _n.user_id\r\n"
    			+ "	WHERE _n.form_code = n.form_code AND _n.organization_id = n.organization_id AND _n.form_id = n.form_id\r\n"
    			+ "	AND _n.seq = 3\r\n"
    			+ "	LIMIT 1\r\n"
    			+ ") AS userRealName3, (\r\n"
    			+ "	SELECT _u.id FROM t_audit_node _n\r\n"
    			+ "	LEFT JOIN t_organization _o ON _o.id = _n.organization_id\r\n"
    			+ "	LEFT JOIN t_user _u ON _u.id = _n.user_id\r\n"
    			+ "	WHERE _n.form_code = n.form_code AND _n.organization_id = n.organization_id AND _n.form_id = n.form_id\r\n"
    			+ "	AND _n.seq = 3\r\n"
    			+ "	LIMIT 1\r\n"
    			+ ") AS userId3, (\r\n"
    			+ "	SELECT _n.is_pass FROM t_audit_node _n\r\n"
    			+ "	LEFT JOIN t_organization _o ON _o.id = _n.organization_id\r\n"
    			+ "	LEFT JOIN t_user _u ON _u.id = _n.user_id\r\n"
    			+ "	WHERE _n.form_code = n.form_code AND _n.organization_id = n.organization_id AND _n.form_id = n.form_id\r\n"
    			+ "	AND _n.seq = 3\r\n"
    			+ "	LIMIT 1\r\n"
    			+ ") AS isPass3, (\r\n"
    			+ "	SELECT _n.remark FROM t_audit_node _n\r\n"
    			+ "	LEFT JOIN t_organization _o ON _o.id = _n.organization_id\r\n"
    			+ "	LEFT JOIN t_user _u ON _u.id = _n.user_id\r\n"
    			+ "	WHERE _n.form_code = n.form_code AND _n.organization_id = n.organization_id AND _n.form_id = n.form_id\r\n"
    			+ "	AND _n.seq = 3\r\n"
    			+ "	LIMIT 1\r\n"
    			+ ") AS remark3, (\r\n"
    			+ "	SELECT _u.username FROM t_audit_node _n\r\n"
    			+ "	LEFT JOIN t_organization _o ON _o.id = _n.organization_id\r\n"
    			+ "	LEFT JOIN t_user _u ON _u.id = _n.user_id\r\n"
    			+ "	WHERE _n.form_code = n.form_code AND _n.organization_id = n.organization_id AND _n.form_id = n.form_id\r\n"
    			+ "	AND _n.seq = 4\r\n"
    			+ "	LIMIT 1\r\n"
    			+ ") AS username4, (\r\n"
    			+ "	SELECT _u.name FROM t_audit_node _n\r\n"
    			+ "	LEFT JOIN t_organization _o ON _o.id = _n.organization_id\r\n"
    			+ "	LEFT JOIN t_user _u ON _u.id = _n.user_id\r\n"
    			+ "	WHERE _n.form_code = n.form_code AND _n.organization_id = n.organization_id AND _n.form_id = n.form_id\r\n"
    			+ "	AND _n.seq = 4\r\n"
    			+ "	LIMIT 1\r\n"
    			+ ") AS userRealName4, (\r\n"
    			+ "	SELECT _u.id FROM t_audit_node _n\r\n"
    			+ "	LEFT JOIN t_organization _o ON _o.id = _n.organization_id\r\n"
    			+ "	LEFT JOIN t_user _u ON _u.id = _n.user_id\r\n"
    			+ "	WHERE _n.form_code = n.form_code AND _n.organization_id = n.organization_id AND _n.form_id = n.form_id\r\n"
    			+ "	AND _n.seq = 4\r\n"
    			+ "	LIMIT 1\r\n"
    			+ ") AS userId4, (\r\n"
    			+ "	SELECT _n.is_pass FROM t_audit_node _n\r\n"
    			+ "	LEFT JOIN t_organization _o ON _o.id = _n.organization_id\r\n"
    			+ "	LEFT JOIN t_user _u ON _u.id = _n.user_id\r\n"
    			+ "	WHERE _n.form_code = n.form_code AND _n.organization_id = n.organization_id AND _n.form_id = n.form_id\r\n"
    			+ "	AND _n.seq = 4\r\n"
    			+ "	LIMIT 1\r\n"
    			+ ") AS isPass4, (\r\n"
    			+ "	SELECT _n.remark FROM t_audit_node _n\r\n"
    			+ "	LEFT JOIN t_organization _o ON _o.id = _n.organization_id\r\n"
    			+ "	LEFT JOIN t_user _u ON _u.id = _n.user_id\r\n"
    			+ "	WHERE _n.form_code = n.form_code AND _n.organization_id = n.organization_id AND _n.form_id = n.form_id\r\n"
    			+ "	AND _n.seq = 4\r\n"
    			+ "	LIMIT 1\r\n"
    			+ ") AS remark4\r\n"
    			+ "FROM t_audit_node n\r\n"
    			+ "LEFT JOIN t_form f ON f.code = n.form_code\r\n"
    			+ "LEFT JOIN t_organization o ON o.id = n.organization_id\r\n";
    	String where = "WHERE seq = 1 \r\n"
    			+ "AND o.id IN (SELECT _uo.organization_id FROM t_user_organization _uo WHERE _uo.user_id = '" + userId + "' OR '' = '" + userId + "') \r\n";
    	String orderBy = "ORDER BY formCode, organizationName";
        return select + where + orderBy;
    }
}