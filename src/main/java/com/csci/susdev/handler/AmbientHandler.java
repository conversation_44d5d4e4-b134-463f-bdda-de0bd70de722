package com.csci.susdev.handler;

import com.alibaba.excel.metadata.Head;
import com.alibaba.excel.write.merge.AbstractMergeStrategy;
import com.csci.common.util.CustomGsonBuilder;
import com.csci.susdev.service.AmbientDetailService;
import com.csci.susdev.util.SpringContextUtil;
import com.csci.susdev.vo.AmbientDetailVO;
import com.csci.susdev.vo.MergeDataVO;
import com.google.gson.Gson;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.poi.ss.usermodel.Cell;
import org.apache.poi.ss.usermodel.Sheet;
import org.apache.poi.ss.util.CellRangeAddress;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

public class AmbientHandler extends AbstractMergeStrategy {

    /**
     * 日志记录对象
     */
    private static final Logger logger = LoggerFactory.getLogger(AmbientHandler.class);
    private static final List<String> thirdColMergeValueList = new ArrayList<>();

    static {
        thirdColMergeValueList.add("地盤/工地、工廠、辦公室及發電廠能源消耗");
        thirdColMergeValueList.add("物料資源");
        thirdColMergeValueList.add("皇姑發電廠固定燃料燃燒");
        thirdColMergeValueList.add("皇姑發電廠物料資源");
        thirdColMergeValueList.add("皇姑發電廠能源消耗");
        thirdColMergeValueList.add("皇姑發電廠廢氣排放");
        thirdColMergeValueList.add("不可再生物料-混凝土");
    }

    private static final Gson gson = CustomGsonBuilder.createGson();
    private List<MergeDataVO> mergeDataList = new ArrayList<>();

    public AmbientHandler(String headId) {
        AmbientDetailService ambientDetailService = SpringContextUtil.getBean(AmbientDetailService.class);
        List<AmbientDetailVO> lstAmbient = ambientDetailService.listAmbientDetailByHeadId(headId);

        mergeDataList.addAll(calcFirstCol(lstAmbient));
        mergeDataList.addAll(calcThirdCol(lstAmbient));

        // 过滤掉同行的数据
        mergeDataList = mergeDataList.stream().filter(x -> !Objects.equals(x.getFromRow(), x.getToRow())).collect(Collectors.toList());
    }

    public static List<MergeDataVO> calcFirstCol(List<AmbientDetailVO> lstAmbient) {
        List<MergeDataVO> lstMergeData = new ArrayList<>();
        MergeDataVO mergeDataVO = null;
        int currentRowIndex = 0;
        for (int i = 0; i < lstAmbient.size(); i++) {
            AmbientDetailVO ambientDetailVO = lstAmbient.get(i);
            // 第一列合并计算----------start
            if (i == 0) {
                // 第一行
                currentRowIndex = 2;
                mergeDataVO = new MergeDataVO().setFromCol(0).setToCol(0).setFromRow(currentRowIndex).setToRow(currentRowIndex).setValue(ambientDetailVO.getCategory());
                lstMergeData.add(mergeDataVO);
                continue;
            }
            currentRowIndex++;
            if (StringUtils.isNotBlank(ambientDetailVO.getCategory()) && !StringUtils.equals(mergeDataVO.getValue(), ambientDetailVO.getCategory())) {
                // 不同类别
                mergeDataVO = new MergeDataVO().setFromCol(0).setToCol(0).setFromRow(currentRowIndex).setToRow(currentRowIndex).setValue(ambientDetailVO.getCategory());
                lstMergeData.add(mergeDataVO);
                continue;
            }

            mergeDataVO.setToRow(mergeDataVO.getToRow() + 1);
            // 第一列合并计算----------end
        }

        return lstMergeData.stream().filter(x -> !Objects.equals(x.getFromRow(), x.getToRow())).collect(Collectors.toList());
    }

    /**
     * 暂时不适用
     *
     * @param lstAmbient
     */
    public static List<MergeDataVO> calcThirdCol(List<AmbientDetailVO> lstAmbient) {
        if (CollectionUtils.isEmpty(lstAmbient)) {
            return new ArrayList<>();
        }
        int currentRow = 0;
        MergeDataVO mergeDataVO = null;
        List<MergeDataVO> mergeDataList = new ArrayList<>();
        String lastCategory = null;
        for (int i = 0; i < lstAmbient.size(); i++) {
            AmbientDetailVO ambientDetailVO = lstAmbient.get(i);

            if (i == 0) {
                currentRow = 2;
                mergeDataVO = new MergeDataVO().setFromCol(2).setToCol(2).setFromRow(currentRow).setToRow(currentRow).setValue(ambientDetailVO.getType2());
                mergeDataList.add(mergeDataVO);
                lastCategory = ambientDetailVO.getCategory();
                continue;
            }
            currentRow++;
            if (StringUtils.isNotBlank(ambientDetailVO.getType2()) && !StringUtils.equals(ambientDetailVO.getType2(), mergeDataVO.getValue())) {
                mergeDataVO = new MergeDataVO().setFromCol(2).setToCol(2).setFromRow(currentRow).setToRow(currentRow).setValue(ambientDetailVO.getType2());
                mergeDataList.add(mergeDataVO);
                lastCategory = ambientDetailVO.getCategory();
                continue;
            }

            if (!thirdColMergeValueList.contains(StringUtils.trim(lastCategory))) {
                continue;
            }
            if (Objects.nonNull(mergeDataVO)) {
                mergeDataVO.setToRow(mergeDataVO.getToRow() + 1);
            }
        }

        return mergeDataList.stream().filter(x -> !Objects.equals(x.getFromRow(), x.getToRow())).collect(Collectors.toList());
    }

    @Override
    protected void merge(Sheet sheet, Cell cell, Head head, Integer relativeRowIndex) {
        if (CollectionUtils.isNotEmpty(mergeDataList)) {
            for (MergeDataVO mergeDataVO : mergeDataList) {
                if (cell.getRowIndex() == mergeDataVO.getFromRow() && cell.getColumnIndex() == mergeDataVO.getFromCol()) {
                    sheet.addMergedRegion(new CellRangeAddress(mergeDataVO.getFromRow(), mergeDataVO.getToRow(), mergeDataVO.getFromCol(), mergeDataVO.getToCol()));
                    break;
                }
            }
        }
    }
}
