package com.csci.susdev.modelcovt;

import com.csci.susdev.model.Permission;
import com.csci.susdev.vo.PermissionVO;
import org.springframework.beans.BeanUtils;

public class PermissionVOConverter {
    public static PermissionVO convert(Permission source) {
        PermissionVO permissionVO = new PermissionVO();
        BeanUtils.copyProperties(source, permissionVO);
        return permissionVO;
    }

    public static Permission convert(PermissionVO source) {
        Permission permission = new Permission();
        BeanUtils.copyProperties(source, permission);
        return permission;
    }
}
