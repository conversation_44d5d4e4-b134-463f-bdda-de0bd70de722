package com.csci.susdev.modelcovt;

import com.csci.susdev.model.SocialPerfTwoDetail;
import com.csci.susdev.vo.Classification;
import com.csci.susdev.vo.SocialPerfTwoTableDataVO;
import org.springframework.beans.BeanUtils;

public class SocialPerfTwoTableDataConverter {

    public static SocialPerfTwoTableDataVO convert(SocialPerfTwoDetail source) {
        SocialPerfTwoTableDataVO socialPerfTwoTableDataVO = new SocialPerfTwoTableDataVO();
        BeanUtils.copyProperties(source, socialPerfTwoTableDataVO);
        Classification classification = new Classification();
        BeanUtils.copyProperties(source, classification);
        socialPerfTwoTableDataVO.setClassification(classification);
        return socialPerfTwoTableDataVO;
    }

    public static SocialPerfTwoDetail convert(SocialPerfTwoTableDataVO source) {
        SocialPerfTwoDetail socialPerfTwoDetail = new SocialPerfTwoDetail();
        BeanUtils.copyProperties(source, socialPerfTwoDetail);
        BeanUtils.copyProperties(source.getClassification(), socialPerfTwoDetail);
        return socialPerfTwoDetail;
    }

}
