package com.csci.susdev.modelcovt;

import com.csci.susdev.model.SocialPerfThreeDetail;
import com.csci.susdev.vo.Classification;
import com.csci.susdev.vo.SocialPerfThreeTableDataVO;
import org.springframework.beans.BeanUtils;

public class SocialPerfThreeTableDataConverter {

    public static SocialPerfThreeTableDataVO convert(SocialPerfThreeDetail source) {
        SocialPerfThreeTableDataVO socialPerfThreeTableDataVO = new SocialPerfThreeTableDataVO();
        BeanUtils.copyProperties(source, socialPerfThreeTableDataVO);
        Classification classification = new Classification();
        BeanUtils.copyProperties(source, classification);
        socialPerfThreeTableDataVO.setClassification(classification);
        return socialPerfThreeTableDataVO;
    }

    public static SocialPerfThreeDetail convert(SocialPerfThreeTableDataVO source) {
        SocialPerfThreeDetail socialPerfThreeDetail = new SocialPerfThreeDetail();
        BeanUtils.copyProperties(source, socialPerfThreeDetail);
        BeanUtils.copyProperties(source.getClassification(), socialPerfThreeDetail);
        return socialPerfThreeDetail;
    }

}
