package com.csci.susdev.modelcovt;

import com.csci.susdev.constant.ApproveStatus;
import com.csci.susdev.model.FfCmMobileHead;
import com.csci.susdev.vo.FfCmMobileHeadVO;
import org.springframework.beans.BeanUtils;

public class FfCmMobileHeadConverter {

    static String getApproveStatusName(Integer approveStatus) {
        ApproveStatus[] approveStatuses = ApproveStatus.values();
        for (ApproveStatus status : approveStatuses) {
            if (status.getCode().equals(approveStatus)) {
                return status.getName();
            }
        }
        return "未知状态";
    }

    public static FfCmMobileHeadVO convert(FfCmMobileHead source) {
        FfCmMobileHeadVO ffCmMobileHeadVO = new FfCmMobileHeadVO();
        BeanUtils.copyProperties(source, ffCmMobileHeadVO);
        return ffCmMobileHeadVO;
    }

    public static FfCmMobileHead convert(FfCmMobileHeadVO source) {
        FfCmMobileHead ffCmMobileHead = new FfCmMobileHead();
        BeanUtils.copyProperties(source, ffCmMobileHead);
        return ffCmMobileHead;
    }

}
