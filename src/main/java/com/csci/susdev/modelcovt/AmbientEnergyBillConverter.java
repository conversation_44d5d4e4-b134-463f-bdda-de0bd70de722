package com.csci.susdev.modelcovt;

import com.csci.susdev.model.AmbientEnergyBill;
import com.csci.susdev.vo.AmbientEnergyBillVO;
import org.springframework.beans.BeanUtils;

public class AmbientEnergyBillConverter {

    public static AmbientEnergyBillVO convert(AmbientEnergyBill ambientEnergyBill) {
        AmbientEnergyBillVO ambientEnergyBillVO = new AmbientEnergyBillVO();
        BeanUtils.copyProperties(ambientEnergyBill, ambientEnergyBillVO);
        return ambientEnergyBillVO;
    }

    public static AmbientEnergyBill convert(AmbientEnergyBillVO ambientEnergyBillVO) {
        AmbientEnergyBill ambientEnergyBill = new AmbientEnergyBill();
        BeanUtils.copyProperties(ambientEnergyBillVO, ambientEnergyBill);
        return ambientEnergyBill;
    }

}
