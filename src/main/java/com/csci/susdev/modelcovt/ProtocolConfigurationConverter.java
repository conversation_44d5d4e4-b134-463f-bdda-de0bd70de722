package com.csci.susdev.modelcovt;

import com.csci.susdev.model.*;
import com.csci.susdev.service.*;
import com.csci.susdev.util.SpringContextUtil;
import com.csci.susdev.vo.FactorScopeVO;
import com.csci.susdev.vo.FactorSelectionVO;
import com.csci.susdev.vo.ProtocolConfigurationVO;
import com.csci.susdev.vo.ProtocolManagementVO;
import org.apache.commons.lang3.ObjectUtils;
import org.springframework.beans.BeanUtils;

public class ProtocolConfigurationConverter {

    public static ProtocolConfigurationVO fillInDetail(ProtocolConfigurationVO vo) {
        ProtocolConfigurationVO protocolConfigurationVO = new ProtocolConfigurationVO();
        BeanUtils.copyProperties(vo, protocolConfigurationVO);

        //配置因子管理
        switch (protocolConfigurationVO.getFcFactorType()) {
            case "物料因子":
                FcMaterialFactorService fcMaterialFactorService = SpringContextUtil.getBean(FcMaterialFactorService.class);
                FcMaterialFactor fcMaterialFactor = fcMaterialFactorService.selectByPrimaryKey(vo.getFcFactorId());
                protocolConfigurationVO.setFcFactorVO(FcMaterialFactorConverter.convertToVO(fcMaterialFactor));
                break;
            case "能源因子":
                FcEnergyFactorService fcEnergyFactorService = SpringContextUtil.getBean(FcEnergyFactorService.class);
                FcEnergyFactor fcEnergyFactor = fcEnergyFactorService.selectByPrimaryKey(vo.getFcFactorId());
                protocolConfigurationVO.setFcFactorVO(FcEnergyFactorConverter.convertToVO(fcEnergyFactor));
                break;
            default:
                break;
        }

        return protocolConfigurationVO;
    }

    public static ProtocolConfigurationVO convertToVO(ProtocolConfiguration protocolConfiguration) {
        ProtocolConfigurationVO protocolConfigurationVO = new ProtocolConfigurationVO();
        BeanUtils.copyProperties(protocolConfiguration, protocolConfigurationVO);

        // 协议管理
        ProtocolManagementService protocolManagementService = SpringContextUtil.getBean(ProtocolManagementService.class);
        ProtocolManagementVO protocolManagementVO = protocolManagementService.getBySubCategoryId(protocolConfiguration.getSubCategoryId());


        protocolConfigurationVO.setSubCategoryId(protocolConfiguration.getSubCategoryId());
        protocolConfigurationVO.setSubCategoryName(protocolManagementVO.getSubCategoryName());
        protocolConfigurationVO.setSubCategoryNameSc(protocolManagementVO.getSubCategoryNameSc());
        protocolConfigurationVO.setSubCategoryNameEn(protocolManagementVO.getSubCategoryNameEn());

        protocolConfigurationVO.setCategoryId(protocolManagementVO.getCategoryId());
        protocolConfigurationVO.setCategoryName(protocolManagementVO.getCategoryName());
        protocolConfigurationVO.setCategoryNameSc(protocolManagementVO.getCategoryNameSc());
        protocolConfigurationVO.setCategoryNameEn(protocolManagementVO.getCategoryNameEn());

        protocolConfigurationVO.setProtocolId(protocolManagementVO.getProtocolId());
        protocolConfigurationVO.setProtocolName(protocolManagementVO.getProtocolName());
        protocolConfigurationVO.setProtocolNameSc(protocolManagementVO.getProtocolNameSc());
        protocolConfigurationVO.setProtocolNameEn(protocolManagementVO.getProtocolNameEn());

        // 表单明细
        FormDetailService formDetailService = SpringContextUtil.getBean(FormDetailService.class);
        FormDetail formDetail = formDetailService.selectByPrimaryKey(protocolConfiguration.getFormDetailId());
        protocolConfigurationVO.setFormDetailId(protocolConfiguration.getFormDetailId());

        protocolConfigurationVO.setFormCode(formDetail.getFormCode());
        protocolConfigurationVO.setYear(formDetail.getYear());
        protocolConfigurationVO.setTypeA(formDetail.getTypeA());
        protocolConfigurationVO.setTypeB(formDetail.getTypeB());
        protocolConfigurationVO.setTypeC(formDetail.getTypeC());
        protocolConfigurationVO.setTypeD(formDetail.getTypeD());
        protocolConfigurationVO.setTypeE(formDetail.getTypeE());


        //配置因子管理
        switch (protocolConfigurationVO.getFcFactorType()) {
            case "物料因子":
                FcMaterialFactorService fcMaterialFactorService = SpringContextUtil.getBean(FcMaterialFactorService.class);
                FcMaterialFactor fcMaterialFactor = fcMaterialFactorService.selectByPrimaryKey(protocolConfiguration.getFcFactorId());
                protocolConfigurationVO.setFcFactorVO(FcMaterialFactorConverter.convertToVO(fcMaterialFactor));
                break;
            case "能源因子":
                FcEnergyFactorService fcEnergyFactorService = SpringContextUtil.getBean(FcEnergyFactorService.class);
                FcEnergyFactor fcEnergyFactor = fcEnergyFactorService.selectByPrimaryKey(protocolConfiguration.getFcFactorId());
                protocolConfigurationVO.setFcFactorVO(FcEnergyFactorConverter.convertToVO(fcEnergyFactor));
                break;
            default:
                break;
        }

        return protocolConfigurationVO;
    }

    public static ProtocolConfiguration convertToModel(ProtocolConfigurationVO protocolConfigurationVO) {
        ProtocolConfiguration protocolConfiguration = new ProtocolConfiguration();
        BeanUtils.copyProperties(protocolConfigurationVO, protocolConfiguration);
        return protocolConfiguration;
    }

    public static ProtocolConfiguration convertToModelWithBase(
            ProtocolConfigurationVO protocolConfigurationVO,
            ProtocolConfiguration protocolConfiguration) {
        BeanUtils.copyProperties(protocolConfigurationVO, protocolConfiguration);
        return protocolConfiguration;
    }

}
