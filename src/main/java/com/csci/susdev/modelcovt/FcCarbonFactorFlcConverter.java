package com.csci.susdev.modelcovt;

import com.csci.susdev.model.FcCarbonFactorFlc;
import com.csci.susdev.vo.FcCarbonFactorFlcVO;
import org.springframework.beans.BeanUtils;

public class FcCarbonFactorFlcConverter {

    public static FcCarbonFactorFlcVO convertToVO(FcCarbonFactorFlc fcCarbonFactorFlc) {
        FcCarbonFactorFlcVO fcCarbonFactorFlcVO = new FcCarbonFactorFlcVO();
        BeanUtils.copyProperties(fcCarbonFactorFlc, fcCarbonFactorFlcVO);
        return fcCarbonFactorFlcVO;
    }

    public static FcCarbonFactorFlc convertToModel(FcCarbonFactorFlcVO fcCarbonFactorFlcVO) {
        FcCarbonFactorFlc fcCarbonFactorFlc = new FcCarbonFactorFlc();
        BeanUtils.copyProperties(fcCarbonFactorFlcVO, fcCarbonFactorFlc);
        return fcCarbonFactorFlc;
    }

    public static FcCarbonFactorFlc convertToModelWithBase(
            FcCarbonFactorFlcVO fcCarbonFactorFlcVO,
            FcCarbonFactorFlc fcCarbonFactorFlc) {
        BeanUtils.copyProperties(fcCarbonFactorFlcVO, fcCarbonFactorFlc);
        return fcCarbonFactorFlc;
    }

}
