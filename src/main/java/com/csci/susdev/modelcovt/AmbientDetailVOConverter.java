package com.csci.susdev.modelcovt;

import com.csci.susdev.model.AmbientDetail;
import com.csci.susdev.vo.AmbientDetailVO;
import org.springframework.beans.BeanUtils;

public class AmbientDetailVOConverter {

    public static AmbientDetailVO convert(AmbientDetail ambientDetail) {
        AmbientDetailVO ambientDetailVO = new AmbientDetailVO();
        BeanUtils.copyProperties(ambientDetail, ambientDetailVO);
        return ambientDetailVO;
    }

    public static AmbientDetail convert(AmbientDetailVO ambientDetailVO) {
        AmbientDetail ambientDetail = new AmbientDetail();
        BeanUtils.copyProperties(ambientDetailVO, ambientDetail);
        return ambientDetail;
    }

}
