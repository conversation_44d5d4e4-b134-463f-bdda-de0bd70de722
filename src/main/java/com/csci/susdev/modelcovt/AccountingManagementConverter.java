package com.csci.susdev.modelcovt;

import com.csci.susdev.model.AccountingManagement;
import com.csci.susdev.vo.AccountingManagementVO;
import org.springframework.beans.BeanUtils;

public class AccountingManagementConverter {

    public static AccountingManagementVO convertToVO(AccountingManagement accountingManagement) {
        AccountingManagementVO accountingManagementVO = new AccountingManagementVO();
        BeanUtils.copyProperties(accountingManagement, accountingManagementVO);
        return accountingManagementVO;
    }

    public static AccountingManagement convertToModel(AccountingManagementVO accountingManagementVO) {
        AccountingManagement accountingManagement = new AccountingManagement();
        BeanUtils.copyProperties(accountingManagementVO, accountingManagement);
        return accountingManagement;
    }

    public static AccountingManagement convertToModelWithBase(
            AccountingManagementVO accountingManagementVO,
            AccountingManagement accountingManagement) {
        BeanUtils.copyProperties(accountingManagementVO, accountingManagement);
        return accountingManagement;
    }

}
