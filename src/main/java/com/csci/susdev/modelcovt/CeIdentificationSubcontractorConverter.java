package com.csci.susdev.modelcovt;

import com.csci.susdev.model.CeIdentificationSubcontractor;
import com.csci.susdev.vo.CeIdentificationSubcontractorVO;
import org.springframework.beans.BeanUtils;

public class CeIdentificationSubcontractorConverter {

    public static CeIdentificationSubcontractorVO convert(CeIdentificationSubcontractor ceIdentificationSubcontractor) {
        CeIdentificationSubcontractorVO ceIdentificationSubcontractorVO = new CeIdentificationSubcontractorVO();
        BeanUtils.copyProperties(ceIdentificationSubcontractor, ceIdentificationSubcontractorVO);
        return ceIdentificationSubcontractorVO;
    }

    public static CeIdentificationSubcontractor convert(CeIdentificationSubcontractorVO ceIdentificationSubcontractorVO) {
        CeIdentificationSubcontractor ceIdentificationSubcontractor = new CeIdentificationSubcontractor();
        BeanUtils.copyProperties(ceIdentificationSubcontractorVO, ceIdentificationSubcontractor);
        return ceIdentificationSubcontractor;
    }

}
