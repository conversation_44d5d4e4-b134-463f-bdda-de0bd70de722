package com.csci.susdev.modelcovt;

import com.csci.susdev.model.FcEnergyFactor;
import com.csci.susdev.vo.FcEnergyFactorVO;
import org.springframework.beans.BeanUtils;

public class FcEnergyFactorConverter {

    public static FcEnergyFactorVO convertToVO(FcEnergyFactor fcEnergyFactor) {
        FcEnergyFactorVO fcEnergyFactorVO = new FcEnergyFactorVO();
        BeanUtils.copyProperties(fcEnergyFactor, fcEnergyFactorVO);
        return fcEnergyFactorVO;
    }

    public static FcEnergyFactor convertToModel(FcEnergyFactorVO fcEnergyFactorVO) {
        FcEnergyFactor FcEnergyFactor = new FcEnergyFactor();
        BeanUtils.copyProperties(fcEnergyFactorVO, FcEnergyFactor);
        return FcEnergyFactor;
    }

    public static FcEnergyFactor convertToModelWithBase(
            FcEnergyFactorVO fcEnergyFactorVO,
            FcEnergyFactor fcEnergyFactor) {
        BeanUtils.copyProperties(fcEnergyFactorVO, fcEnergyFactor);
        return fcEnergyFactor;
    }

}
