package com.csci.susdev.facade;

import com.csci.susdev.model.Organization;
import com.csci.susdev.model.OrganizationExample;
import com.csci.susdev.service.OrganizationService;
import com.csci.susdev.service.UserService;
import com.csci.susdev.vo.OrganizationVO;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;

import static com.csci.susdev.service.ServiceHelper.checkExist;

@Component
public class UserFacade {

    @Resource
    private UserService userService;

    @Resource
    private OrganizationService organizationService;

    /**
     * 将指定的组织机构及其子部门权限添加给指定的用户
     *
     * @param orgId
     * @param userId
     */
    public void addOrgWithChildToUser(String orgId, String userId) {
        checkExist(userId, "userId不能为空");
        checkExist(orgId, "orgId不能为空");
        OrganizationVO organizationVO = organizationService.getOrganizationById(orgId);
        checkExist(organizationVO, "未找到指定的组织机构");
        OrganizationExample example = new OrganizationExample();
        example.or().andIsDeletedEqualTo(Boolean.FALSE).andNoLike(organizationVO.getNo() + "%");
        example.setOrderByClause("no");
        List<Organization> lstOrganization = organizationService.selectByExample(example);

        for (Organization organization : lstOrganization) {
            userService.addOrgToUser(userId, organization.getId());
        }
    }
}
