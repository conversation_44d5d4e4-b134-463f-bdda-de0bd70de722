package com.csci.susdev.facade;

import com.csci.common.exception.ServiceException;
import com.csci.susdev.constant.SusDevConsts;
import com.csci.susdev.constant.WorkflowControlState;
import com.csci.susdev.constant.WorkflowOperateType;
import com.csci.susdev.model.*;
import com.csci.susdev.modelcovt.WorkflowVOConverter;
import com.csci.susdev.service.*;
import com.csci.susdev.util.DateUtils;
import com.csci.susdev.util.redis.RedisLockUtil;
import com.csci.susdev.util.SpringContextUtil;
import com.csci.susdev.util.context.ContextUtils;
import com.csci.susdev.util.context.model.UserInfo;
import com.csci.susdev.vo.ApproveVO;
import com.csci.susdev.vo.WorkflowNodeVO;
import com.csci.susdev.vo.WorkflowVO;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

import static com.csci.susdev.service.ServiceHelper.addOrgToUser;
import static com.csci.susdev.service.ServiceHelper.checkExist;

@Component
public class WorkflowFacade {

    /**
     * 日志记录对象
     */
    private static final Logger logger = LoggerFactory.getLogger(WorkflowFacade.class);

    @Resource
    private WorkflowService workflowService;

    @Resource
    private WorkflowNodeService workflowNodeService;

    @Resource
    private WorkflowControlService workflowControlService;

    @Resource
    private WorkflowLogsService workflowlogsService;

    @Resource
    private UserService userService;

    @Resource
    private WorkflowNodeUserService workflowNodeUserService;

    @Resource
    private OrganizationService organizationService;

    @Resource
    private AmbientService ambientService;

    private static void checkApproveVO(ApproveVO approveVO) {
        checkExist(approveVO.getOrganizationId(), "组织id不能为空");
        checkExist(approveVO.getFormId(), "表单id不能为空");
        checkExist(approveVO.getBusinessId(), "业务id不能为空");
    }

    /**
     * 发起流程
     *
     * @param organizationId 组织id
     * @param formId         表单id
     * @param businessId     业务id
     */
    @Transactional(rollbackFor = Exception.class)
    public void startWorkflow(String organizationId, String formId, String businessId) {
        checkExist(organizationId, "组织id不能为空");
        checkExist(formId, "表单id不能爲空");
        checkExist(businessId, "业务ID不能为空");

        Integer year = workflowControlService.findFormYearByBusinessId(businessId);
        Integer month = workflowControlService.findFormMonthByBusinessId(businessId);
        if(Objects.isNull(year)) {
            throw new ServiceException("找不到相關表單年份");
        }
        if(Objects.isNull(month)) {
            throw new ServiceException("找不到相關表單月份");
        }

        Workflow workflow = workflowService.findWorkflowByOrgIdAndFormId(organizationId, formId, year, month);
        checkExist(workflow, "审批流程未設置");

        if(StringUtils.isNotBlank(workflow.getRepresentativeId())) {
            UserInfo curUserInfo = ContextUtils.getCurrentUser();
            if(!workflow.getRepresentativeId().equals(curUserInfo.getId())) {
                throw new ServiceException("沒有提交權限");
            }
        }


        String key = SusDevConsts.PROJECT_PREFIX + organizationId + formId + businessId;
        try {
            if (RedisLockUtil.lock(key)) {
                doStartWorkflow(organizationId, formId, businessId, year, month);
            }
        } finally {
            RedisLockUtil.unlock(key);
        }
    }

    @Transactional(rollbackFor = Exception.class)
    public void approve(String businessId, String remark) {
        checkExist(businessId, "业务ID不能为空");
        WorkflowControl workflowControl = workflowControlService.findWorkflowControlByBusinessId(businessId);
        checkExist(workflowControl, "未找到指定单据的流程控制记录");

        String key = SusDevConsts.PROJECT_PREFIX + "-approve-" + workflowControl.getId();
        try {
            if (RedisLockUtil.lock(key)) {
                doApprove(workflowControl, remark);
            } else {
                throw new ServiceException("当前单据正在审批中，请稍后再试");
            }
        } finally {
            RedisLockUtil.unlock(key);
        }
    }

    /**
     * 終止流程
     *
     * @param approveVO 审批信息
     */
    @Transactional(rollbackFor = Exception.class)
    public void terminate(ApproveVO approveVO) {
        checkApproveVO(approveVO);

        // Workflow workflow = workflowService.findWorkflowByOrgIdAndFormId(approveVO.getOrganizationId(), approveVO.getFormId());
        WorkflowControl workflowControl = workflowControlService.findWorkflowControlByBusinessId(approveVO.getBusinessId());
        checkExist(workflowControl, "未找到指定单据的流程控制记录");

        String key = SusDevConsts.PROJECT_PREFIX + "-terminate-" + workflowControl.getId();
        try {
            if (RedisLockUtil.lock(key)) {
                doTerminate(workflowControl, approveVO.getRemark());
            } else {
                throw new ServiceException("当前单据正在审批中，请稍后再试");
            }
        } finally {
            RedisLockUtil.unlock(key);
        }
    }

    @Transactional(rollbackFor = Exception.class)
    public void recall(String businessId, String remark) {
        checkExist(businessId, "业务ID不能为空");

        WorkflowControl workflowControl = workflowControlService.findWorkflowControlByBusinessId(businessId);
        checkExist(workflowControl, "未找到指定单据的流程控制记录");

        String key = SusDevConsts.PROJECT_PREFIX + "-recall-" + workflowControl.getId();
        try {
            if (RedisLockUtil.lock(key)) {
                doRecall(workflowControl, remark);
            } else {
                throw new ServiceException("当前单据正在审批中，请稍后再试");
            }
        } finally {
            RedisLockUtil.unlock(key);
        }
    }

    @Transactional(rollbackFor = Exception.class)
    public void terminate(String businessId, String remark) {
        checkExist(businessId, "业务ID不能为空");

        WorkflowControl workflowControl = workflowControlService.findWorkflowControlByBusinessId(businessId);
        checkExist(workflowControl, "未找到指定单据的流程控制记录");

        String key = SusDevConsts.PROJECT_PREFIX + "-terminate-" + workflowControl.getId();
        try {
            if (RedisLockUtil.lock(key)) {
                doTerminate(workflowControl, remark);
            } else {
                throw new ServiceException("当前单据正在审批中，请稍后再试");
            }
        } finally {
            RedisLockUtil.unlock(key);
        }
    }

    /**
     * 驳回流程
     *
     * @param approveVO 审批信息
     */
    @Transactional(rollbackFor = Exception.class)
    public void reject(ApproveVO approveVO) {
        checkApproveVO(approveVO);

        WorkflowControl workflowControl = workflowControlService.findWorkflowControlByBusinessId(approveVO.getBusinessId());
        checkExist(workflowControl, "该单据尚未发起流程审批");

        String key = SusDevConsts.PROJECT_PREFIX + "-reject-" + workflowControl.getId();
        try {
            if (RedisLockUtil.lock(key)) {
                doReject(workflowControl, approveVO.getRemark());
            } else {
                throw new ServiceException("当前单据正在审批中，请稍后再试");
            }
        } finally {
            RedisLockUtil.unlock(key);
        }
    }

    @Transactional(rollbackFor = Exception.class)
    public void reject(String businessId, String remark) {
        checkExist(businessId, "业务ID不能为空");

        WorkflowControl workflowControl = workflowControlService.findWorkflowControlByBusinessId(businessId);
        checkExist(workflowControl, "该单据尚未发起流程审批");

        String key = SusDevConsts.PROJECT_PREFIX + "-reject-" + workflowControl.getId();
        try {
            if (RedisLockUtil.lock(key)) {
                doReject(workflowControl, remark);
            } else {
                throw new ServiceException("当前单据正在审批中，请稍后再试");
            }
        } finally {
            RedisLockUtil.unlock(key);
        }
    }

    public void doReject(WorkflowControl workflowControl, String remark) {
        if (!Objects.equals(workflowControl.getState(), WorkflowControlState.IN_PROGRESS.getCode())) {
            throw new ServiceException("只能驳回审批中的单据");
        }
        WorkflowNode currentNode = workflowNodeService.selectByPrimaryKey(workflowControl.getCurrentNodeId());
        WorkflowNode beginNode = workflowNodeService.findBeginNodeByWorkflowId(workflowControl.getWorkflowId());
        checkAuthority(currentNode.getId());

        WorkflowNode previousNode = shiftToPreviousNode(currentNode);

        WorkflowControl updateWorkflowControl = new WorkflowControl();
        updateWorkflowControl.setId(workflowControl.getId());
        // 如果上一個節點是開始節點，則將流程控制记录设置为未激活状态
        if (StringUtils.equals(previousNode.getId(), beginNode.getId())) {
            updateWorkflowControl.setIsActive(Boolean.FALSE);
        }
        updateWorkflowControl.setCurrentNodeId(previousNode.getId());
        workflowControlService.updateByPrimaryKeySelective(updateWorkflowControl);

        // 保存流程日志
        WorkflowLogs workflowLogs = new WorkflowLogs();
        workflowLogs.setWorkflowId(workflowControl.getWorkflowId());
        workflowLogs.setWorkflowControlId(workflowControl.getId());
        workflowLogs.setNodeId(currentNode.getId());
        workflowLogs.setNodeName(currentNode.getName());
        workflowLogs.setUserId(ContextUtils.getCurrentUser().getId());
        workflowLogs.setUsersName(ContextUtils.getCurrentUser().getName());
        workflowLogs.setRemark(remark);
        workflowLogs.setOperateName(WorkflowOperateType.REJECT.getName());
        workflowlogsService.insertSelective(workflowLogs);

    }

    /**
     * 獲取指定節點的上一個節點，如果節點用戶為空，則往前找
     *
     * @param workflowNode
     * @return
     */
    private WorkflowNode shiftToPreviousNode(WorkflowNode workflowNode) {
        WorkflowNode previousNode = workflowNodeService.selectByPrimaryKey(workflowNode.getPreviousNodeId());
        if (previousNode == null) {
            throw new ServiceException("未找到上一节点");
        }
        if (Objects.equals(previousNode.getIsBeginNode(), Boolean.TRUE)) {
            return previousNode;
        }
        User user = workflowNodeService.getNodeUser(previousNode.getId());
        if (Objects.isNull(user)) {
            return shiftToPreviousNode(previousNode);
        }
        return previousNode;
    }

    private void doRecall(WorkflowControl workflowControl, String remark) {

        WorkflowNode currentNode = workflowNodeService.selectByPrimaryKey(workflowControl.getCurrentNodeId());

        // 1. 更新流程控制表
        WorkflowControl updateWorkflowControl = new WorkflowControl();
        updateWorkflowControl.setId(workflowControl.getId());
        updateWorkflowControl.setState(WorkflowControlState.RECALL.getCode());
        updateWorkflowControl.setIsActive(Boolean.FALSE);
        workflowControlService.updateByPrimaryKeySelective(updateWorkflowControl);

        // 2. 增加流程日志
        WorkflowLogs workflowLogs = new WorkflowLogs();
        workflowLogs.setWorkflowId(workflowControl.getWorkflowId());
        workflowLogs.setWorkflowControlId(workflowControl.getId());
        workflowLogs.setNodeId(currentNode.getId());
        workflowLogs.setNodeName(currentNode.getName());
        workflowLogs.setUserId(ContextUtils.getCurrentUser().getId());
        workflowLogs.setUsersName(ContextUtils.getCurrentUser().getName());
        workflowLogs.setRemark(remark);
        workflowLogs.setOperateName(WorkflowOperateType.RECALL.getName());
        workflowlogsService.insertSelective(workflowLogs);
    }

    private void doTerminate(WorkflowControl workflowControl, String remark) {
        if (!Objects.equals(workflowControl.getState(), WorkflowControlState.IN_PROGRESS.getCode())) {
            throw new ServiceException("当前单据不在审批中");
        }

        WorkflowNode currentNode = workflowNodeService.selectByPrimaryKey(workflowControl.getCurrentNodeId());

        checkAuthority(currentNode.getId());

        // 1. 更新流程控制表
        WorkflowControl updateWorkflowControl = new WorkflowControl();
        updateWorkflowControl.setId(workflowControl.getId());
        updateWorkflowControl.setState(WorkflowControlState.TERMINATED.getCode());
        updateWorkflowControl.setIsActive(Boolean.FALSE);
        workflowControlService.updateByPrimaryKeySelective(updateWorkflowControl);

        // 2. 增加流程日志
        WorkflowLogs workflowLogs = new WorkflowLogs();
        workflowLogs.setWorkflowId(workflowControl.getWorkflowId());
        workflowLogs.setWorkflowControlId(workflowControl.getId());
        workflowLogs.setNodeId(currentNode.getId());
        workflowLogs.setNodeName(currentNode.getName());
        workflowLogs.setUserId(ContextUtils.getCurrentUser().getId());
        workflowLogs.setUsersName(ContextUtils.getCurrentUser().getName());
        workflowLogs.setRemark(remark);
        workflowLogs.setOperateName(WorkflowOperateType.TERMINATE.getName());
        workflowlogsService.insertSelective(workflowLogs);
    }

    /**
     * 實現審批邏輯
     *
     * @param workflowControl
     */
    private void doApprove(WorkflowControl workflowControl, String remark) {
        doApprove(workflowControl, remark, true);
    }

    void doApprove(WorkflowControl workflowControl, String remark, boolean needAuth) {
        if (Objects.equals(workflowControl.getState(), WorkflowControlState.COMPLETED.getCode())) {
            throw new ServiceException("当前单据已经审批完成");
        }

        String currentNodeId = workflowControl.getCurrentNodeId();
        WorkflowNode currentNode = workflowNodeService.selectByPrimaryKey(currentNodeId);
        WorkflowNode nextNode = workflowNodeService.findNextAvailableNodeByWorkflowIdAndNodeId(workflowControl.getWorkflowId(), currentNodeId);

        if (needAuth) {
            checkAuthority(currentNodeId);
        }

        // 如果當前節點是最後一個節點，則直接完成
        WorkflowControl updateWorkflowControl = new WorkflowControl();
        updateWorkflowControl.setId(workflowControl.getId());
        if (nextNode == null) {
            updateWorkflowControl.setState(WorkflowControlState.COMPLETED.getCode());
        } else {
            updateWorkflowControl.setCurrentNodeId(nextNode.getId());
        }
        workflowControlService.updateByPrimaryKeySelective(updateWorkflowControl);
        //当前节点审核通过，更新最后更新时间
        currentNode.setLastUpdateTime(LocalDateTime.now());
        workflowNodeService.updateBySelective(currentNode);
        // 增加一條流程日志
        WorkflowLogs workflowLogs = new WorkflowLogs();
        workflowLogs.setWorkflowId(workflowControl.getWorkflowId());
        workflowLogs.setWorkflowControlId(workflowControl.getId());
        workflowLogs.setNodeId(currentNode.getId());
        workflowLogs.setNodeName(currentNode.getName());
        workflowLogs.setUserId(ContextUtils.getCurrentUser().getId());
        workflowLogs.setUsersName(ContextUtils.getCurrentUser().getName());
        workflowLogs.setRemark(remark);
        workflowLogs.setOperateName(WorkflowOperateType.APPROVE.getName());
        workflowlogsService.insertSelective(workflowLogs);
    }

    /**
     * 验证当前用户是否拥有操作权限
     *
     * @param currentNodeId
     */
    private void checkAuthority(String currentNodeId) {
        List<User> lstUser = workflowNodeService.listNodeUser(currentNodeId);
        List<String> lstUserId = lstUser.stream().map(User::getId).collect(Collectors.toList());
        if (!CollectionUtils.containsAny(lstUserId, ContextUtils.getCurrentUser().getId())) {
            throw new ServiceException("当前节点不是您审批的节点");
        }
    }

    @Transactional(rollbackFor = Exception.class)
    public String saveWorkflow(WorkflowVO workflowVO) {
        // checkExist(workflowVO.getCode(), "流程编码不能为空");
        // checkExist(workflowVO.getName(), "流程名称不能为空");
        checkExist(workflowVO.getFormId(), "表单ID不能为空");
        checkExist(workflowVO.getOrganizationId(), "组织ID不能为空");
        checkExist(workflowVO.getRepresentativeId(), "發起人ID不能为空");
        if (CollectionUtils.isEmpty(workflowVO.getWorkflowNodes())) {
            throw new ServiceException("流程节点不能为空");
        }
        if(workflowVO.getWorkflowNodes().size() < 1 || workflowVO.getWorkflowNodes().get(0).getUserId() == null) {
            throw new ServiceException("流程配置必須最少填寫一個審核人");
        }

        if (StringUtils.isBlank(workflowVO.getId())) {
            return doAdd(workflowVO);
        } else {
            WorkflowVO oriWorkflowVO = workflowService.findWorkflowById(workflowVO.getId());
            WorkflowControl workflowControl = workflowControlService.findWorkflowControlByWorkflowId(workflowVO.getId());
            //throw new ServiceException(JSON.toJSONString(oriWorkflowVO) + JSON.toJSONString(workflowControl));
            if(workflowControl != null && oriWorkflowVO != null
                    && !workflowVO.getRepresentativeId().equals(oriWorkflowVO.getRepresentativeId())) {
                throw new ServiceException("表單已提交，不允許修改發起人");
            }
            return doUpdate(workflowVO);
        }

    }

    private String doUpdate(WorkflowVO workflowVO) {

        Workflow existedWorkflow = workflowService.findWorkflowByOrgIdAndFormId(workflowVO.getOrganizationId(),
                workflowVO.getFormId(), workflowVO.getYear(), workflowVO.getMonth());
        if (Objects.nonNull(existedWorkflow) && !StringUtils.equals(existedWorkflow.getId(), workflowVO.getId())) {
            throw new ServiceException("指定组织机构及表单的流程已存在");
        }

        Workflow workflow = WorkflowVOConverter.convert(workflowVO);
        // update
        checkExist(workflowVO.getLastUpdateVersion(), "最后更新版本不能为空");
        WorkflowExample workflowExample = new WorkflowExample();
        workflowExample.or().andIdEqualTo(workflowVO.getId()).andLastUpdateVersionEqualTo(workflowVO.getLastUpdateVersion());
        int updateCount = workflowService.updateByExampleSelective(workflow, workflowExample);
        if (updateCount == 0) {
            throw new ServiceException("更新失败，数据已被修改");
        }

        for (WorkflowNodeVO workflowNodeVO : workflowVO.getWorkflowNodes()) {
            if (StringUtils.isBlank(workflowNodeVO.getUserId())) {
                workflowNodeUserService.removeAllUserFromNode(workflowNodeVO.getId());
            } else {
                workflowNodeUserService.addUserToNode(workflowNodeVO.getUserId(), workflowNodeVO.getId());
                addOrgToUser(workflowVO.getOrganizationId(), workflowNodeVO.getUserId());
            }
        }

        return workflow.getId();
    }

    private String doAdd(WorkflowVO workflowVO) {
        String key = SusDevConsts.PROJECT_PREFIX + "-" + workflowVO.getOrganizationId() + "-" + workflowVO.getFormId();
        try {
            if (RedisLockUtil.lock(key)) {
                Workflow workflow = WorkflowVOConverter.convert(workflowVO);
                if (workflowService.isExistByOrgIdAndFormId(workflowVO.getOrganizationId(),
                        workflowVO.getFormId(), workflowVO.getYear(), workflowVO.getMonth())) {
                    throw new ServiceException("该组织下已存在该表单的流程");
                }

                // add
                workflowService.insertSelective(workflow);

                // 创建流程节点
                String previousNodeId = workflowNodeService.addWorkflowNode(new WorkflowNodeVO().setWorkflowId(workflow.getId()).setName("发起人").setIsBeginNode(Boolean.TRUE));

                for (WorkflowNodeVO workflowNodeVO : workflowVO.getWorkflowNodes()) {
                    workflowNodeVO.setWorkflowId(workflow.getId());
                    workflowNodeVO.setIsBeginNode(Boolean.FALSE);
                    workflowNodeVO.setPreviousNodeId(previousNodeId);
                    previousNodeId = workflowNodeService.addWorkflowNode(workflowNodeVO);
                    if (StringUtils.isNotBlank(workflowNodeVO.getUserId())) {
                        workflowNodeUserService.addUserToNode(workflowNodeVO.getUserId(), previousNodeId);
                        // 增加用户组织机构权限，默认分配审批权限就等于分配组织机构权限
                        addOrgToUser(workflowVO.getOrganizationId(), workflowNodeVO.getUserId());
                    }
                }
                return workflow.getId();
            } else {
                throw new ServiceException("请勿重复提交");
            }
        } finally {
            RedisLockUtil.unlock(key);
        }
    }

    private void doStartWorkflow(String organizationId, String formId, String businessId, Integer year, Integer month) {
        Workflow workflow = workflowService.findWorkflowByOrgIdAndFormId(organizationId, formId, year, month);
        checkExist(workflow, "流程不存在");

        if (!workflowControlService.isAllowToStartWorkflow(workflow.getId(), businessId)) {
            throw new ServiceException("该单据已经存在流程记录");
        }

        // 增加一条流程控制记录
        WorkflowControl workflowControl = new WorkflowControl();
        workflowControl.setWorkflowId(workflow.getId());
        workflowControl.setBusinessId(businessId);
        workflowControl.setCurrentNodeId(workflowNodeService.findFirstAuditNodeByWorkflowId(workflow.getId()).getId());
        workflowControl.setState(WorkflowControlState.IN_PROGRESS.getCode());
        workflowControl.setIsActive(Boolean.TRUE);
        workflowControl.setIsUrgentEditable(Boolean.FALSE);
        workflowControlService.insertSelective(workflowControl);

        WorkflowNode beginNode = workflowNodeService.findBeginNodeByWorkflowId(workflow.getId());
        // 更新最后更新时间
        beginNode.setLastUpdateTime(LocalDateTime.now());
        workflowNodeService.updateBySelective(beginNode);
        // 增加一条發起流程的日志记录
        WorkflowLogs workflowLogs = new WorkflowLogs();
        workflowLogs.setWorkflowId(workflow.getId());
        workflowLogs.setNodeId(beginNode.getId());
        workflowLogs.setNodeName(beginNode.getName());
        workflowLogs.setUserId(ContextUtils.getCurrentUser().getId());
        workflowLogs.setUsersName(ContextUtils.getCurrentUser().getName());
        workflowLogs.setRemark("发起流程");
        workflowLogs.setOperateName(WorkflowOperateType.SUBMIT.getName());
        workflowlogsService.insertSelective(workflowLogs);
    }

    @Transactional(rollbackFor = Exception.class)
    void initWorkflow(String workflowCode, String workflowName) {
        if (workflowService.isWorkflowExist(workflowCode)) {
            logger.info("流程已存在，不需要初始化");
            throw new ServiceException("流程已存在，不需要初始化");
        }

        // 创建流程定义
        Workflow workflow = new Workflow();
        workflow.setName(workflowName);
        workflow.setCode(workflowCode);
        workflow.setIsActive(Boolean.TRUE);
        workflowService.insertSelective(workflow);

        User user = userService.getUserByUsername("tao_li");

        // 创建流程节点
        String previousNodeId = workflowNodeService.addWorkflowNode(new WorkflowNodeVO().setWorkflowId(workflow.getId()).setName("发起人").setIsBeginNode(Boolean.TRUE));

        // 审批人1
        previousNodeId = workflowNodeService.addWorkflowNode(
                new WorkflowNodeVO().setWorkflowId(workflow.getId()).setName("审批人1").setPreviousNodeId(previousNodeId).setIsBeginNode(Boolean.FALSE));
        workflowNodeUserService.addUserToNode(user.getId(), previousNodeId);

        // 审批人2
        previousNodeId = workflowNodeService.addWorkflowNode(
                new WorkflowNodeVO().setWorkflowId(workflow.getId()).setName("审批人2").setPreviousNodeId(previousNodeId).setIsBeginNode(Boolean.FALSE));
        workflowNodeUserService.addUserToNode(user.getId(), previousNodeId);

        // 审批人3
        previousNodeId = workflowNodeService.addWorkflowNode(
                new WorkflowNodeVO().setWorkflowId(workflow.getId()).setName("审批人3").setPreviousNodeId(previousNodeId).setIsBeginNode(Boolean.FALSE));
        workflowNodeUserService.addUserToNode(user.getId(), previousNodeId);

        // 审批人4
        previousNodeId = workflowNodeService.addWorkflowNode(
                new WorkflowNodeVO().setWorkflowId(workflow.getId()).setName("审批人4").setPreviousNodeId(previousNodeId).setIsBeginNode(Boolean.FALSE));
        workflowNodeUserService.addUserToNode(user.getId(), previousNodeId);

        // previousNodeId = workflowNodeService.addWorkflowNode(new WorkflowNodeVO(null, workflow.getId(), "结束", previousNodeId, Boolean.FALSE));
    }

    public void finishAllAmbient(int year, int month) {
        AmbientService ambientService = SpringContextUtil.getBean(AmbientService.class);
        List<String> headIds = ambientService.selectAllSubmittedHeadIdByYearMonth(year, month);
        logger.info("headIds: {}", headIds);
        logger.info("finish all ambient, year: {}, ambient size: {}", year, headIds.size());

        if (CollectionUtils.isEmpty(headIds)) {
            logger.info("no ambient to finish");
            return;
        }

        for (String headId : headIds) {
            WorkflowControl workflowControl = workflowControlService.findWorkflowControlByBusinessId(headId);
            // 2023-02-16 按ricky要求，将所有已提交的环境监测单自动完成
            doApprove(workflowControl, "按业务部们要求，系统自动完成：" + DateUtils.toDateString(LocalDateTime.now()), false);
        }
    }

    /**
     * 将指定组织机构及其子机构的流程记录全部删除
     *
     * @param orgId
     */
    public void removeAllAmbientWorkflowOfOrgWithChildren(String orgId, Integer year) {
        checkExist(orgId, "组织机构id不能为空");

        // step1: 获取组织机构及其子机构
        Organization organization = organizationService.selectByPrimaryKey(orgId);
        String no = organization.getNo();
        OrganizationExample example = new OrganizationExample();
        example.or().andIsDeletedEqualTo(Boolean.FALSE).andNoLike(no + "%");
        example.setOrderByClause("no");

        List<Organization> orgChildren = organizationService.selectByExample(example);

        // step2: 遍历组织机构删除流程记录
        for (Organization child : orgChildren) {
            System.out.println("no: " + child.getNo() + ", name: " + child.getName());
            inactiveAmbientWorkflowByOrgIdAndYear(child.getId(), year);
        }
    }

    /**
     * 移除指定组织机构的环境绩效流程记录
     *
     * @param orgId 组织机构id
     * @param year  年份
     */
    private void inactiveAmbientWorkflowByOrgIdAndYear(String orgId, Integer year) {
        checkExist(orgId, "组织机构id不能为空");
        checkExist(year, "年份不能为空");

        AmbientHeadExample headExample = new AmbientHeadExample();
        headExample.or().andOrganizationIdEqualTo(orgId).andYearEqualTo(year).andIsActiveEqualTo(Boolean.TRUE);
        List<AmbientHead> lstHeads = ambientService.selectByExample(headExample);
        if (CollectionUtils.isEmpty(lstHeads)) {
            logger.info("orgId: {}, year: {}, no ambient head found", orgId, year);
            return;
        }
        if (lstHeads.size() > 1) {
            logger.error("orgId: {}, year: {}, more than one ambient head found", orgId, year);
            throw new ServiceException("orgId: " + orgId + ", year: " + year + ", more than one ambient head found");
        }
        AmbientHead head = lstHeads.get(0);

        WorkflowControl workflowControl = workflowControlService.findWorkflowControlByBusinessId(head.getId());
        if (workflowControl == null) {
            logger.info("orgId: {}, year: {}, no workflow control found", orgId, year);
            return;
        }
        workflowControlService.inactiveWorkflowControl(workflowControl.getId());
    }

    /**
     * 将指定组织机构在指定年份的环境绩效设置为已完成
     * 包括未提交和审批中的单据
     *
     * @param orgId
     * @param year
     */
    public void finishSpecifiedAmbientByOrgIdAndYear(String orgId, Integer year) {
        checkExist(orgId, "组织机构id不能为空");
        checkExist(year, "年份不能为空");

        AmbientHeadExample headExample = new AmbientHeadExample();
        headExample.or().andOrganizationIdEqualTo(orgId).andYearEqualTo(year).andIsActiveEqualTo(Boolean.TRUE);
        List<AmbientHead> lstHeads = ambientService.selectByExample(headExample);
        if (CollectionUtils.isEmpty(lstHeads)) {
            logger.info("orgId: {}, year: {}, no ambient head found", orgId, year);
            return;
        }
        if (lstHeads.size() > 1) {
            logger.error("orgId: {}, year: {}, more than one ambient head found", orgId, year);
            throw new ServiceException("orgId: " + orgId + ", year: " + year + ", more than one ambient head found");
        }
        AmbientHead head = lstHeads.get(0);

        WorkflowControl workflowControl = workflowControlService.findWorkflowControlByBusinessId(head.getId());
        if (workflowControl == null) {
            FormService formService = SpringContextUtil.getBean(FormService.class);
            Form form = formService.findFormByCode("ambient");
            startWorkflow(orgId, form.getId(), head.getId());
            workflowControl = workflowControlService.findWorkflowControlByBusinessId(head.getId());
        }

        while (!Objects.equals(WorkflowControlState.COMPLETED.getCode(), workflowControl.getState())) {
            doApprove(workflowControl, "按业务部们要求，系统自动完成：" + DateUtils.toDateString(LocalDateTime.now()), false);
            workflowControl = workflowControlService.findWorkflowControlByBusinessId(head.getId());
        }

    }
}
