package com.csci.susdev.facade;

import com.csci.common.exception.ServiceException;
import com.csci.common.util.CommonUtils;
import com.csci.susdev.annotation.LogMethod;
import com.csci.susdev.model.AmbientDetail;
import com.csci.susdev.model.AmbientHead;
import com.csci.susdev.model.Organization;
import com.csci.susdev.service.*;
import com.csci.susdev.util.SpringContextUtil;
import com.csci.susdev.util.context.ContextUtils;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

import static com.csci.susdev.service.ServiceHelper.*;

@Component
@LogMethod
public class AmbientSummaryFacade {

    /**
     * 日志记录对象
     */
    private static final Logger logger = LoggerFactory.getLogger(AmbientSummaryFacade.class);

    @Resource
    private OrganizationService organizationService;

    @Resource
    private AmbientService ambientservice;

    @Resource
    private AmbientDetailService ambientDetailService;

    // 环境绩效汇总
    @Transactional(rollbackFor = Exception.class)
    public void summaryOrgData(String organizationId, Integer year, Integer month) {
        checkExist(organizationId, "组织机构id不能为空");
        // checkExist(year, "年份不能为空");
        // checkExist(month, "月份不能为空");
        /*
        if (!isUserOrganizationExist(ContextUtils.getCurrentUser().getId(), organizationId)) {
            throw new ServiceException("用户无权限查看该组织机构的数据");
        }
         */

        Organization parent = organizationService.selectByPrimaryKey(organizationId);
        ServiceHelper.checkExist(parent, "组织机构不存在");
        if (organizationService.isOrganizationLeaf(organizationId)) {
            throw new ServiceException("该组织机构是叶子节点，无法汇总");
        }

        AmbientHead parentHead = ambientservice.findAmbientHead(organizationId, year, month);
        checkExist(parentHead, "父组织机构环境绩效数据不存在");

        WorkflowControlService workflowControlService = SpringContextUtil.getBean(WorkflowControlService.class);
        if (workflowControlService.isNotAllowEdit(parentHead.getId())) {
            throw new ServiceException("环境绩效数据已经提交，不能修改");
        }

        // 查询所有子组织机构
        List<Organization> organizations = organizationService.findLeafChildrenByOrgId(organizationId);
        if (CollectionUtils.isEmpty(organizations)) {
            throw new ServiceException("没有子部门或项目");
        }

        // 查询所有子组织机构的环境绩效数据
        // List<AmbientHead> ambientHeads = ambientservice.listAmbientHeadByOrganizationIdsAndYear(organizations.stream().map(Organization::getId).collect(Collectors.toList()), year);
        // if (CollectionUtils.isEmpty(ambientHeads)) {
        //     throw new ServiceException("没有环境绩效数据");
        // }
        List<String> headIds = ambientservice.selectAllSubmittedHeadId(organizations.stream().map(Organization::getId).collect(Collectors.toList()), year, month);
        if (CollectionUtils.isEmpty(headIds)) {
            throw new ServiceException("没有已提交的环境绩效数据");
        }

        // 查询所有明细数据
        List<AmbientDetail> ambientDetails = ambientDetailService.listAmbientDetailByHeadIds(headIds);

        List<AmbientDetail> lstSumDetail = new ArrayList<>();
        // 按unitCode分组
        ambientDetails.stream().collect(Collectors.groupingBy(AmbientDetail::getUnitCode)).forEach((k, v) -> {
            // 将分组后的数据汇总并入一条数据，然后加入到结果集中
            logger.info("unitCode: {}, ambientDetails: {}", k, v.size());
            AmbientDetail first = v.get(0);
            // 将list中元素的每个属性分别汇总到一条记录里面
            AmbientDetail ambientDetail = new AmbientDetail();
            ambientDetail.setUnitCode(k);
            // ambientDetail.setId();
            ambientDetail.setHeadId(parentHead.getId());
            ambientDetail.setCategory(first.getCategory());
            ambientDetail.setCategoryDigest(first.getCategoryDigest());
            ambientDetail.setType(first.getType());
            ambientDetail.setType2(first.getType2());
            ambientDetail.setUnit(first.getUnit());
            ambientDetail.setSeq(first.getSeq());
            // 汇总
            ambientDetail.setMonthValue1(v.stream().map(AmbientDetail::getMonthValue1).reduce("0", this::add));
            ambientDetail.setMonthValue2(v.stream().map(AmbientDetail::getMonthValue2).reduce("0", this::add));
            ambientDetail.setMonthValue3(v.stream().map(AmbientDetail::getMonthValue3).reduce("0", this::add));
            ambientDetail.setSeasonValue1(v.stream().map(AmbientDetail::getSeasonValue1).reduce("0", this::add));
            ambientDetail.setMonthValue4(v.stream().map(AmbientDetail::getMonthValue4).reduce("0", this::add));
            ambientDetail.setMonthValue5(v.stream().map(AmbientDetail::getMonthValue5).reduce("0", this::add));
            ambientDetail.setMonthValue6(v.stream().map(AmbientDetail::getMonthValue6).reduce("0", this::add));
            ambientDetail.setSeasonValue2(v.stream().map(AmbientDetail::getSeasonValue2).reduce("0", this::add));
            ambientDetail.setMonthValue7(v.stream().map(AmbientDetail::getMonthValue7).reduce("0", this::add));
            ambientDetail.setMonthValue8(v.stream().map(AmbientDetail::getMonthValue8).reduce("0", this::add));
            ambientDetail.setMonthValue9(v.stream().map(AmbientDetail::getMonthValue9).reduce("0", this::add));
            ambientDetail.setSeasonValue3(v.stream().map(AmbientDetail::getSeasonValue3).reduce("0", this::add));
            ambientDetail.setMonthValue10(v.stream().map(AmbientDetail::getMonthValue10).reduce("0", this::add));
            ambientDetail.setMonthValue11(v.stream().map(AmbientDetail::getMonthValue11).reduce("0", this::add));
            ambientDetail.setMonthValue12(v.stream().map(AmbientDetail::getMonthValue12).reduce("0", this::add));
            ambientDetail.setSeasonValue4(v.stream().map(AmbientDetail::getSeasonValue4).reduce("0", this::add));
            ambientDetail.setYearTotalValue(v.stream().map(AmbientDetail::getYearTotalValue).reduce("0", this::add));
            ambientDetail.setRemark(first.getRemark());
            // ambientDetail.setCreationTime();
            lstSumDetail.add(ambientDetail);
        });

        deleteAmbientDetailByHeadId(parentHead.getId());
        for (AmbientDetail ambientDetail : lstSumDetail) {
            ambientDetailService.insertSelective(ambientDetail);
        }
    }

    String add(String a, String b) {
        // 转换成数字之后相加，为空则默认为0
        return String.valueOf(CommonUtils.add(convert(a), convert(b)));
    }

    BigDecimal convert(String a) {
        if (StringUtils.isBlank(a)) {
            return BigDecimal.ZERO;
        }
        try {
            return new BigDecimal(a);
        } catch (Exception e) {
            return BigDecimal.ZERO;
        }
    }
}
