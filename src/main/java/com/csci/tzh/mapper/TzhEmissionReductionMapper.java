package com.csci.tzh.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.csci.tzh.model.TzhEmissionReduction;
import com.csci.tzh.model.TzhEmissionReductionExample;
import java.util.List;
import org.apache.ibatis.annotations.Param;

public interface TzhEmissionReductionMapper  extends BaseMapper<com.csci.cohl.model.TzhEmissionReduction> {

	/**
	 * This method was generated by MyBatis Generator. This method corresponds to the database table Tzh_EmissionReduction
	 * @mbg.generated  Mon May 22 15:51:12 HKT 2023
	 */
	long countByExample(TzhEmissionReductionExample example);

	/**
	 * This method was generated by MyBatis Generator. This method corresponds to the database table Tzh_EmissionReduction
	 * @mbg.generated  Mon May 22 15:51:12 HKT 2023
	 */
	int deleteByExample(TzhEmissionReductionExample example);

	/**
	 * This method was generated by MyBatis Generator. This method corresponds to the database table Tzh_EmissionReduction
	 * @mbg.generated  Mon May 22 15:51:12 HKT 2023
	 */
	int insert(TzhEmissionReduction row);

	/**
	 * This method was generated by MyBatis Generator. This method corresponds to the database table Tzh_EmissionReduction
	 * @mbg.generated  Mon May 22 15:51:12 HKT 2023
	 */
	int insertSelective(TzhEmissionReduction row);

	/**
	 * This method was generated by MyBatis Generator. This method corresponds to the database table Tzh_EmissionReduction
	 * @mbg.generated  Mon May 22 15:51:12 HKT 2023
	 */
	List<TzhEmissionReduction> selectByExample(TzhEmissionReductionExample example);

	/**
	 * This method was generated by MyBatis Generator. This method corresponds to the database table Tzh_EmissionReduction
	 * @mbg.generated  Mon May 22 15:51:12 HKT 2023
	 */
	int updateByExampleSelective(@Param("row") TzhEmissionReduction row,
			@Param("example") TzhEmissionReductionExample example);

	/**
	 * This method was generated by MyBatis Generator. This method corresponds to the database table Tzh_EmissionReduction
	 * @mbg.generated  Mon May 22 15:51:12 HKT 2023
	 */
	int updateByExample(@Param("row") TzhEmissionReduction row, @Param("example") TzhEmissionReductionExample example);
}
