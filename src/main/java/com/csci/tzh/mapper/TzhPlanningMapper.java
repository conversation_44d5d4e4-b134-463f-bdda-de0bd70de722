package com.csci.tzh.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.csci.tzh.model.TzhPlanning;
import com.csci.tzh.model.TzhPlanningExample;
import java.util.List;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

public interface TzhPlanningMapper extends BaseMapper<com.csci.cohl.model.TzhPlanning> {
	@Select("""
            <script>
            SELECT * FROM Tzh_Planning PL
            LEFT JOIN Tzh_Protocol PT ON PL.ProtocolId = PT.Id
            LEFT JOIN t_organization o  on o.name = PL.SiteName and o.is_deleted = 0
            WHERE PL.IsDeleted = 0 AND PL.SiteName = #{siteName} AND PT.NameEN = #{protocol}
            <if test="siteId != null and siteId != ''">
                and o.id = #{siteId}
			</if>
            ORDER BY Seq
            </script>
            """)
	List<com.csci.cohl.model.TzhPlanning> list(@Param("siteName") String siteName, @Param("protocol") String protocol, @Param("siteId") String siteId);
	/**
	 * This method was generated by MyBatis Generator. This method corresponds to the database table Tzh_Planning
	 * @mbg.generated  Tue Apr 25 13:08:25 HKT 2023
	 */
	long countByExample(TzhPlanningExample example);

	/**
	 * This method was generated by MyBatis Generator. This method corresponds to the database table Tzh_Planning
	 * @mbg.generated  Tue Apr 25 13:08:25 HKT 2023
	 */
	int deleteByExample(TzhPlanningExample example);

	/**
	 * This method was generated by MyBatis Generator. This method corresponds to the database table Tzh_Planning
	 * @mbg.generated  Tue Apr 25 13:08:25 HKT 2023
	 */
	int insert(TzhPlanning row);

	/**
	 * This method was generated by MyBatis Generator. This method corresponds to the database table Tzh_Planning
	 * @mbg.generated  Tue Apr 25 13:08:25 HKT 2023
	 */
	int insertSelective(TzhPlanning row);

	/**
	 * This method was generated by MyBatis Generator. This method corresponds to the database table Tzh_Planning
	 * @mbg.generated  Tue Apr 25 13:08:25 HKT 2023
	 */
	List<TzhPlanning> selectByExample(TzhPlanningExample example);

	/**
	 * This method was generated by MyBatis Generator. This method corresponds to the database table Tzh_Planning
	 * @mbg.generated  Tue Apr 25 13:08:25 HKT 2023
	 */
	int updateByExampleSelective(@Param("row") TzhPlanning row, @Param("example") TzhPlanningExample example);

	/**
	 * This method was generated by MyBatis Generator. This method corresponds to the database table Tzh_Planning
	 * @mbg.generated  Tue Apr 25 13:08:25 HKT 2023
	 */
	int updateByExample(@Param("row") TzhPlanning row, @Param("example") TzhPlanningExample example);
}
