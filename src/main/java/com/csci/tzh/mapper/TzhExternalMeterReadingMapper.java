package com.csci.tzh.mapper;

import com.csci.tzh.model.TzhExternalMeterReading;
import com.csci.tzh.model.TzhExternalMeterReadingExample;
import java.util.List;
import org.apache.ibatis.annotations.Param;

public interface TzhExternalMeterReadingMapper {

	/**
	 * This method was generated by MyBatis Generator. This method corresponds to the database table Tzh_External_MeterReading
	 * @mbg.generated  Mon Feb 20 16:33:53 HKT 2023
	 */
	long countByExample(TzhExternalMeterReadingExample example);

	/**
	 * This method was generated by MyBatis Generator. This method corresponds to the database table Tzh_External_MeterReading
	 * @mbg.generated  Mon Feb 20 16:33:53 HKT 2023
	 */
	int deleteByExample(TzhExternalMeterReadingExample example);

	/**
	 * This method was generated by MyBatis Generator. This method corresponds to the database table Tzh_External_MeterReading
	 * @mbg.generated  Mon Feb 20 16:33:53 HKT 2023
	 */
	int insert(TzhExternalMeterReading row);

	/**
	 * This method was generated by MyBatis Generator. This method corresponds to the database table Tzh_External_MeterReading
	 * @mbg.generated  Mon Feb 20 16:33:53 HKT 2023
	 */
	int insertSelective(TzhExternalMeterReading row);

	/**
	 * This method was generated by MyBatis Generator. This method corresponds to the database table Tzh_External_MeterReading
	 * @mbg.generated  Mon Feb 20 16:33:53 HKT 2023
	 */
	List<TzhExternalMeterReading> selectByExample(TzhExternalMeterReadingExample example);

	/**
	 * This method was generated by MyBatis Generator. This method corresponds to the database table Tzh_External_MeterReading
	 * @mbg.generated  Mon Feb 20 16:33:53 HKT 2023
	 */
	int updateByExampleSelective(@Param("row") TzhExternalMeterReading row,
			@Param("example") TzhExternalMeterReadingExample example);

	/**
	 * This method was generated by MyBatis Generator. This method corresponds to the database table Tzh_External_MeterReading
	 * @mbg.generated  Mon Feb 20 16:33:53 HKT 2023
	 */
	int updateByExample(@Param("row") TzhExternalMeterReading row,
			@Param("example") TzhExternalMeterReadingExample example);
}