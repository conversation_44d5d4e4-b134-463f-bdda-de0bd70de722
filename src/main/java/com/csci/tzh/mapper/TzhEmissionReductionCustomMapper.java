package com.csci.tzh.mapper;

import com.csci.tzh.vo.TzhEmissionReductionVO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;
import java.util.Map;

@Mapper
public interface TzhEmissionReductionCustomMapper {
	
    @Select("""
            SELECT ER.Id, HeadId = ERH.Id, ERH.SiteName, ERH.Title, ERH.TitleSC, ERH.TitleEN, C.CategoryName, C.CategoryNameSC, C.CategoryNameEN,
            CarbonEmissionLocation = CEL.Name, CarbonEmissionLocationSC = CEL.NameSC, CarbonEmissionLocationEN = CEL.NameEN,
            ER.Type, ER.RecordYearMonth, ER.CarbonReductionAmount, ER.CarbonUnit, ER.IsDeleted
            FROM [Tzh_EmissionReductionHead] ERH
            INNER JOIN Tzh_EmissionReduction ER ON ER.headId = ERH.id AND ER.IsDeleted <> 1
            LEFT JOIN Tzh_CarbonEmissionLocation CEL ON ERH.CarbonEmissionLocationId = CEL.Id
            LEFT JOIN Tzh_Protocol_Category C ON ERH.ProtocolCategoryId = C.Id
            LEFT JOIN Tzh_Protocol P ON C.ProtocolId = P.Id
            LEFT JOIN Tzh_ProjectInfo PI ON PI.Name = ERH.SiteName AND PI.IsDeleted = 0
            LEFT JOIN Tzh_Region R ON R.Id = PI.RegionId
            WHERE ERH.IsDeleted = 0
            AND ERH.SiteName = #{siteName}
            AND P.NameEN = #{protocol}
            AND ER.RecordYearMonth >= #{recordYearMonthFrom} AND ER.RecordYearMonth <= #{recordYearMonthTo}
            """)
    public List<TzhEmissionReductionVO> list(@Param("siteName") String siteName,
                                             @Param("protocol") String protocol,
                                             @Param("recordYearMonthFrom") Integer recordYearMonthFrom,
                                             @Param("recordYearMonthTo") Integer recordYearMonthTo);

    @Select("""
            SELECT 
            (SELECT MAX(t.RecordYearMonth) FROM F_CombineAll t WHERE t.SiteName = #{siteName} AND t.CalculateDate = (SELECT MAX(_t.CalculateDate) FROM F_CombineAll _t) AND t.CarbonAmount > 0 AND t.CarbonAmount IS NOT NULL) AS LastRecordMonth,             
            (SELECT MAX(t.RecordYearMonth)             
            FROM Tzh_EmissionReductionHead h             
            LEFT JOIN Tzh_EmissionReduction t ON t.IsDeleted = 0 AND t.HeadId = h.Id             
            WHERE h.SiteName = #{siteName}             
            AND t.Type = N'每月減排'
            AND h.IsDeleted = 0             
            AND t.CarbonReductionAmount > 0 AND t.CarbonReductionAmount IS NOT NULL) AS LastReductionRecordMonth,             
            (SELECT SUM(ISNULL(t.CarbonAmount, 0))/1000 FROM F_CombineAll t WHERE t.SiteName = #{siteName} AND t.CarbonEmissionLocation = N'地盤' AND t.CalculateDate = (SELECT max(CalculateDate) FROM F_CombineAll)) AS CarbonAmountSite,              
            (SELECT SUM(ISNULL(t.CarbonAmount, 0))/1000 FROM F_CombineAll t WHERE t.SiteName = #{siteName} AND t.CarbonEmissionLocation = N'辦公室' AND t.CalculateDate = (SELECT max(CalculateDate) FROM F_CombineAll)) AS CarbonAmountOffice,              
            (SELECT SUM(ISNULL(t.CarbonAmount, 0))/1000 FROM F_CombineAll t WHERE t.SiteName = #{siteName} AND CalculateDate = (SELECT max(CalculateDate) FROM F_CombineAll)) AS CarbonAmountTotal,              
            (SELECT SUM(ISNULL(t.CarbonAmount, 0)) FROM Tzh_ExpectedEmissionIso t WHERE t.SiteName = #{siteName} AND t.IsDeleted <> 1 AND (SELECT CEL.Name FROM Tzh_CarbonEmissionLocation CEL WHERE Id = t.CarbonEmissionLocationId) = N'地盤') AS ExpectedCarbonAmountSite,              
            (SELECT SUM(ISNULL(t.CarbonAmount, 0)) FROM Tzh_ExpectedEmissionIso t WHERE t.SiteName = #{siteName} AND t.IsDeleted <> 1 AND (SELECT CEL.Name FROM Tzh_CarbonEmissionLocation CEL WHERE Id = t.CarbonEmissionLocationId) = N'辦公室') AS ExpectedCarbonAmountOffice,              
            (SELECT SUM(ISNULL(t.CarbonAmount, 0)) FROM Tzh_ExpectedEmissionIso t WHERE t.SiteName = #{siteName} AND t.IsDeleted <> 1) AS ExpectedCarbonAmountTotal,              
            (SELECT SUM(ISNULL(t.CarbonAmountUnderMeasure, 0)) FROM Tzh_ExpectedEmissionIso t WHERE t.SiteName = #{siteName} AND t.IsDeleted <> 1 AND (SELECT CEL.Name FROM Tzh_CarbonEmissionLocation CEL WHERE Id = t.CarbonEmissionLocationId) = N'地盤') AS ExpectedCarbonAmountUnderMeasureSite,              
            (SELECT SUM(ISNULL(t.CarbonAmountUnderMeasure, 0)) FROM Tzh_ExpectedEmissionIso t WHERE t.SiteName = #{siteName} AND t.IsDeleted <> 1 AND (SELECT CEL.Name FROM Tzh_CarbonEmissionLocation CEL WHERE Id = t.CarbonEmissionLocationId) = N'辦公室') AS ExpectedCarbonAmountUnderMeasureOffice,              
            (SELECT SUM(ISNULL(t.CarbonAmountUnderMeasure, 0)) FROM Tzh_ExpectedEmissionIso t WHERE t.SiteName = #{siteName} AND t.IsDeleted <> 1) AS ExpectedCarbonAmountUnderMeasureTotal,              
            (SELECT SUM(ISNULL(t.CarbonReductionAmount, 0))/1000 FROM (             
            SELECT h.SiteName, CEL.Name AS CarbonEmissionLocation, t.CarbonReductionAmount             
            FROM [Tzh_EmissionReductionHead] h             
            LEFT JOIN  [Tzh_EmissionReduction] t ON t.headId = h.id AND t.IsDeleted <> 1         
			LEFT JOIN Tzh_CarbonEmissionLocation CEL ON CEL.Id = h.CarbonEmissionLocationId
            WHERE h.IsDeleted <> 1             
            ) t WHERE t.SiteName = #{siteName} AND t.CarbonEmissionLocation = N'地盤') AS CarbonReductionAmountSite,             
            (SELECT SUM(ISNULL(t.CarbonReductionAmount, 0))/1000 FROM (             
            SELECT h.SiteName, CEL.Name AS CarbonEmissionLocation, t.CarbonReductionAmount             
            FROM [Tzh_EmissionReductionHead] h             
            LEFT JOIN  [Tzh_EmissionReduction] t ON t.headId = h.id AND t.IsDeleted <> 1       
			LEFT JOIN Tzh_CarbonEmissionLocation CEL ON CEL.Id = h.CarbonEmissionLocationId        
            WHERE h.IsDeleted <> 1             
            AND t.Type = N'每月減排'
            ) t WHERE t.SiteName = #{siteName} AND t.CarbonEmissionLocation = N'辦公室') AS CarbonReductionAmountOffice,             
            (SELECT SUM(ISNULL(t.CarbonReductionAmount, 0))/1000 FROM (             
            SELECT h.SiteName, CEL.Name AS CarbonEmissionLocation, t.CarbonReductionAmount             
            FROM [Tzh_EmissionReductionHead] h             
            LEFT JOIN  [Tzh_EmissionReduction] t ON t.headId = h.id AND t.IsDeleted <> 1       
			LEFT JOIN Tzh_CarbonEmissionLocation CEL ON CEL.Id = h.CarbonEmissionLocationId        
            WHERE h.IsDeleted <> 1             
            ) t WHERE t.SiteName = #{siteName}) AS CarbonReductionAmountTotal 
            """)
    public Map summary(@Param("siteName") String siteName);

}