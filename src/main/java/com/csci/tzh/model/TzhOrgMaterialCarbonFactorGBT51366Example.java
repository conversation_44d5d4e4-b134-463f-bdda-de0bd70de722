package com.csci.tzh.model;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;

public class TzhOrgMaterialCarbonFactorGBT51366Example {
    /**
	 * This field was generated by MyBatis Generator. This field corresponds to the database table Tzh_Org_MaterialCarbonFactor_GBT51366
	 * @mbg.generated  Fri Aug 11 15:13:04 HKT 2023
	 */
	protected String orderByClause;
	/**
	 * This field was generated by MyBatis Generator. This field corresponds to the database table Tzh_Org_MaterialCarbonFactor_GBT51366
	 * @mbg.generated  Fri Aug 11 15:13:04 HKT 2023
	 */
	protected boolean distinct;
	/**
	 * This field was generated by MyBatis Generator. This field corresponds to the database table Tzh_Org_MaterialCarbonFactor_GBT51366
	 * @mbg.generated  Fri Aug 11 15:13:04 HKT 2023
	 */
	protected List<Criteria> oredCriteria;

	/**
	 * This method was generated by MyBatis Generator. This method corresponds to the database table Tzh_Org_MaterialCarbonFactor_GBT51366
	 * @mbg.generated  Fri Aug 11 15:13:04 HKT 2023
	 */
	public TzhOrgMaterialCarbonFactorGBT51366Example() {
		oredCriteria = new ArrayList<>();
	}

	/**
	 * This method was generated by MyBatis Generator. This method corresponds to the database table Tzh_Org_MaterialCarbonFactor_GBT51366
	 * @mbg.generated  Fri Aug 11 15:13:04 HKT 2023
	 */
	public void setOrderByClause(String orderByClause) {
		this.orderByClause = orderByClause;
	}

	/**
	 * This method was generated by MyBatis Generator. This method corresponds to the database table Tzh_Org_MaterialCarbonFactor_GBT51366
	 * @mbg.generated  Fri Aug 11 15:13:04 HKT 2023
	 */
	public String getOrderByClause() {
		return orderByClause;
	}

	/**
	 * This method was generated by MyBatis Generator. This method corresponds to the database table Tzh_Org_MaterialCarbonFactor_GBT51366
	 * @mbg.generated  Fri Aug 11 15:13:04 HKT 2023
	 */
	public void setDistinct(boolean distinct) {
		this.distinct = distinct;
	}

	/**
	 * This method was generated by MyBatis Generator. This method corresponds to the database table Tzh_Org_MaterialCarbonFactor_GBT51366
	 * @mbg.generated  Fri Aug 11 15:13:04 HKT 2023
	 */
	public boolean isDistinct() {
		return distinct;
	}

	/**
	 * This method was generated by MyBatis Generator. This method corresponds to the database table Tzh_Org_MaterialCarbonFactor_GBT51366
	 * @mbg.generated  Fri Aug 11 15:13:04 HKT 2023
	 */
	public List<Criteria> getOredCriteria() {
		return oredCriteria;
	}

	/**
	 * This method was generated by MyBatis Generator. This method corresponds to the database table Tzh_Org_MaterialCarbonFactor_GBT51366
	 * @mbg.generated  Fri Aug 11 15:13:04 HKT 2023
	 */
	public void or(Criteria criteria) {
		oredCriteria.add(criteria);
	}

	/**
	 * This method was generated by MyBatis Generator. This method corresponds to the database table Tzh_Org_MaterialCarbonFactor_GBT51366
	 * @mbg.generated  Fri Aug 11 15:13:04 HKT 2023
	 */
	public Criteria or() {
		Criteria criteria = createCriteriaInternal();
		oredCriteria.add(criteria);
		return criteria;
	}

	/**
	 * This method was generated by MyBatis Generator. This method corresponds to the database table Tzh_Org_MaterialCarbonFactor_GBT51366
	 * @mbg.generated  Fri Aug 11 15:13:04 HKT 2023
	 */
	public Criteria createCriteria() {
		Criteria criteria = createCriteriaInternal();
		if (oredCriteria.size() == 0) {
			oredCriteria.add(criteria);
		}
		return criteria;
	}

	/**
	 * This method was generated by MyBatis Generator. This method corresponds to the database table Tzh_Org_MaterialCarbonFactor_GBT51366
	 * @mbg.generated  Fri Aug 11 15:13:04 HKT 2023
	 */
	protected Criteria createCriteriaInternal() {
		Criteria criteria = new Criteria();
		return criteria;
	}

	/**
	 * This method was generated by MyBatis Generator. This method corresponds to the database table Tzh_Org_MaterialCarbonFactor_GBT51366
	 * @mbg.generated  Fri Aug 11 15:13:04 HKT 2023
	 */
	public void clear() {
		oredCriteria.clear();
		orderByClause = null;
		distinct = false;
	}

	/**
	 * This class was generated by MyBatis Generator. This class corresponds to the database table Tzh_Org_MaterialCarbonFactor_GBT51366
	 * @mbg.generated  Fri Aug 11 15:13:04 HKT 2023
	 */
	protected abstract static class GeneratedCriteria {
		protected List<Criterion> criteria;

		protected GeneratedCriteria() {
			super();
			criteria = new ArrayList<>();
		}

		public boolean isValid() {
			return criteria.size() > 0;
		}

		public List<Criterion> getAllCriteria() {
			return criteria;
		}

		public List<Criterion> getCriteria() {
			return criteria;
		}

		protected void addCriterion(String condition) {
			if (condition == null) {
				throw new RuntimeException("Value for condition cannot be null");
			}
			criteria.add(new Criterion(condition));
		}

		protected void addCriterion(String condition, Object value, String property) {
			if (value == null) {
				throw new RuntimeException("Value for " + property + " cannot be null");
			}
			criteria.add(new Criterion(condition, value));
		}

		protected void addCriterion(String condition, Object value1, Object value2, String property) {
			if (value1 == null || value2 == null) {
				throw new RuntimeException("Between values for " + property + " cannot be null");
			}
			criteria.add(new Criterion(condition, value1, value2));
		}

		public Criteria andIdIsNull() {
			addCriterion("Id is null");
			return (Criteria) this;
		}

		public Criteria andIdIsNotNull() {
			addCriterion("Id is not null");
			return (Criteria) this;
		}

		public Criteria andIdEqualTo(String value) {
			addCriterion("Id =", value, "id");
			return (Criteria) this;
		}

		public Criteria andIdNotEqualTo(String value) {
			addCriterion("Id <>", value, "id");
			return (Criteria) this;
		}

		public Criteria andIdGreaterThan(String value) {
			addCriterion("Id >", value, "id");
			return (Criteria) this;
		}

		public Criteria andIdGreaterThanOrEqualTo(String value) {
			addCriterion("Id >=", value, "id");
			return (Criteria) this;
		}

		public Criteria andIdLessThan(String value) {
			addCriterion("Id <", value, "id");
			return (Criteria) this;
		}

		public Criteria andIdLessThanOrEqualTo(String value) {
			addCriterion("Id <=", value, "id");
			return (Criteria) this;
		}

		public Criteria andIdLike(String value) {
			addCriterion("Id like", value, "id");
			return (Criteria) this;
		}

		public Criteria andIdNotLike(String value) {
			addCriterion("Id not like", value, "id");
			return (Criteria) this;
		}

		public Criteria andIdIn(List<String> values) {
			addCriterion("Id in", values, "id");
			return (Criteria) this;
		}

		public Criteria andIdNotIn(List<String> values) {
			addCriterion("Id not in", values, "id");
			return (Criteria) this;
		}

		public Criteria andIdBetween(String value1, String value2) {
			addCriterion("Id between", value1, value2, "id");
			return (Criteria) this;
		}

		public Criteria andIdNotBetween(String value1, String value2) {
			addCriterion("Id not between", value1, value2, "id");
			return (Criteria) this;
		}

		public Criteria andChinesenameIsNull() {
			addCriterion("ChineseName is null");
			return (Criteria) this;
		}

		public Criteria andChinesenameIsNotNull() {
			addCriterion("ChineseName is not null");
			return (Criteria) this;
		}

		public Criteria andChinesenameEqualTo(String value) {
			addCriterion("ChineseName =", value, "chinesename");
			return (Criteria) this;
		}

		public Criteria andChinesenameNotEqualTo(String value) {
			addCriterion("ChineseName <>", value, "chinesename");
			return (Criteria) this;
		}

		public Criteria andChinesenameGreaterThan(String value) {
			addCriterion("ChineseName >", value, "chinesename");
			return (Criteria) this;
		}

		public Criteria andChinesenameGreaterThanOrEqualTo(String value) {
			addCriterion("ChineseName >=", value, "chinesename");
			return (Criteria) this;
		}

		public Criteria andChinesenameLessThan(String value) {
			addCriterion("ChineseName <", value, "chinesename");
			return (Criteria) this;
		}

		public Criteria andChinesenameLessThanOrEqualTo(String value) {
			addCriterion("ChineseName <=", value, "chinesename");
			return (Criteria) this;
		}

		public Criteria andChinesenameLike(String value) {
			addCriterion("ChineseName like", value, "chinesename");
			return (Criteria) this;
		}

		public Criteria andChinesenameNotLike(String value) {
			addCriterion("ChineseName not like", value, "chinesename");
			return (Criteria) this;
		}

		public Criteria andChinesenameIn(List<String> values) {
			addCriterion("ChineseName in", values, "chinesename");
			return (Criteria) this;
		}

		public Criteria andChinesenameNotIn(List<String> values) {
			addCriterion("ChineseName not in", values, "chinesename");
			return (Criteria) this;
		}

		public Criteria andChinesenameBetween(String value1, String value2) {
			addCriterion("ChineseName between", value1, value2, "chinesename");
			return (Criteria) this;
		}

		public Criteria andChinesenameNotBetween(String value1, String value2) {
			addCriterion("ChineseName not between", value1, value2, "chinesename");
			return (Criteria) this;
		}

		public Criteria andSpecificationIsNull() {
			addCriterion("Specification is null");
			return (Criteria) this;
		}

		public Criteria andSpecificationIsNotNull() {
			addCriterion("Specification is not null");
			return (Criteria) this;
		}

		public Criteria andSpecificationEqualTo(String value) {
			addCriterion("Specification =", value, "specification");
			return (Criteria) this;
		}

		public Criteria andSpecificationNotEqualTo(String value) {
			addCriterion("Specification <>", value, "specification");
			return (Criteria) this;
		}

		public Criteria andSpecificationGreaterThan(String value) {
			addCriterion("Specification >", value, "specification");
			return (Criteria) this;
		}

		public Criteria andSpecificationGreaterThanOrEqualTo(String value) {
			addCriterion("Specification >=", value, "specification");
			return (Criteria) this;
		}

		public Criteria andSpecificationLessThan(String value) {
			addCriterion("Specification <", value, "specification");
			return (Criteria) this;
		}

		public Criteria andSpecificationLessThanOrEqualTo(String value) {
			addCriterion("Specification <=", value, "specification");
			return (Criteria) this;
		}

		public Criteria andSpecificationLike(String value) {
			addCriterion("Specification like", value, "specification");
			return (Criteria) this;
		}

		public Criteria andSpecificationNotLike(String value) {
			addCriterion("Specification not like", value, "specification");
			return (Criteria) this;
		}

		public Criteria andSpecificationIn(List<String> values) {
			addCriterion("Specification in", values, "specification");
			return (Criteria) this;
		}

		public Criteria andSpecificationNotIn(List<String> values) {
			addCriterion("Specification not in", values, "specification");
			return (Criteria) this;
		}

		public Criteria andSpecificationBetween(String value1, String value2) {
			addCriterion("Specification between", value1, value2, "specification");
			return (Criteria) this;
		}

		public Criteria andSpecificationNotBetween(String value1, String value2) {
			addCriterion("Specification not between", value1, value2, "specification");
			return (Criteria) this;
		}

		public Criteria andEnergyconsumptionIsNull() {
			addCriterion("EnergyConsumption is null");
			return (Criteria) this;
		}

		public Criteria andEnergyconsumptionIsNotNull() {
			addCriterion("EnergyConsumption is not null");
			return (Criteria) this;
		}

		public Criteria andEnergyconsumptionEqualTo(String value) {
			addCriterion("EnergyConsumption =", value, "energyconsumption");
			return (Criteria) this;
		}

		public Criteria andEnergyconsumptionNotEqualTo(String value) {
			addCriterion("EnergyConsumption <>", value, "energyconsumption");
			return (Criteria) this;
		}

		public Criteria andEnergyconsumptionGreaterThan(String value) {
			addCriterion("EnergyConsumption >", value, "energyconsumption");
			return (Criteria) this;
		}

		public Criteria andEnergyconsumptionGreaterThanOrEqualTo(String value) {
			addCriterion("EnergyConsumption >=", value, "energyconsumption");
			return (Criteria) this;
		}

		public Criteria andEnergyconsumptionLessThan(String value) {
			addCriterion("EnergyConsumption <", value, "energyconsumption");
			return (Criteria) this;
		}

		public Criteria andEnergyconsumptionLessThanOrEqualTo(String value) {
			addCriterion("EnergyConsumption <=", value, "energyconsumption");
			return (Criteria) this;
		}

		public Criteria andEnergyconsumptionLike(String value) {
			addCriterion("EnergyConsumption like", value, "energyconsumption");
			return (Criteria) this;
		}

		public Criteria andEnergyconsumptionNotLike(String value) {
			addCriterion("EnergyConsumption not like", value, "energyconsumption");
			return (Criteria) this;
		}

		public Criteria andEnergyconsumptionIn(List<String> values) {
			addCriterion("EnergyConsumption in", values, "energyconsumption");
			return (Criteria) this;
		}

		public Criteria andEnergyconsumptionNotIn(List<String> values) {
			addCriterion("EnergyConsumption not in", values, "energyconsumption");
			return (Criteria) this;
		}

		public Criteria andEnergyconsumptionBetween(String value1, String value2) {
			addCriterion("EnergyConsumption between", value1, value2, "energyconsumption");
			return (Criteria) this;
		}

		public Criteria andEnergyconsumptionNotBetween(String value1, String value2) {
			addCriterion("EnergyConsumption not between", value1, value2, "energyconsumption");
			return (Criteria) this;
		}

		public Criteria andHeatco2factorIsNull() {
			addCriterion("HeatCo2Factor is null");
			return (Criteria) this;
		}

		public Criteria andHeatco2factorIsNotNull() {
			addCriterion("HeatCo2Factor is not null");
			return (Criteria) this;
		}

		public Criteria andHeatco2factorEqualTo(BigDecimal value) {
			addCriterion("HeatCo2Factor =", value, "heatco2factor");
			return (Criteria) this;
		}

		public Criteria andHeatco2factorNotEqualTo(BigDecimal value) {
			addCriterion("HeatCo2Factor <>", value, "heatco2factor");
			return (Criteria) this;
		}

		public Criteria andHeatco2factorGreaterThan(BigDecimal value) {
			addCriterion("HeatCo2Factor >", value, "heatco2factor");
			return (Criteria) this;
		}

		public Criteria andHeatco2factorGreaterThanOrEqualTo(BigDecimal value) {
			addCriterion("HeatCo2Factor >=", value, "heatco2factor");
			return (Criteria) this;
		}

		public Criteria andHeatco2factorLessThan(BigDecimal value) {
			addCriterion("HeatCo2Factor <", value, "heatco2factor");
			return (Criteria) this;
		}

		public Criteria andHeatco2factorLessThanOrEqualTo(BigDecimal value) {
			addCriterion("HeatCo2Factor <=", value, "heatco2factor");
			return (Criteria) this;
		}

		public Criteria andHeatco2factorIn(List<BigDecimal> values) {
			addCriterion("HeatCo2Factor in", values, "heatco2factor");
			return (Criteria) this;
		}

		public Criteria andHeatco2factorNotIn(List<BigDecimal> values) {
			addCriterion("HeatCo2Factor not in", values, "heatco2factor");
			return (Criteria) this;
		}

		public Criteria andHeatco2factorBetween(BigDecimal value1, BigDecimal value2) {
			addCriterion("HeatCo2Factor between", value1, value2, "heatco2factor");
			return (Criteria) this;
		}

		public Criteria andHeatco2factorNotBetween(BigDecimal value1, BigDecimal value2) {
			addCriterion("HeatCo2Factor not between", value1, value2, "heatco2factor");
			return (Criteria) this;
		}

		public Criteria andEffectiveco2factordefaultIsNull() {
			addCriterion("EffectiveCo2FactorDefault is null");
			return (Criteria) this;
		}

		public Criteria andEffectiveco2factordefaultIsNotNull() {
			addCriterion("EffectiveCo2FactorDefault is not null");
			return (Criteria) this;
		}

		public Criteria andEffectiveco2factordefaultEqualTo(BigDecimal value) {
			addCriterion("EffectiveCo2FactorDefault =", value, "effectiveco2factordefault");
			return (Criteria) this;
		}

		public Criteria andEffectiveco2factordefaultNotEqualTo(BigDecimal value) {
			addCriterion("EffectiveCo2FactorDefault <>", value, "effectiveco2factordefault");
			return (Criteria) this;
		}

		public Criteria andEffectiveco2factordefaultGreaterThan(BigDecimal value) {
			addCriterion("EffectiveCo2FactorDefault >", value, "effectiveco2factordefault");
			return (Criteria) this;
		}

		public Criteria andEffectiveco2factordefaultGreaterThanOrEqualTo(BigDecimal value) {
			addCriterion("EffectiveCo2FactorDefault >=", value, "effectiveco2factordefault");
			return (Criteria) this;
		}

		public Criteria andEffectiveco2factordefaultLessThan(BigDecimal value) {
			addCriterion("EffectiveCo2FactorDefault <", value, "effectiveco2factordefault");
			return (Criteria) this;
		}

		public Criteria andEffectiveco2factordefaultLessThanOrEqualTo(BigDecimal value) {
			addCriterion("EffectiveCo2FactorDefault <=", value, "effectiveco2factordefault");
			return (Criteria) this;
		}

		public Criteria andEffectiveco2factordefaultIn(List<BigDecimal> values) {
			addCriterion("EffectiveCo2FactorDefault in", values, "effectiveco2factordefault");
			return (Criteria) this;
		}

		public Criteria andEffectiveco2factordefaultNotIn(List<BigDecimal> values) {
			addCriterion("EffectiveCo2FactorDefault not in", values, "effectiveco2factordefault");
			return (Criteria) this;
		}

		public Criteria andEffectiveco2factordefaultBetween(BigDecimal value1, BigDecimal value2) {
			addCriterion("EffectiveCo2FactorDefault between", value1, value2, "effectiveco2factordefault");
			return (Criteria) this;
		}

		public Criteria andEffectiveco2factordefaultNotBetween(BigDecimal value1, BigDecimal value2) {
			addCriterion("EffectiveCo2FactorDefault not between", value1, value2, "effectiveco2factordefault");
			return (Criteria) this;
		}

		public Criteria andEffectiveco2factorlowerIsNull() {
			addCriterion("EffectiveCo2FactorLower is null");
			return (Criteria) this;
		}

		public Criteria andEffectiveco2factorlowerIsNotNull() {
			addCriterion("EffectiveCo2FactorLower is not null");
			return (Criteria) this;
		}

		public Criteria andEffectiveco2factorlowerEqualTo(BigDecimal value) {
			addCriterion("EffectiveCo2FactorLower =", value, "effectiveco2factorlower");
			return (Criteria) this;
		}

		public Criteria andEffectiveco2factorlowerNotEqualTo(BigDecimal value) {
			addCriterion("EffectiveCo2FactorLower <>", value, "effectiveco2factorlower");
			return (Criteria) this;
		}

		public Criteria andEffectiveco2factorlowerGreaterThan(BigDecimal value) {
			addCriterion("EffectiveCo2FactorLower >", value, "effectiveco2factorlower");
			return (Criteria) this;
		}

		public Criteria andEffectiveco2factorlowerGreaterThanOrEqualTo(BigDecimal value) {
			addCriterion("EffectiveCo2FactorLower >=", value, "effectiveco2factorlower");
			return (Criteria) this;
		}

		public Criteria andEffectiveco2factorlowerLessThan(BigDecimal value) {
			addCriterion("EffectiveCo2FactorLower <", value, "effectiveco2factorlower");
			return (Criteria) this;
		}

		public Criteria andEffectiveco2factorlowerLessThanOrEqualTo(BigDecimal value) {
			addCriterion("EffectiveCo2FactorLower <=", value, "effectiveco2factorlower");
			return (Criteria) this;
		}

		public Criteria andEffectiveco2factorlowerIn(List<BigDecimal> values) {
			addCriterion("EffectiveCo2FactorLower in", values, "effectiveco2factorlower");
			return (Criteria) this;
		}

		public Criteria andEffectiveco2factorlowerNotIn(List<BigDecimal> values) {
			addCriterion("EffectiveCo2FactorLower not in", values, "effectiveco2factorlower");
			return (Criteria) this;
		}

		public Criteria andEffectiveco2factorlowerBetween(BigDecimal value1, BigDecimal value2) {
			addCriterion("EffectiveCo2FactorLower between", value1, value2, "effectiveco2factorlower");
			return (Criteria) this;
		}

		public Criteria andEffectiveco2factorlowerNotBetween(BigDecimal value1, BigDecimal value2) {
			addCriterion("EffectiveCo2FactorLower not between", value1, value2, "effectiveco2factorlower");
			return (Criteria) this;
		}

		public Criteria andEffectiveco2factorupperIsNull() {
			addCriterion("EffectiveCo2FactorUpper is null");
			return (Criteria) this;
		}

		public Criteria andEffectiveco2factorupperIsNotNull() {
			addCriterion("EffectiveCo2FactorUpper is not null");
			return (Criteria) this;
		}

		public Criteria andEffectiveco2factorupperEqualTo(BigDecimal value) {
			addCriterion("EffectiveCo2FactorUpper =", value, "effectiveco2factorupper");
			return (Criteria) this;
		}

		public Criteria andEffectiveco2factorupperNotEqualTo(BigDecimal value) {
			addCriterion("EffectiveCo2FactorUpper <>", value, "effectiveco2factorupper");
			return (Criteria) this;
		}

		public Criteria andEffectiveco2factorupperGreaterThan(BigDecimal value) {
			addCriterion("EffectiveCo2FactorUpper >", value, "effectiveco2factorupper");
			return (Criteria) this;
		}

		public Criteria andEffectiveco2factorupperGreaterThanOrEqualTo(BigDecimal value) {
			addCriterion("EffectiveCo2FactorUpper >=", value, "effectiveco2factorupper");
			return (Criteria) this;
		}

		public Criteria andEffectiveco2factorupperLessThan(BigDecimal value) {
			addCriterion("EffectiveCo2FactorUpper <", value, "effectiveco2factorupper");
			return (Criteria) this;
		}

		public Criteria andEffectiveco2factorupperLessThanOrEqualTo(BigDecimal value) {
			addCriterion("EffectiveCo2FactorUpper <=", value, "effectiveco2factorupper");
			return (Criteria) this;
		}

		public Criteria andEffectiveco2factorupperIn(List<BigDecimal> values) {
			addCriterion("EffectiveCo2FactorUpper in", values, "effectiveco2factorupper");
			return (Criteria) this;
		}

		public Criteria andEffectiveco2factorupperNotIn(List<BigDecimal> values) {
			addCriterion("EffectiveCo2FactorUpper not in", values, "effectiveco2factorupper");
			return (Criteria) this;
		}

		public Criteria andEffectiveco2factorupperBetween(BigDecimal value1, BigDecimal value2) {
			addCriterion("EffectiveCo2FactorUpper between", value1, value2, "effectiveco2factorupper");
			return (Criteria) this;
		}

		public Criteria andEffectiveco2factorupperNotBetween(BigDecimal value1, BigDecimal value2) {
			addCriterion("EffectiveCo2FactorUpper not between", value1, value2, "effectiveco2factorupper");
			return (Criteria) this;
		}

		public Criteria andCarbonfactorIsNull() {
			addCriterion("CarbonFactor is null");
			return (Criteria) this;
		}

		public Criteria andCarbonfactorIsNotNull() {
			addCriterion("CarbonFactor is not null");
			return (Criteria) this;
		}

		public Criteria andCarbonfactorEqualTo(BigDecimal value) {
			addCriterion("CarbonFactor =", value, "carbonfactor");
			return (Criteria) this;
		}

		public Criteria andCarbonfactorNotEqualTo(BigDecimal value) {
			addCriterion("CarbonFactor <>", value, "carbonfactor");
			return (Criteria) this;
		}

		public Criteria andCarbonfactorGreaterThan(BigDecimal value) {
			addCriterion("CarbonFactor >", value, "carbonfactor");
			return (Criteria) this;
		}

		public Criteria andCarbonfactorGreaterThanOrEqualTo(BigDecimal value) {
			addCriterion("CarbonFactor >=", value, "carbonfactor");
			return (Criteria) this;
		}

		public Criteria andCarbonfactorLessThan(BigDecimal value) {
			addCriterion("CarbonFactor <", value, "carbonfactor");
			return (Criteria) this;
		}

		public Criteria andCarbonfactorLessThanOrEqualTo(BigDecimal value) {
			addCriterion("CarbonFactor <=", value, "carbonfactor");
			return (Criteria) this;
		}

		public Criteria andCarbonfactorIn(List<BigDecimal> values) {
			addCriterion("CarbonFactor in", values, "carbonfactor");
			return (Criteria) this;
		}

		public Criteria andCarbonfactorNotIn(List<BigDecimal> values) {
			addCriterion("CarbonFactor not in", values, "carbonfactor");
			return (Criteria) this;
		}

		public Criteria andCarbonfactorBetween(BigDecimal value1, BigDecimal value2) {
			addCriterion("CarbonFactor between", value1, value2, "carbonfactor");
			return (Criteria) this;
		}

		public Criteria andCarbonfactorNotBetween(BigDecimal value1, BigDecimal value2) {
			addCriterion("CarbonFactor not between", value1, value2, "carbonfactor");
			return (Criteria) this;
		}

		public Criteria andCarbonfactorunitIsNull() {
			addCriterion("CarbonFactorUnit is null");
			return (Criteria) this;
		}

		public Criteria andCarbonfactorunitIsNotNull() {
			addCriterion("CarbonFactorUnit is not null");
			return (Criteria) this;
		}

		public Criteria andCarbonfactorunitEqualTo(String value) {
			addCriterion("CarbonFactorUnit =", value, "carbonfactorunit");
			return (Criteria) this;
		}

		public Criteria andCarbonfactorunitNotEqualTo(String value) {
			addCriterion("CarbonFactorUnit <>", value, "carbonfactorunit");
			return (Criteria) this;
		}

		public Criteria andCarbonfactorunitGreaterThan(String value) {
			addCriterion("CarbonFactorUnit >", value, "carbonfactorunit");
			return (Criteria) this;
		}

		public Criteria andCarbonfactorunitGreaterThanOrEqualTo(String value) {
			addCriterion("CarbonFactorUnit >=", value, "carbonfactorunit");
			return (Criteria) this;
		}

		public Criteria andCarbonfactorunitLessThan(String value) {
			addCriterion("CarbonFactorUnit <", value, "carbonfactorunit");
			return (Criteria) this;
		}

		public Criteria andCarbonfactorunitLessThanOrEqualTo(String value) {
			addCriterion("CarbonFactorUnit <=", value, "carbonfactorunit");
			return (Criteria) this;
		}

		public Criteria andCarbonfactorunitLike(String value) {
			addCriterion("CarbonFactorUnit like", value, "carbonfactorunit");
			return (Criteria) this;
		}

		public Criteria andCarbonfactorunitNotLike(String value) {
			addCriterion("CarbonFactorUnit not like", value, "carbonfactorunit");
			return (Criteria) this;
		}

		public Criteria andCarbonfactorunitIn(List<String> values) {
			addCriterion("CarbonFactorUnit in", values, "carbonfactorunit");
			return (Criteria) this;
		}

		public Criteria andCarbonfactorunitNotIn(List<String> values) {
			addCriterion("CarbonFactorUnit not in", values, "carbonfactorunit");
			return (Criteria) this;
		}

		public Criteria andCarbonfactorunitBetween(String value1, String value2) {
			addCriterion("CarbonFactorUnit between", value1, value2, "carbonfactorunit");
			return (Criteria) this;
		}

		public Criteria andCarbonfactorunitNotBetween(String value1, String value2) {
			addCriterion("CarbonFactorUnit not between", value1, value2, "carbonfactorunit");
			return (Criteria) this;
		}

		public Criteria andCreatedbyIsNull() {
			addCriterion("CreatedBy is null");
			return (Criteria) this;
		}

		public Criteria andCreatedbyIsNotNull() {
			addCriterion("CreatedBy is not null");
			return (Criteria) this;
		}

		public Criteria andCreatedbyEqualTo(String value) {
			addCriterion("CreatedBy =", value, "createdby");
			return (Criteria) this;
		}

		public Criteria andCreatedbyNotEqualTo(String value) {
			addCriterion("CreatedBy <>", value, "createdby");
			return (Criteria) this;
		}

		public Criteria andCreatedbyGreaterThan(String value) {
			addCriterion("CreatedBy >", value, "createdby");
			return (Criteria) this;
		}

		public Criteria andCreatedbyGreaterThanOrEqualTo(String value) {
			addCriterion("CreatedBy >=", value, "createdby");
			return (Criteria) this;
		}

		public Criteria andCreatedbyLessThan(String value) {
			addCriterion("CreatedBy <", value, "createdby");
			return (Criteria) this;
		}

		public Criteria andCreatedbyLessThanOrEqualTo(String value) {
			addCriterion("CreatedBy <=", value, "createdby");
			return (Criteria) this;
		}

		public Criteria andCreatedbyLike(String value) {
			addCriterion("CreatedBy like", value, "createdby");
			return (Criteria) this;
		}

		public Criteria andCreatedbyNotLike(String value) {
			addCriterion("CreatedBy not like", value, "createdby");
			return (Criteria) this;
		}

		public Criteria andCreatedbyIn(List<String> values) {
			addCriterion("CreatedBy in", values, "createdby");
			return (Criteria) this;
		}

		public Criteria andCreatedbyNotIn(List<String> values) {
			addCriterion("CreatedBy not in", values, "createdby");
			return (Criteria) this;
		}

		public Criteria andCreatedbyBetween(String value1, String value2) {
			addCriterion("CreatedBy between", value1, value2, "createdby");
			return (Criteria) this;
		}

		public Criteria andCreatedbyNotBetween(String value1, String value2) {
			addCriterion("CreatedBy not between", value1, value2, "createdby");
			return (Criteria) this;
		}

		public Criteria andCreatedtimeIsNull() {
			addCriterion("CreatedTime is null");
			return (Criteria) this;
		}

		public Criteria andCreatedtimeIsNotNull() {
			addCriterion("CreatedTime is not null");
			return (Criteria) this;
		}

		public Criteria andCreatedtimeEqualTo(LocalDateTime value) {
			addCriterion("CreatedTime =", value, "createdtime");
			return (Criteria) this;
		}

		public Criteria andCreatedtimeNotEqualTo(LocalDateTime value) {
			addCriterion("CreatedTime <>", value, "createdtime");
			return (Criteria) this;
		}

		public Criteria andCreatedtimeGreaterThan(LocalDateTime value) {
			addCriterion("CreatedTime >", value, "createdtime");
			return (Criteria) this;
		}

		public Criteria andCreatedtimeGreaterThanOrEqualTo(LocalDateTime value) {
			addCriterion("CreatedTime >=", value, "createdtime");
			return (Criteria) this;
		}

		public Criteria andCreatedtimeLessThan(LocalDateTime value) {
			addCriterion("CreatedTime <", value, "createdtime");
			return (Criteria) this;
		}

		public Criteria andCreatedtimeLessThanOrEqualTo(LocalDateTime value) {
			addCriterion("CreatedTime <=", value, "createdtime");
			return (Criteria) this;
		}

		public Criteria andCreatedtimeIn(List<LocalDateTime> values) {
			addCriterion("CreatedTime in", values, "createdtime");
			return (Criteria) this;
		}

		public Criteria andCreatedtimeNotIn(List<LocalDateTime> values) {
			addCriterion("CreatedTime not in", values, "createdtime");
			return (Criteria) this;
		}

		public Criteria andCreatedtimeBetween(LocalDateTime value1, LocalDateTime value2) {
			addCriterion("CreatedTime between", value1, value2, "createdtime");
			return (Criteria) this;
		}

		public Criteria andCreatedtimeNotBetween(LocalDateTime value1, LocalDateTime value2) {
			addCriterion("CreatedTime not between", value1, value2, "createdtime");
			return (Criteria) this;
		}

		public Criteria andDeletedbyIsNull() {
			addCriterion("DeletedBy is null");
			return (Criteria) this;
		}

		public Criteria andDeletedbyIsNotNull() {
			addCriterion("DeletedBy is not null");
			return (Criteria) this;
		}

		public Criteria andDeletedbyEqualTo(String value) {
			addCriterion("DeletedBy =", value, "deletedby");
			return (Criteria) this;
		}

		public Criteria andDeletedbyNotEqualTo(String value) {
			addCriterion("DeletedBy <>", value, "deletedby");
			return (Criteria) this;
		}

		public Criteria andDeletedbyGreaterThan(String value) {
			addCriterion("DeletedBy >", value, "deletedby");
			return (Criteria) this;
		}

		public Criteria andDeletedbyGreaterThanOrEqualTo(String value) {
			addCriterion("DeletedBy >=", value, "deletedby");
			return (Criteria) this;
		}

		public Criteria andDeletedbyLessThan(String value) {
			addCriterion("DeletedBy <", value, "deletedby");
			return (Criteria) this;
		}

		public Criteria andDeletedbyLessThanOrEqualTo(String value) {
			addCriterion("DeletedBy <=", value, "deletedby");
			return (Criteria) this;
		}

		public Criteria andDeletedbyLike(String value) {
			addCriterion("DeletedBy like", value, "deletedby");
			return (Criteria) this;
		}

		public Criteria andDeletedbyNotLike(String value) {
			addCriterion("DeletedBy not like", value, "deletedby");
			return (Criteria) this;
		}

		public Criteria andDeletedbyIn(List<String> values) {
			addCriterion("DeletedBy in", values, "deletedby");
			return (Criteria) this;
		}

		public Criteria andDeletedbyNotIn(List<String> values) {
			addCriterion("DeletedBy not in", values, "deletedby");
			return (Criteria) this;
		}

		public Criteria andDeletedbyBetween(String value1, String value2) {
			addCriterion("DeletedBy between", value1, value2, "deletedby");
			return (Criteria) this;
		}

		public Criteria andDeletedbyNotBetween(String value1, String value2) {
			addCriterion("DeletedBy not between", value1, value2, "deletedby");
			return (Criteria) this;
		}

		public Criteria andDeletedtimeIsNull() {
			addCriterion("DeletedTime is null");
			return (Criteria) this;
		}

		public Criteria andDeletedtimeIsNotNull() {
			addCriterion("DeletedTime is not null");
			return (Criteria) this;
		}

		public Criteria andDeletedtimeEqualTo(LocalDateTime value) {
			addCriterion("DeletedTime =", value, "deletedtime");
			return (Criteria) this;
		}

		public Criteria andDeletedtimeNotEqualTo(LocalDateTime value) {
			addCriterion("DeletedTime <>", value, "deletedtime");
			return (Criteria) this;
		}

		public Criteria andDeletedtimeGreaterThan(LocalDateTime value) {
			addCriterion("DeletedTime >", value, "deletedtime");
			return (Criteria) this;
		}

		public Criteria andDeletedtimeGreaterThanOrEqualTo(LocalDateTime value) {
			addCriterion("DeletedTime >=", value, "deletedtime");
			return (Criteria) this;
		}

		public Criteria andDeletedtimeLessThan(LocalDateTime value) {
			addCriterion("DeletedTime <", value, "deletedtime");
			return (Criteria) this;
		}

		public Criteria andDeletedtimeLessThanOrEqualTo(LocalDateTime value) {
			addCriterion("DeletedTime <=", value, "deletedtime");
			return (Criteria) this;
		}

		public Criteria andDeletedtimeIn(List<LocalDateTime> values) {
			addCriterion("DeletedTime in", values, "deletedtime");
			return (Criteria) this;
		}

		public Criteria andDeletedtimeNotIn(List<LocalDateTime> values) {
			addCriterion("DeletedTime not in", values, "deletedtime");
			return (Criteria) this;
		}

		public Criteria andDeletedtimeBetween(LocalDateTime value1, LocalDateTime value2) {
			addCriterion("DeletedTime between", value1, value2, "deletedtime");
			return (Criteria) this;
		}

		public Criteria andDeletedtimeNotBetween(LocalDateTime value1, LocalDateTime value2) {
			addCriterion("DeletedTime not between", value1, value2, "deletedtime");
			return (Criteria) this;
		}

		public Criteria andIsdeletedIsNull() {
			addCriterion("IsDeleted is null");
			return (Criteria) this;
		}

		public Criteria andIsdeletedIsNotNull() {
			addCriterion("IsDeleted is not null");
			return (Criteria) this;
		}

		public Criteria andIsdeletedEqualTo(Boolean value) {
			addCriterion("IsDeleted =", value, "isdeleted");
			return (Criteria) this;
		}

		public Criteria andIsdeletedNotEqualTo(Boolean value) {
			addCriterion("IsDeleted <>", value, "isdeleted");
			return (Criteria) this;
		}

		public Criteria andIsdeletedGreaterThan(Boolean value) {
			addCriterion("IsDeleted >", value, "isdeleted");
			return (Criteria) this;
		}

		public Criteria andIsdeletedGreaterThanOrEqualTo(Boolean value) {
			addCriterion("IsDeleted >=", value, "isdeleted");
			return (Criteria) this;
		}

		public Criteria andIsdeletedLessThan(Boolean value) {
			addCriterion("IsDeleted <", value, "isdeleted");
			return (Criteria) this;
		}

		public Criteria andIsdeletedLessThanOrEqualTo(Boolean value) {
			addCriterion("IsDeleted <=", value, "isdeleted");
			return (Criteria) this;
		}

		public Criteria andIsdeletedIn(List<Boolean> values) {
			addCriterion("IsDeleted in", values, "isdeleted");
			return (Criteria) this;
		}

		public Criteria andIsdeletedNotIn(List<Boolean> values) {
			addCriterion("IsDeleted not in", values, "isdeleted");
			return (Criteria) this;
		}

		public Criteria andIsdeletedBetween(Boolean value1, Boolean value2) {
			addCriterion("IsDeleted between", value1, value2, "isdeleted");
			return (Criteria) this;
		}

		public Criteria andIsdeletedNotBetween(Boolean value1, Boolean value2) {
			addCriterion("IsDeleted not between", value1, value2, "isdeleted");
			return (Criteria) this;
		}
	}

	/**
	 * This class was generated by MyBatis Generator. This class corresponds to the database table Tzh_Org_MaterialCarbonFactor_GBT51366
	 * @mbg.generated  Fri Aug 11 15:13:04 HKT 2023
	 */
	public static class Criterion {
		private String condition;
		private Object value;
		private Object secondValue;
		private boolean noValue;
		private boolean singleValue;
		private boolean betweenValue;
		private boolean listValue;
		private String typeHandler;

		public String getCondition() {
			return condition;
		}

		public Object getValue() {
			return value;
		}

		public Object getSecondValue() {
			return secondValue;
		}

		public boolean isNoValue() {
			return noValue;
		}

		public boolean isSingleValue() {
			return singleValue;
		}

		public boolean isBetweenValue() {
			return betweenValue;
		}

		public boolean isListValue() {
			return listValue;
		}

		public String getTypeHandler() {
			return typeHandler;
		}

		protected Criterion(String condition) {
			super();
			this.condition = condition;
			this.typeHandler = null;
			this.noValue = true;
		}

		protected Criterion(String condition, Object value, String typeHandler) {
			super();
			this.condition = condition;
			this.value = value;
			this.typeHandler = typeHandler;
			if (value instanceof List<?>) {
				this.listValue = true;
			} else {
				this.singleValue = true;
			}
		}

		protected Criterion(String condition, Object value) {
			this(condition, value, null);
		}

		protected Criterion(String condition, Object value, Object secondValue, String typeHandler) {
			super();
			this.condition = condition;
			this.value = value;
			this.secondValue = secondValue;
			this.typeHandler = typeHandler;
			this.betweenValue = true;
		}

		protected Criterion(String condition, Object value, Object secondValue) {
			this(condition, value, secondValue, null);
		}
	}

	/**
     * This class was generated by MyBatis Generator.
     * This class corresponds to the database table Tzh_Org_MaterialCarbonFactor_GBT51366
     *
     * @mbg.generated do_not_delete_during_merge Fri Aug 11 15:12:57 HKT 2023
     */
    public static class Criteria extends GeneratedCriteria {
        protected Criteria() {
            super();
        }
    }
}