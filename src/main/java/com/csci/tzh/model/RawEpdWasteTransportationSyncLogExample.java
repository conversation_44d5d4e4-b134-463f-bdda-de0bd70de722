package com.csci.tzh.model;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;

public class RawEpdWasteTransportationSyncLogExample {
    /**
	 * This field was generated by MyBatis Generator. This field corresponds to the database table RawEpdWasteTransportation_SyncLog
	 * @mbg.generated  Wed Dec 14 15:33:44 HKT 2022
	 */
	protected String orderByClause;
	/**
	 * This field was generated by MyBatis Generator. This field corresponds to the database table RawEpdWasteTransportation_SyncLog
	 * @mbg.generated  Wed Dec 14 15:33:44 HKT 2022
	 */
	protected boolean distinct;
	/**
	 * This field was generated by MyBatis Generator. This field corresponds to the database table RawEpdWasteTransportation_SyncLog
	 * @mbg.generated  Wed Dec 14 15:33:44 HKT 2022
	 */
	protected List<Criteria> oredCriteria;

	/**
	 * This method was generated by MyBatis Generator. This method corresponds to the database table RawEpdWasteTransportation_SyncLog
	 * @mbg.generated  Wed Dec 14 15:33:44 HKT 2022
	 */
	public RawEpdWasteTransportationSyncLogExample() {
		oredCriteria = new ArrayList<>();
	}

	/**
	 * This method was generated by MyBatis Generator. This method corresponds to the database table RawEpdWasteTransportation_SyncLog
	 * @mbg.generated  Wed Dec 14 15:33:44 HKT 2022
	 */
	public void setOrderByClause(String orderByClause) {
		this.orderByClause = orderByClause;
	}

	/**
	 * This method was generated by MyBatis Generator. This method corresponds to the database table RawEpdWasteTransportation_SyncLog
	 * @mbg.generated  Wed Dec 14 15:33:44 HKT 2022
	 */
	public String getOrderByClause() {
		return orderByClause;
	}

	/**
	 * This method was generated by MyBatis Generator. This method corresponds to the database table RawEpdWasteTransportation_SyncLog
	 * @mbg.generated  Wed Dec 14 15:33:44 HKT 2022
	 */
	public void setDistinct(boolean distinct) {
		this.distinct = distinct;
	}

	/**
	 * This method was generated by MyBatis Generator. This method corresponds to the database table RawEpdWasteTransportation_SyncLog
	 * @mbg.generated  Wed Dec 14 15:33:44 HKT 2022
	 */
	public boolean isDistinct() {
		return distinct;
	}

	/**
	 * This method was generated by MyBatis Generator. This method corresponds to the database table RawEpdWasteTransportation_SyncLog
	 * @mbg.generated  Wed Dec 14 15:33:44 HKT 2022
	 */
	public List<Criteria> getOredCriteria() {
		return oredCriteria;
	}

	/**
	 * This method was generated by MyBatis Generator. This method corresponds to the database table RawEpdWasteTransportation_SyncLog
	 * @mbg.generated  Wed Dec 14 15:33:44 HKT 2022
	 */
	public void or(Criteria criteria) {
		oredCriteria.add(criteria);
	}

	/**
	 * This method was generated by MyBatis Generator. This method corresponds to the database table RawEpdWasteTransportation_SyncLog
	 * @mbg.generated  Wed Dec 14 15:33:44 HKT 2022
	 */
	public Criteria or() {
		Criteria criteria = createCriteriaInternal();
		oredCriteria.add(criteria);
		return criteria;
	}

	/**
	 * This method was generated by MyBatis Generator. This method corresponds to the database table RawEpdWasteTransportation_SyncLog
	 * @mbg.generated  Wed Dec 14 15:33:44 HKT 2022
	 */
	public Criteria createCriteria() {
		Criteria criteria = createCriteriaInternal();
		if (oredCriteria.size() == 0) {
			oredCriteria.add(criteria);
		}
		return criteria;
	}

	/**
	 * This method was generated by MyBatis Generator. This method corresponds to the database table RawEpdWasteTransportation_SyncLog
	 * @mbg.generated  Wed Dec 14 15:33:44 HKT 2022
	 */
	protected Criteria createCriteriaInternal() {
		Criteria criteria = new Criteria();
		return criteria;
	}

	/**
	 * This method was generated by MyBatis Generator. This method corresponds to the database table RawEpdWasteTransportation_SyncLog
	 * @mbg.generated  Wed Dec 14 15:33:44 HKT 2022
	 */
	public void clear() {
		oredCriteria.clear();
		orderByClause = null;
		distinct = false;
	}

	/**
	 * This class was generated by MyBatis Generator. This class corresponds to the database table RawEpdWasteTransportation_SyncLog
	 * @mbg.generated  Wed Dec 14 15:33:44 HKT 2022
	 */
	protected abstract static class GeneratedCriteria {
		protected List<Criterion> criteria;

		protected GeneratedCriteria() {
			super();
			criteria = new ArrayList<>();
		}

		public boolean isValid() {
			return criteria.size() > 0;
		}

		public List<Criterion> getAllCriteria() {
			return criteria;
		}

		public List<Criterion> getCriteria() {
			return criteria;
		}

		protected void addCriterion(String condition) {
			if (condition == null) {
				throw new RuntimeException("Value for condition cannot be null");
			}
			criteria.add(new Criterion(condition));
		}

		protected void addCriterion(String condition, Object value, String property) {
			if (value == null) {
				throw new RuntimeException("Value for " + property + " cannot be null");
			}
			criteria.add(new Criterion(condition, value));
		}

		protected void addCriterion(String condition, Object value1, Object value2, String property) {
			if (value1 == null || value2 == null) {
				throw new RuntimeException("Between values for " + property + " cannot be null");
			}
			criteria.add(new Criterion(condition, value1, value2));
		}

		public Criteria andIdIsNull() {
			addCriterion("Id is null");
			return (Criteria) this;
		}

		public Criteria andIdIsNotNull() {
			addCriterion("Id is not null");
			return (Criteria) this;
		}

		public Criteria andIdEqualTo(String value) {
			addCriterion("Id =", value, "id");
			return (Criteria) this;
		}

		public Criteria andIdNotEqualTo(String value) {
			addCriterion("Id <>", value, "id");
			return (Criteria) this;
		}

		public Criteria andIdGreaterThan(String value) {
			addCriterion("Id >", value, "id");
			return (Criteria) this;
		}

		public Criteria andIdGreaterThanOrEqualTo(String value) {
			addCriterion("Id >=", value, "id");
			return (Criteria) this;
		}

		public Criteria andIdLessThan(String value) {
			addCriterion("Id <", value, "id");
			return (Criteria) this;
		}

		public Criteria andIdLessThanOrEqualTo(String value) {
			addCriterion("Id <=", value, "id");
			return (Criteria) this;
		}

		public Criteria andIdLike(String value) {
			addCriterion("Id like", value, "id");
			return (Criteria) this;
		}

		public Criteria andIdNotLike(String value) {
			addCriterion("Id not like", value, "id");
			return (Criteria) this;
		}

		public Criteria andIdIn(List<String> values) {
			addCriterion("Id in", values, "id");
			return (Criteria) this;
		}

		public Criteria andIdNotIn(List<String> values) {
			addCriterion("Id not in", values, "id");
			return (Criteria) this;
		}

		public Criteria andIdBetween(String value1, String value2) {
			addCriterion("Id between", value1, value2, "id");
			return (Criteria) this;
		}

		public Criteria andIdNotBetween(String value1, String value2) {
			addCriterion("Id not between", value1, value2, "id");
			return (Criteria) this;
		}

		public Criteria andFilenameIsNull() {
			addCriterion("Filename is null");
			return (Criteria) this;
		}

		public Criteria andFilenameIsNotNull() {
			addCriterion("Filename is not null");
			return (Criteria) this;
		}

		public Criteria andFilenameEqualTo(String value) {
			addCriterion("Filename =", value, "filename");
			return (Criteria) this;
		}

		public Criteria andFilenameNotEqualTo(String value) {
			addCriterion("Filename <>", value, "filename");
			return (Criteria) this;
		}

		public Criteria andFilenameGreaterThan(String value) {
			addCriterion("Filename >", value, "filename");
			return (Criteria) this;
		}

		public Criteria andFilenameGreaterThanOrEqualTo(String value) {
			addCriterion("Filename >=", value, "filename");
			return (Criteria) this;
		}

		public Criteria andFilenameLessThan(String value) {
			addCriterion("Filename <", value, "filename");
			return (Criteria) this;
		}

		public Criteria andFilenameLessThanOrEqualTo(String value) {
			addCriterion("Filename <=", value, "filename");
			return (Criteria) this;
		}

		public Criteria andFilenameLike(String value) {
			addCriterion("Filename like", value, "filename");
			return (Criteria) this;
		}

		public Criteria andFilenameNotLike(String value) {
			addCriterion("Filename not like", value, "filename");
			return (Criteria) this;
		}

		public Criteria andFilenameIn(List<String> values) {
			addCriterion("Filename in", values, "filename");
			return (Criteria) this;
		}

		public Criteria andFilenameNotIn(List<String> values) {
			addCriterion("Filename not in", values, "filename");
			return (Criteria) this;
		}

		public Criteria andFilenameBetween(String value1, String value2) {
			addCriterion("Filename between", value1, value2, "filename");
			return (Criteria) this;
		}

		public Criteria andFilenameNotBetween(String value1, String value2) {
			addCriterion("Filename not between", value1, value2, "filename");
			return (Criteria) this;
		}

		public Criteria andStartdatetimeIsNull() {
			addCriterion("StartDatetime is null");
			return (Criteria) this;
		}

		public Criteria andStartdatetimeIsNotNull() {
			addCriterion("StartDatetime is not null");
			return (Criteria) this;
		}

		public Criteria andStartdatetimeEqualTo(LocalDateTime value) {
			addCriterion("StartDatetime =", value, "startdatetime");
			return (Criteria) this;
		}

		public Criteria andStartdatetimeNotEqualTo(LocalDateTime value) {
			addCriterion("StartDatetime <>", value, "startdatetime");
			return (Criteria) this;
		}

		public Criteria andStartdatetimeGreaterThan(LocalDateTime value) {
			addCriterion("StartDatetime >", value, "startdatetime");
			return (Criteria) this;
		}

		public Criteria andStartdatetimeGreaterThanOrEqualTo(LocalDateTime value) {
			addCriterion("StartDatetime >=", value, "startdatetime");
			return (Criteria) this;
		}

		public Criteria andStartdatetimeLessThan(LocalDateTime value) {
			addCriterion("StartDatetime <", value, "startdatetime");
			return (Criteria) this;
		}

		public Criteria andStartdatetimeLessThanOrEqualTo(LocalDateTime value) {
			addCriterion("StartDatetime <=", value, "startdatetime");
			return (Criteria) this;
		}

		public Criteria andStartdatetimeIn(List<LocalDateTime> values) {
			addCriterion("StartDatetime in", values, "startdatetime");
			return (Criteria) this;
		}

		public Criteria andStartdatetimeNotIn(List<LocalDateTime> values) {
			addCriterion("StartDatetime not in", values, "startdatetime");
			return (Criteria) this;
		}

		public Criteria andStartdatetimeBetween(LocalDateTime value1, LocalDateTime value2) {
			addCriterion("StartDatetime between", value1, value2, "startdatetime");
			return (Criteria) this;
		}

		public Criteria andStartdatetimeNotBetween(LocalDateTime value1, LocalDateTime value2) {
			addCriterion("StartDatetime not between", value1, value2, "startdatetime");
			return (Criteria) this;
		}

		public Criteria andEnddatetimeIsNull() {
			addCriterion("EndDatetime is null");
			return (Criteria) this;
		}

		public Criteria andEnddatetimeIsNotNull() {
			addCriterion("EndDatetime is not null");
			return (Criteria) this;
		}

		public Criteria andEnddatetimeEqualTo(LocalDateTime value) {
			addCriterion("EndDatetime =", value, "enddatetime");
			return (Criteria) this;
		}

		public Criteria andEnddatetimeNotEqualTo(LocalDateTime value) {
			addCriterion("EndDatetime <>", value, "enddatetime");
			return (Criteria) this;
		}

		public Criteria andEnddatetimeGreaterThan(LocalDateTime value) {
			addCriterion("EndDatetime >", value, "enddatetime");
			return (Criteria) this;
		}

		public Criteria andEnddatetimeGreaterThanOrEqualTo(LocalDateTime value) {
			addCriterion("EndDatetime >=", value, "enddatetime");
			return (Criteria) this;
		}

		public Criteria andEnddatetimeLessThan(LocalDateTime value) {
			addCriterion("EndDatetime <", value, "enddatetime");
			return (Criteria) this;
		}

		public Criteria andEnddatetimeLessThanOrEqualTo(LocalDateTime value) {
			addCriterion("EndDatetime <=", value, "enddatetime");
			return (Criteria) this;
		}

		public Criteria andEnddatetimeIn(List<LocalDateTime> values) {
			addCriterion("EndDatetime in", values, "enddatetime");
			return (Criteria) this;
		}

		public Criteria andEnddatetimeNotIn(List<LocalDateTime> values) {
			addCriterion("EndDatetime not in", values, "enddatetime");
			return (Criteria) this;
		}

		public Criteria andEnddatetimeBetween(LocalDateTime value1, LocalDateTime value2) {
			addCriterion("EndDatetime between", value1, value2, "enddatetime");
			return (Criteria) this;
		}

		public Criteria andEnddatetimeNotBetween(LocalDateTime value1, LocalDateTime value2) {
			addCriterion("EndDatetime not between", value1, value2, "enddatetime");
			return (Criteria) this;
		}

		public Criteria andRecordcountIsNull() {
			addCriterion("RecordCount is null");
			return (Criteria) this;
		}

		public Criteria andRecordcountIsNotNull() {
			addCriterion("RecordCount is not null");
			return (Criteria) this;
		}

		public Criteria andRecordcountEqualTo(Integer value) {
			addCriterion("RecordCount =", value, "recordcount");
			return (Criteria) this;
		}

		public Criteria andRecordcountNotEqualTo(Integer value) {
			addCriterion("RecordCount <>", value, "recordcount");
			return (Criteria) this;
		}

		public Criteria andRecordcountGreaterThan(Integer value) {
			addCriterion("RecordCount >", value, "recordcount");
			return (Criteria) this;
		}

		public Criteria andRecordcountGreaterThanOrEqualTo(Integer value) {
			addCriterion("RecordCount >=", value, "recordcount");
			return (Criteria) this;
		}

		public Criteria andRecordcountLessThan(Integer value) {
			addCriterion("RecordCount <", value, "recordcount");
			return (Criteria) this;
		}

		public Criteria andRecordcountLessThanOrEqualTo(Integer value) {
			addCriterion("RecordCount <=", value, "recordcount");
			return (Criteria) this;
		}

		public Criteria andRecordcountIn(List<Integer> values) {
			addCriterion("RecordCount in", values, "recordcount");
			return (Criteria) this;
		}

		public Criteria andRecordcountNotIn(List<Integer> values) {
			addCriterion("RecordCount not in", values, "recordcount");
			return (Criteria) this;
		}

		public Criteria andRecordcountBetween(Integer value1, Integer value2) {
			addCriterion("RecordCount between", value1, value2, "recordcount");
			return (Criteria) this;
		}

		public Criteria andRecordcountNotBetween(Integer value1, Integer value2) {
			addCriterion("RecordCount not between", value1, value2, "recordcount");
			return (Criteria) this;
		}

		public Criteria andInsertcountIsNull() {
			addCriterion("InsertCount is null");
			return (Criteria) this;
		}

		public Criteria andInsertcountIsNotNull() {
			addCriterion("InsertCount is not null");
			return (Criteria) this;
		}

		public Criteria andInsertcountEqualTo(Integer value) {
			addCriterion("InsertCount =", value, "insertcount");
			return (Criteria) this;
		}

		public Criteria andInsertcountNotEqualTo(Integer value) {
			addCriterion("InsertCount <>", value, "insertcount");
			return (Criteria) this;
		}

		public Criteria andInsertcountGreaterThan(Integer value) {
			addCriterion("InsertCount >", value, "insertcount");
			return (Criteria) this;
		}

		public Criteria andInsertcountGreaterThanOrEqualTo(Integer value) {
			addCriterion("InsertCount >=", value, "insertcount");
			return (Criteria) this;
		}

		public Criteria andInsertcountLessThan(Integer value) {
			addCriterion("InsertCount <", value, "insertcount");
			return (Criteria) this;
		}

		public Criteria andInsertcountLessThanOrEqualTo(Integer value) {
			addCriterion("InsertCount <=", value, "insertcount");
			return (Criteria) this;
		}

		public Criteria andInsertcountIn(List<Integer> values) {
			addCriterion("InsertCount in", values, "insertcount");
			return (Criteria) this;
		}

		public Criteria andInsertcountNotIn(List<Integer> values) {
			addCriterion("InsertCount not in", values, "insertcount");
			return (Criteria) this;
		}

		public Criteria andInsertcountBetween(Integer value1, Integer value2) {
			addCriterion("InsertCount between", value1, value2, "insertcount");
			return (Criteria) this;
		}

		public Criteria andInsertcountNotBetween(Integer value1, Integer value2) {
			addCriterion("InsertCount not between", value1, value2, "insertcount");
			return (Criteria) this;
		}

		public Criteria andUpdatecountIsNull() {
			addCriterion("UpdateCount is null");
			return (Criteria) this;
		}

		public Criteria andUpdatecountIsNotNull() {
			addCriterion("UpdateCount is not null");
			return (Criteria) this;
		}

		public Criteria andUpdatecountEqualTo(Integer value) {
			addCriterion("UpdateCount =", value, "updatecount");
			return (Criteria) this;
		}

		public Criteria andUpdatecountNotEqualTo(Integer value) {
			addCriterion("UpdateCount <>", value, "updatecount");
			return (Criteria) this;
		}

		public Criteria andUpdatecountGreaterThan(Integer value) {
			addCriterion("UpdateCount >", value, "updatecount");
			return (Criteria) this;
		}

		public Criteria andUpdatecountGreaterThanOrEqualTo(Integer value) {
			addCriterion("UpdateCount >=", value, "updatecount");
			return (Criteria) this;
		}

		public Criteria andUpdatecountLessThan(Integer value) {
			addCriterion("UpdateCount <", value, "updatecount");
			return (Criteria) this;
		}

		public Criteria andUpdatecountLessThanOrEqualTo(Integer value) {
			addCriterion("UpdateCount <=", value, "updatecount");
			return (Criteria) this;
		}

		public Criteria andUpdatecountIn(List<Integer> values) {
			addCriterion("UpdateCount in", values, "updatecount");
			return (Criteria) this;
		}

		public Criteria andUpdatecountNotIn(List<Integer> values) {
			addCriterion("UpdateCount not in", values, "updatecount");
			return (Criteria) this;
		}

		public Criteria andUpdatecountBetween(Integer value1, Integer value2) {
			addCriterion("UpdateCount between", value1, value2, "updatecount");
			return (Criteria) this;
		}

		public Criteria andUpdatecountNotBetween(Integer value1, Integer value2) {
			addCriterion("UpdateCount not between", value1, value2, "updatecount");
			return (Criteria) this;
		}

		public Criteria andDeletecountIsNull() {
			addCriterion("DeleteCount is null");
			return (Criteria) this;
		}

		public Criteria andDeletecountIsNotNull() {
			addCriterion("DeleteCount is not null");
			return (Criteria) this;
		}

		public Criteria andDeletecountEqualTo(Integer value) {
			addCriterion("DeleteCount =", value, "deletecount");
			return (Criteria) this;
		}

		public Criteria andDeletecountNotEqualTo(Integer value) {
			addCriterion("DeleteCount <>", value, "deletecount");
			return (Criteria) this;
		}

		public Criteria andDeletecountGreaterThan(Integer value) {
			addCriterion("DeleteCount >", value, "deletecount");
			return (Criteria) this;
		}

		public Criteria andDeletecountGreaterThanOrEqualTo(Integer value) {
			addCriterion("DeleteCount >=", value, "deletecount");
			return (Criteria) this;
		}

		public Criteria andDeletecountLessThan(Integer value) {
			addCriterion("DeleteCount <", value, "deletecount");
			return (Criteria) this;
		}

		public Criteria andDeletecountLessThanOrEqualTo(Integer value) {
			addCriterion("DeleteCount <=", value, "deletecount");
			return (Criteria) this;
		}

		public Criteria andDeletecountIn(List<Integer> values) {
			addCriterion("DeleteCount in", values, "deletecount");
			return (Criteria) this;
		}

		public Criteria andDeletecountNotIn(List<Integer> values) {
			addCriterion("DeleteCount not in", values, "deletecount");
			return (Criteria) this;
		}

		public Criteria andDeletecountBetween(Integer value1, Integer value2) {
			addCriterion("DeleteCount between", value1, value2, "deletecount");
			return (Criteria) this;
		}

		public Criteria andDeletecountNotBetween(Integer value1, Integer value2) {
			addCriterion("DeleteCount not between", value1, value2, "deletecount");
			return (Criteria) this;
		}

		public Criteria andErrordatetimeIsNull() {
			addCriterion("ErrorDatetime is null");
			return (Criteria) this;
		}

		public Criteria andErrordatetimeIsNotNull() {
			addCriterion("ErrorDatetime is not null");
			return (Criteria) this;
		}

		public Criteria andErrordatetimeEqualTo(LocalDateTime value) {
			addCriterion("ErrorDatetime =", value, "errordatetime");
			return (Criteria) this;
		}

		public Criteria andErrordatetimeNotEqualTo(LocalDateTime value) {
			addCriterion("ErrorDatetime <>", value, "errordatetime");
			return (Criteria) this;
		}

		public Criteria andErrordatetimeGreaterThan(LocalDateTime value) {
			addCriterion("ErrorDatetime >", value, "errordatetime");
			return (Criteria) this;
		}

		public Criteria andErrordatetimeGreaterThanOrEqualTo(LocalDateTime value) {
			addCriterion("ErrorDatetime >=", value, "errordatetime");
			return (Criteria) this;
		}

		public Criteria andErrordatetimeLessThan(LocalDateTime value) {
			addCriterion("ErrorDatetime <", value, "errordatetime");
			return (Criteria) this;
		}

		public Criteria andErrordatetimeLessThanOrEqualTo(LocalDateTime value) {
			addCriterion("ErrorDatetime <=", value, "errordatetime");
			return (Criteria) this;
		}

		public Criteria andErrordatetimeIn(List<LocalDateTime> values) {
			addCriterion("ErrorDatetime in", values, "errordatetime");
			return (Criteria) this;
		}

		public Criteria andErrordatetimeNotIn(List<LocalDateTime> values) {
			addCriterion("ErrorDatetime not in", values, "errordatetime");
			return (Criteria) this;
		}

		public Criteria andErrordatetimeBetween(LocalDateTime value1, LocalDateTime value2) {
			addCriterion("ErrorDatetime between", value1, value2, "errordatetime");
			return (Criteria) this;
		}

		public Criteria andErrordatetimeNotBetween(LocalDateTime value1, LocalDateTime value2) {
			addCriterion("ErrorDatetime not between", value1, value2, "errordatetime");
			return (Criteria) this;
		}
	}

	/**
	 * This class was generated by MyBatis Generator. This class corresponds to the database table RawEpdWasteTransportation_SyncLog
	 * @mbg.generated  Wed Dec 14 15:33:44 HKT 2022
	 */
	public static class Criterion {
		private String condition;
		private Object value;
		private Object secondValue;
		private boolean noValue;
		private boolean singleValue;
		private boolean betweenValue;
		private boolean listValue;
		private String typeHandler;

		public String getCondition() {
			return condition;
		}

		public Object getValue() {
			return value;
		}

		public Object getSecondValue() {
			return secondValue;
		}

		public boolean isNoValue() {
			return noValue;
		}

		public boolean isSingleValue() {
			return singleValue;
		}

		public boolean isBetweenValue() {
			return betweenValue;
		}

		public boolean isListValue() {
			return listValue;
		}

		public String getTypeHandler() {
			return typeHandler;
		}

		protected Criterion(String condition) {
			super();
			this.condition = condition;
			this.typeHandler = null;
			this.noValue = true;
		}

		protected Criterion(String condition, Object value, String typeHandler) {
			super();
			this.condition = condition;
			this.value = value;
			this.typeHandler = typeHandler;
			if (value instanceof List<?>) {
				this.listValue = true;
			} else {
				this.singleValue = true;
			}
		}

		protected Criterion(String condition, Object value) {
			this(condition, value, null);
		}

		protected Criterion(String condition, Object value, Object secondValue, String typeHandler) {
			super();
			this.condition = condition;
			this.value = value;
			this.secondValue = secondValue;
			this.typeHandler = typeHandler;
			this.betweenValue = true;
		}

		protected Criterion(String condition, Object value, Object secondValue) {
			this(condition, value, secondValue, null);
		}
	}

	/**
     * This class was generated by MyBatis Generator.
     * This class corresponds to the database table RawEpdWasteTransportation_SyncLog
     *
     * @mbg.generated do_not_delete_during_merge Wed Dec 07 10:54:23 HKT 2022
     */
    public static class Criteria extends GeneratedCriteria {
        protected Criteria() {
            super();
        }
    }
}