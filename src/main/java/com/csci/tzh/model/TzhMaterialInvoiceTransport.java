package com.csci.tzh.model;

import java.math.BigDecimal;
import java.time.LocalDateTime;

public class TzhMaterialInvoiceTransport {

	/**
	 * This field was generated by MyBatis Generator. This field corresponds to the database column Tzh_MaterialInvoiceTransport.Id
	 * @mbg.generated  Wed Apr 19 11:16:27 HKT 2023
	 */
	private String id;
	/**
	 * This field was generated by MyBatis Generator. This field corresponds to the database column Tzh_MaterialInvoiceTransport.MaterialCode
	 * @mbg.generated  Wed Apr 19 11:16:27 HKT 2023
	 */
	private String materialcode;
	/**
	 * This field was generated by MyBatis Generator. This field corresponds to the database column Tzh_MaterialInvoiceTransport.SiteName
	 * @mbg.generated  Wed Apr 19 11:16:27 HKT 2023
	 */
	private String sitename;
	/**
	 * This field was generated by MyBatis Generator. This field corresponds to the database column Tzh_MaterialInvoiceTransport.ProtocolId
	 * @mbg.generated  Wed Apr 19 11:16:27 HKT 2023
	 */
	private String protocolid;
	/**
	 * This field was generated by MyBatis Generator. This field corresponds to the database column Tzh_MaterialInvoiceTransport.BillNo
	 * @mbg.generated  Wed Apr 19 11:16:27 HKT 2023
	 */
	private String billno;
	/**
	 * This field was generated by MyBatis Generator. This field corresponds to the database column Tzh_MaterialInvoiceTransport.TransportFactor
	 * @mbg.generated  Wed Apr 19 11:16:27 HKT 2023
	 */
	private BigDecimal transportfactor;
	/**
	 * This field was generated by MyBatis Generator. This field corresponds to the database column Tzh_MaterialInvoiceTransport.TransportFactorUnit
	 * @mbg.generated  Wed Apr 19 11:16:27 HKT 2023
	 */
	private String transportfactorunit;
	/**
	 * This field was generated by MyBatis Generator. This field corresponds to the database column Tzh_MaterialInvoiceTransport.TransportDistance
	 * @mbg.generated  Wed Apr 19 11:16:27 HKT 2023
	 */
	private BigDecimal transportdistance;
	/**
	 * This field was generated by MyBatis Generator. This field corresponds to the database column Tzh_MaterialInvoiceTransport.TransportDistanceUnit
	 * @mbg.generated  Wed Apr 19 11:16:27 HKT 2023
	 */
	private String transportdistanceunit;
	/**
	 * This field was generated by MyBatis Generator. This field corresponds to the database column Tzh_MaterialInvoiceTransport.CreatedTime
	 * @mbg.generated  Wed Apr 19 11:16:27 HKT 2023
	 */
	private LocalDateTime createdtime;
	/**
	 * This field was generated by MyBatis Generator. This field corresponds to the database column Tzh_MaterialInvoiceTransport.CreatedBy
	 * @mbg.generated  Wed Apr 19 11:16:27 HKT 2023
	 */
	private String createdby;
	/**
	 * This field was generated by MyBatis Generator. This field corresponds to the database column Tzh_MaterialInvoiceTransport.DeletedTime
	 * @mbg.generated  Wed Apr 19 11:16:27 HKT 2023
	 */
	private LocalDateTime deletedtime;
	/**
	 * This field was generated by MyBatis Generator. This field corresponds to the database column Tzh_MaterialInvoiceTransport.DeletedBy
	 * @mbg.generated  Wed Apr 19 11:16:27 HKT 2023
	 */
	private String deletedby;
	/**
	 * This field was generated by MyBatis Generator. This field corresponds to the database column Tzh_MaterialInvoiceTransport.IsDeleted
	 * @mbg.generated  Wed Apr 19 11:16:27 HKT 2023
	 */
	private Boolean isdeleted;

	/**
	 * This method was generated by MyBatis Generator. This method returns the value of the database column Tzh_MaterialInvoiceTransport.Id
	 * @return  the value of Tzh_MaterialInvoiceTransport.Id
	 * @mbg.generated  Wed Apr 19 11:16:27 HKT 2023
	 */
	public String getId() {
		return id;
	}

	/**
	 * This method was generated by MyBatis Generator. This method sets the value of the database column Tzh_MaterialInvoiceTransport.Id
	 * @param id  the value for Tzh_MaterialInvoiceTransport.Id
	 * @mbg.generated  Wed Apr 19 11:16:27 HKT 2023
	 */
	public void setId(String id) {
		this.id = id == null ? null : id.trim();
	}

	/**
	 * This method was generated by MyBatis Generator. This method returns the value of the database column Tzh_MaterialInvoiceTransport.MaterialCode
	 * @return  the value of Tzh_MaterialInvoiceTransport.MaterialCode
	 * @mbg.generated  Wed Apr 19 11:16:27 HKT 2023
	 */
	public String getMaterialcode() {
		return materialcode;
	}

	/**
	 * This method was generated by MyBatis Generator. This method sets the value of the database column Tzh_MaterialInvoiceTransport.MaterialCode
	 * @param materialcode  the value for Tzh_MaterialInvoiceTransport.MaterialCode
	 * @mbg.generated  Wed Apr 19 11:16:27 HKT 2023
	 */
	public void setMaterialcode(String materialcode) {
		this.materialcode = materialcode == null ? null : materialcode.trim();
	}

	/**
	 * This method was generated by MyBatis Generator. This method returns the value of the database column Tzh_MaterialInvoiceTransport.SiteName
	 * @return  the value of Tzh_MaterialInvoiceTransport.SiteName
	 * @mbg.generated  Wed Apr 19 11:16:27 HKT 2023
	 */
	public String getSitename() {
		return sitename;
	}

	/**
	 * This method was generated by MyBatis Generator. This method sets the value of the database column Tzh_MaterialInvoiceTransport.SiteName
	 * @param sitename  the value for Tzh_MaterialInvoiceTransport.SiteName
	 * @mbg.generated  Wed Apr 19 11:16:27 HKT 2023
	 */
	public void setSitename(String sitename) {
		this.sitename = sitename == null ? null : sitename.trim();
	}

	/**
	 * This method was generated by MyBatis Generator. This method returns the value of the database column Tzh_MaterialInvoiceTransport.ProtocolId
	 * @return  the value of Tzh_MaterialInvoiceTransport.ProtocolId
	 * @mbg.generated  Wed Apr 19 11:16:27 HKT 2023
	 */
	public String getProtocolid() {
		return protocolid;
	}

	/**
	 * This method was generated by MyBatis Generator. This method sets the value of the database column Tzh_MaterialInvoiceTransport.ProtocolId
	 * @param protocolid  the value for Tzh_MaterialInvoiceTransport.ProtocolId
	 * @mbg.generated  Wed Apr 19 11:16:27 HKT 2023
	 */
	public void setProtocolid(String protocolid) {
		this.protocolid = protocolid == null ? null : protocolid.trim();
	}

	/**
	 * This method was generated by MyBatis Generator. This method returns the value of the database column Tzh_MaterialInvoiceTransport.BillNo
	 * @return  the value of Tzh_MaterialInvoiceTransport.BillNo
	 * @mbg.generated  Wed Apr 19 11:16:27 HKT 2023
	 */
	public String getBillno() {
		return billno;
	}

	/**
	 * This method was generated by MyBatis Generator. This method sets the value of the database column Tzh_MaterialInvoiceTransport.BillNo
	 * @param billno  the value for Tzh_MaterialInvoiceTransport.BillNo
	 * @mbg.generated  Wed Apr 19 11:16:27 HKT 2023
	 */
	public void setBillno(String billno) {
		this.billno = billno == null ? null : billno.trim();
	}

	/**
	 * This method was generated by MyBatis Generator. This method returns the value of the database column Tzh_MaterialInvoiceTransport.TransportFactor
	 * @return  the value of Tzh_MaterialInvoiceTransport.TransportFactor
	 * @mbg.generated  Wed Apr 19 11:16:27 HKT 2023
	 */
	public BigDecimal getTransportfactor() {
		return transportfactor;
	}

	/**
	 * This method was generated by MyBatis Generator. This method sets the value of the database column Tzh_MaterialInvoiceTransport.TransportFactor
	 * @param transportfactor  the value for Tzh_MaterialInvoiceTransport.TransportFactor
	 * @mbg.generated  Wed Apr 19 11:16:27 HKT 2023
	 */
	public void setTransportfactor(BigDecimal transportfactor) {
		this.transportfactor = transportfactor;
	}

	/**
	 * This method was generated by MyBatis Generator. This method returns the value of the database column Tzh_MaterialInvoiceTransport.TransportFactorUnit
	 * @return  the value of Tzh_MaterialInvoiceTransport.TransportFactorUnit
	 * @mbg.generated  Wed Apr 19 11:16:27 HKT 2023
	 */
	public String getTransportfactorunit() {
		return transportfactorunit;
	}

	/**
	 * This method was generated by MyBatis Generator. This method sets the value of the database column Tzh_MaterialInvoiceTransport.TransportFactorUnit
	 * @param transportfactorunit  the value for Tzh_MaterialInvoiceTransport.TransportFactorUnit
	 * @mbg.generated  Wed Apr 19 11:16:27 HKT 2023
	 */
	public void setTransportfactorunit(String transportfactorunit) {
		this.transportfactorunit = transportfactorunit == null ? null : transportfactorunit.trim();
	}

	/**
	 * This method was generated by MyBatis Generator. This method returns the value of the database column Tzh_MaterialInvoiceTransport.TransportDistance
	 * @return  the value of Tzh_MaterialInvoiceTransport.TransportDistance
	 * @mbg.generated  Wed Apr 19 11:16:27 HKT 2023
	 */
	public BigDecimal getTransportdistance() {
		return transportdistance;
	}

	/**
	 * This method was generated by MyBatis Generator. This method sets the value of the database column Tzh_MaterialInvoiceTransport.TransportDistance
	 * @param transportdistance  the value for Tzh_MaterialInvoiceTransport.TransportDistance
	 * @mbg.generated  Wed Apr 19 11:16:27 HKT 2023
	 */
	public void setTransportdistance(BigDecimal transportdistance) {
		this.transportdistance = transportdistance;
	}

	/**
	 * This method was generated by MyBatis Generator. This method returns the value of the database column Tzh_MaterialInvoiceTransport.TransportDistanceUnit
	 * @return  the value of Tzh_MaterialInvoiceTransport.TransportDistanceUnit
	 * @mbg.generated  Wed Apr 19 11:16:27 HKT 2023
	 */
	public String getTransportdistanceunit() {
		return transportdistanceunit;
	}

	/**
	 * This method was generated by MyBatis Generator. This method sets the value of the database column Tzh_MaterialInvoiceTransport.TransportDistanceUnit
	 * @param transportdistanceunit  the value for Tzh_MaterialInvoiceTransport.TransportDistanceUnit
	 * @mbg.generated  Wed Apr 19 11:16:27 HKT 2023
	 */
	public void setTransportdistanceunit(String transportdistanceunit) {
		this.transportdistanceunit = transportdistanceunit == null ? null : transportdistanceunit.trim();
	}

	/**
	 * This method was generated by MyBatis Generator. This method returns the value of the database column Tzh_MaterialInvoiceTransport.CreatedTime
	 * @return  the value of Tzh_MaterialInvoiceTransport.CreatedTime
	 * @mbg.generated  Wed Apr 19 11:16:27 HKT 2023
	 */
	public LocalDateTime getCreatedtime() {
		return createdtime;
	}

	/**
	 * This method was generated by MyBatis Generator. This method sets the value of the database column Tzh_MaterialInvoiceTransport.CreatedTime
	 * @param createdtime  the value for Tzh_MaterialInvoiceTransport.CreatedTime
	 * @mbg.generated  Wed Apr 19 11:16:27 HKT 2023
	 */
	public void setCreatedtime(LocalDateTime createdtime) {
		this.createdtime = createdtime;
	}

	/**
	 * This method was generated by MyBatis Generator. This method returns the value of the database column Tzh_MaterialInvoiceTransport.CreatedBy
	 * @return  the value of Tzh_MaterialInvoiceTransport.CreatedBy
	 * @mbg.generated  Wed Apr 19 11:16:27 HKT 2023
	 */
	public String getCreatedby() {
		return createdby;
	}

	/**
	 * This method was generated by MyBatis Generator. This method sets the value of the database column Tzh_MaterialInvoiceTransport.CreatedBy
	 * @param createdby  the value for Tzh_MaterialInvoiceTransport.CreatedBy
	 * @mbg.generated  Wed Apr 19 11:16:27 HKT 2023
	 */
	public void setCreatedby(String createdby) {
		this.createdby = createdby == null ? null : createdby.trim();
	}

	/**
	 * This method was generated by MyBatis Generator. This method returns the value of the database column Tzh_MaterialInvoiceTransport.DeletedTime
	 * @return  the value of Tzh_MaterialInvoiceTransport.DeletedTime
	 * @mbg.generated  Wed Apr 19 11:16:27 HKT 2023
	 */
	public LocalDateTime getDeletedtime() {
		return deletedtime;
	}

	/**
	 * This method was generated by MyBatis Generator. This method sets the value of the database column Tzh_MaterialInvoiceTransport.DeletedTime
	 * @param deletedtime  the value for Tzh_MaterialInvoiceTransport.DeletedTime
	 * @mbg.generated  Wed Apr 19 11:16:27 HKT 2023
	 */
	public void setDeletedtime(LocalDateTime deletedtime) {
		this.deletedtime = deletedtime;
	}

	/**
	 * This method was generated by MyBatis Generator. This method returns the value of the database column Tzh_MaterialInvoiceTransport.DeletedBy
	 * @return  the value of Tzh_MaterialInvoiceTransport.DeletedBy
	 * @mbg.generated  Wed Apr 19 11:16:27 HKT 2023
	 */
	public String getDeletedby() {
		return deletedby;
	}

	/**
	 * This method was generated by MyBatis Generator. This method sets the value of the database column Tzh_MaterialInvoiceTransport.DeletedBy
	 * @param deletedby  the value for Tzh_MaterialInvoiceTransport.DeletedBy
	 * @mbg.generated  Wed Apr 19 11:16:27 HKT 2023
	 */
	public void setDeletedby(String deletedby) {
		this.deletedby = deletedby == null ? null : deletedby.trim();
	}

	/**
	 * This method was generated by MyBatis Generator. This method returns the value of the database column Tzh_MaterialInvoiceTransport.IsDeleted
	 * @return  the value of Tzh_MaterialInvoiceTransport.IsDeleted
	 * @mbg.generated  Wed Apr 19 11:16:27 HKT 2023
	 */
	public Boolean getIsdeleted() {
		return isdeleted;
	}

	/**
	 * This method was generated by MyBatis Generator. This method sets the value of the database column Tzh_MaterialInvoiceTransport.IsDeleted
	 * @param isdeleted  the value for Tzh_MaterialInvoiceTransport.IsDeleted
	 * @mbg.generated  Wed Apr 19 11:16:27 HKT 2023
	 */
	public void setIsdeleted(Boolean isdeleted) {
		this.isdeleted = isdeleted;
	}
}