package com.csci.tzh.model;

import java.util.ArrayList;
import java.util.List;

public class TzhBsSiteProtocolExample {
    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database table Tzh_Bs_SiteProtocol
     *
     * @mbg.generated Fri Apr 14 15:28:58 HKT 2023
     */
    protected String orderByClause;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database table Tzh_Bs_SiteProtocol
     *
     * @mbg.generated Fri Apr 14 15:28:58 HKT 2023
     */
    protected boolean distinct;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database table Tzh_Bs_SiteProtocol
     *
     * @mbg.generated Fri Apr 14 15:28:58 HKT 2023
     */
    protected List<Criteria> oredCriteria;

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table Tzh_Bs_SiteProtocol
     *
     * @mbg.generated Fri Apr 14 15:28:58 HKT 2023
     */
    public TzhBsSiteProtocolExample() {
        oredCriteria = new ArrayList<>();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table Tzh_Bs_SiteProtocol
     *
     * @mbg.generated Fri Apr 14 15:28:58 HKT 2023
     */
    public void setOrderByClause(String orderByClause) {
        this.orderByClause = orderByClause;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table Tzh_Bs_SiteProtocol
     *
     * @mbg.generated Fri Apr 14 15:28:58 HKT 2023
     */
    public String getOrderByClause() {
        return orderByClause;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table Tzh_Bs_SiteProtocol
     *
     * @mbg.generated Fri Apr 14 15:28:58 HKT 2023
     */
    public void setDistinct(boolean distinct) {
        this.distinct = distinct;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table Tzh_Bs_SiteProtocol
     *
     * @mbg.generated Fri Apr 14 15:28:58 HKT 2023
     */
    public boolean isDistinct() {
        return distinct;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table Tzh_Bs_SiteProtocol
     *
     * @mbg.generated Fri Apr 14 15:28:58 HKT 2023
     */
    public List<Criteria> getOredCriteria() {
        return oredCriteria;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table Tzh_Bs_SiteProtocol
     *
     * @mbg.generated Fri Apr 14 15:28:58 HKT 2023
     */
    public void or(Criteria criteria) {
        oredCriteria.add(criteria);
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table Tzh_Bs_SiteProtocol
     *
     * @mbg.generated Fri Apr 14 15:28:58 HKT 2023
     */
    public Criteria or() {
        Criteria criteria = createCriteriaInternal();
        oredCriteria.add(criteria);
        return criteria;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table Tzh_Bs_SiteProtocol
     *
     * @mbg.generated Fri Apr 14 15:28:58 HKT 2023
     */
    public Criteria createCriteria() {
        Criteria criteria = createCriteriaInternal();
        if (oredCriteria.size() == 0) {
            oredCriteria.add(criteria);
        }
        return criteria;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table Tzh_Bs_SiteProtocol
     *
     * @mbg.generated Fri Apr 14 15:28:58 HKT 2023
     */
    protected Criteria createCriteriaInternal() {
        Criteria criteria = new Criteria();
        return criteria;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table Tzh_Bs_SiteProtocol
     *
     * @mbg.generated Fri Apr 14 15:28:58 HKT 2023
     */
    public void clear() {
        oredCriteria.clear();
        orderByClause = null;
        distinct = false;
    }

    /**
     * This class was generated by MyBatis Generator.
     * This class corresponds to the database table Tzh_Bs_SiteProtocol
     *
     * @mbg.generated Fri Apr 14 15:28:58 HKT 2023
     */
    protected abstract static class GeneratedCriteria {
        protected List<Criterion> criteria;

        protected GeneratedCriteria() {
            super();
            criteria = new ArrayList<>();
        }

        public boolean isValid() {
            return criteria.size() > 0;
        }

        public List<Criterion> getAllCriteria() {
            return criteria;
        }

        public List<Criterion> getCriteria() {
            return criteria;
        }

        protected void addCriterion(String condition) {
            if (condition == null) {
                throw new RuntimeException("Value for condition cannot be null");
            }
            criteria.add(new Criterion(condition));
        }

        protected void addCriterion(String condition, Object value, String property) {
            if (value == null) {
                throw new RuntimeException("Value for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value));
        }

        protected void addCriterion(String condition, Object value1, Object value2, String property) {
            if (value1 == null || value2 == null) {
                throw new RuntimeException("Between values for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value1, value2));
        }

        public Criteria andSitenameIsNull() {
            addCriterion("SiteName is null");
            return (Criteria) this;
        }

        public Criteria andSitenameIsNotNull() {
            addCriterion("SiteName is not null");
            return (Criteria) this;
        }

        public Criteria andSitenameEqualTo(String value) {
            addCriterion("SiteName =", value, "sitename");
            return (Criteria) this;
        }

        public Criteria andSitenameNotEqualTo(String value) {
            addCriterion("SiteName <>", value, "sitename");
            return (Criteria) this;
        }

        public Criteria andSitenameGreaterThan(String value) {
            addCriterion("SiteName >", value, "sitename");
            return (Criteria) this;
        }

        public Criteria andSitenameGreaterThanOrEqualTo(String value) {
            addCriterion("SiteName >=", value, "sitename");
            return (Criteria) this;
        }

        public Criteria andSitenameLessThan(String value) {
            addCriterion("SiteName <", value, "sitename");
            return (Criteria) this;
        }

        public Criteria andSitenameLessThanOrEqualTo(String value) {
            addCriterion("SiteName <=", value, "sitename");
            return (Criteria) this;
        }

        public Criteria andSitenameLike(String value) {
            addCriterion("SiteName like", value, "sitename");
            return (Criteria) this;
        }

        public Criteria andSitenameNotLike(String value) {
            addCriterion("SiteName not like", value, "sitename");
            return (Criteria) this;
        }

        public Criteria andSitenameIn(List<String> values) {
            addCriterion("SiteName in", values, "sitename");
            return (Criteria) this;
        }

        public Criteria andSitenameNotIn(List<String> values) {
            addCriterion("SiteName not in", values, "sitename");
            return (Criteria) this;
        }

        public Criteria andSitenameBetween(String value1, String value2) {
            addCriterion("SiteName between", value1, value2, "sitename");
            return (Criteria) this;
        }

        public Criteria andSitenameNotBetween(String value1, String value2) {
            addCriterion("SiteName not between", value1, value2, "sitename");
            return (Criteria) this;
        }

        public Criteria andProtocolidIsNull() {
            addCriterion("ProtocolId is null");
            return (Criteria) this;
        }

        public Criteria andProtocolidIsNotNull() {
            addCriterion("ProtocolId is not null");
            return (Criteria) this;
        }

        public Criteria andProtocolidEqualTo(String value) {
            addCriterion("ProtocolId =", value, "protocolid");
            return (Criteria) this;
        }

        public Criteria andProtocolidNotEqualTo(String value) {
            addCriterion("ProtocolId <>", value, "protocolid");
            return (Criteria) this;
        }

        public Criteria andProtocolidGreaterThan(String value) {
            addCriterion("ProtocolId >", value, "protocolid");
            return (Criteria) this;
        }

        public Criteria andProtocolidGreaterThanOrEqualTo(String value) {
            addCriterion("ProtocolId >=", value, "protocolid");
            return (Criteria) this;
        }

        public Criteria andProtocolidLessThan(String value) {
            addCriterion("ProtocolId <", value, "protocolid");
            return (Criteria) this;
        }

        public Criteria andProtocolidLessThanOrEqualTo(String value) {
            addCriterion("ProtocolId <=", value, "protocolid");
            return (Criteria) this;
        }

        public Criteria andProtocolidLike(String value) {
            addCriterion("ProtocolId like", value, "protocolid");
            return (Criteria) this;
        }

        public Criteria andProtocolidNotLike(String value) {
            addCriterion("ProtocolId not like", value, "protocolid");
            return (Criteria) this;
        }

        public Criteria andProtocolidIn(List<String> values) {
            addCriterion("ProtocolId in", values, "protocolid");
            return (Criteria) this;
        }

        public Criteria andProtocolidNotIn(List<String> values) {
            addCriterion("ProtocolId not in", values, "protocolid");
            return (Criteria) this;
        }

        public Criteria andProtocolidBetween(String value1, String value2) {
            addCriterion("ProtocolId between", value1, value2, "protocolid");
            return (Criteria) this;
        }

        public Criteria andProtocolidNotBetween(String value1, String value2) {
            addCriterion("ProtocolId not between", value1, value2, "protocolid");
            return (Criteria) this;
        }
    }

    /**
     * This class was generated by MyBatis Generator.
     * This class corresponds to the database table Tzh_Bs_SiteProtocol
     *
     * @mbg.generated do_not_delete_during_merge Fri Apr 14 15:28:58 HKT 2023
     */
    public static class Criteria extends GeneratedCriteria {
        protected Criteria() {
            super();
        }
    }

    /**
     * This class was generated by MyBatis Generator.
     * This class corresponds to the database table Tzh_Bs_SiteProtocol
     *
     * @mbg.generated Fri Apr 14 15:28:58 HKT 2023
     */
    public static class Criterion {
        private String condition;

        private Object value;

        private Object secondValue;

        private boolean noValue;

        private boolean singleValue;

        private boolean betweenValue;

        private boolean listValue;

        private String typeHandler;

        public String getCondition() {
            return condition;
        }

        public Object getValue() {
            return value;
        }

        public Object getSecondValue() {
            return secondValue;
        }

        public boolean isNoValue() {
            return noValue;
        }

        public boolean isSingleValue() {
            return singleValue;
        }

        public boolean isBetweenValue() {
            return betweenValue;
        }

        public boolean isListValue() {
            return listValue;
        }

        public String getTypeHandler() {
            return typeHandler;
        }

        protected Criterion(String condition) {
            super();
            this.condition = condition;
            this.typeHandler = null;
            this.noValue = true;
        }

        protected Criterion(String condition, Object value, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.typeHandler = typeHandler;
            if (value instanceof List<?>) {
                this.listValue = true;
            } else {
                this.singleValue = true;
            }
        }

        protected Criterion(String condition, Object value) {
            this(condition, value, null);
        }

        protected Criterion(String condition, Object value, Object secondValue, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.secondValue = secondValue;
            this.typeHandler = typeHandler;
            this.betweenValue = true;
        }

        protected Criterion(String condition, Object value, Object secondValue) {
            this(condition, value, secondValue, null);
        }
    }
}