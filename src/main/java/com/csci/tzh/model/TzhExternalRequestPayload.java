package com.csci.tzh.model;

import java.time.LocalDateTime;

public class TzhExternalRequestPayload {
    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column Tzh_External_RequestPayload.Id
     *
     * @mbg.generated Fri Feb 17 16:44:10 HKT 2023
     */
    private String id;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column Tzh_External_RequestPayload.Url
     *
     * @mbg.generated Fri Feb 17 16:44:10 HKT 2023
     */
    private String url;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column Tzh_External_RequestPayload.Payload
     *
     * @mbg.generated Fri Feb 17 16:44:10 HKT 2023
     */
    private String payload;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column Tzh_External_RequestPayload.CreatedBy
     *
     * @mbg.generated Fri Feb 17 16:44:10 HKT 2023
     */
    private String createdby;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column Tzh_External_RequestPayload.CreatedTime
     *
     * @mbg.generated Fri Feb 17 16:44:10 HKT 2023
     */
    private LocalDateTime createdtime;

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column Tzh_External_RequestPayload.Id
     *
     * @return the value of Tzh_External_RequestPayload.Id
     *
     * @mbg.generated Fri Feb 17 16:44:10 HKT 2023
     */
    public String getId() {
        return id;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column Tzh_External_RequestPayload.Id
     *
     * @param id the value for Tzh_External_RequestPayload.Id
     *
     * @mbg.generated Fri Feb 17 16:44:10 HKT 2023
     */
    public void setId(String id) {
        this.id = id == null ? null : id.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column Tzh_External_RequestPayload.Url
     *
     * @return the value of Tzh_External_RequestPayload.Url
     *
     * @mbg.generated Fri Feb 17 16:44:10 HKT 2023
     */
    public String getUrl() {
        return url;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column Tzh_External_RequestPayload.Url
     *
     * @param url the value for Tzh_External_RequestPayload.Url
     *
     * @mbg.generated Fri Feb 17 16:44:10 HKT 2023
     */
    public void setUrl(String url) {
        this.url = url == null ? null : url.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column Tzh_External_RequestPayload.Payload
     *
     * @return the value of Tzh_External_RequestPayload.Payload
     *
     * @mbg.generated Fri Feb 17 16:44:10 HKT 2023
     */
    public String getPayload() {
        return payload;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column Tzh_External_RequestPayload.Payload
     *
     * @param payload the value for Tzh_External_RequestPayload.Payload
     *
     * @mbg.generated Fri Feb 17 16:44:10 HKT 2023
     */
    public void setPayload(String payload) {
        this.payload = payload == null ? null : payload.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column Tzh_External_RequestPayload.CreatedBy
     *
     * @return the value of Tzh_External_RequestPayload.CreatedBy
     *
     * @mbg.generated Fri Feb 17 16:44:10 HKT 2023
     */
    public String getCreatedby() {
        return createdby;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column Tzh_External_RequestPayload.CreatedBy
     *
     * @param createdby the value for Tzh_External_RequestPayload.CreatedBy
     *
     * @mbg.generated Fri Feb 17 16:44:10 HKT 2023
     */
    public void setCreatedby(String createdby) {
        this.createdby = createdby == null ? null : createdby.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column Tzh_External_RequestPayload.CreatedTime
     *
     * @return the value of Tzh_External_RequestPayload.CreatedTime
     *
     * @mbg.generated Fri Feb 17 16:44:10 HKT 2023
     */
    public LocalDateTime getCreatedtime() {
        return createdtime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column Tzh_External_RequestPayload.CreatedTime
     *
     * @param createdtime the value for Tzh_External_RequestPayload.CreatedTime
     *
     * @mbg.generated Fri Feb 17 16:44:10 HKT 2023
     */
    public void setCreatedtime(LocalDateTime createdtime) {
        this.createdtime = createdtime;
    }
}