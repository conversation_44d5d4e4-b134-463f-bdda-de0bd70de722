package com.csci.tzh.model;

import java.util.ArrayList;
import java.util.List;

public class DScopeExample {
    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database table D_Scope
     *
     * @mbg.generated Mon Jan 09 11:17:37 HKT 2023
     */
    protected String orderByClause;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database table D_Scope
     *
     * @mbg.generated Mon Jan 09 11:17:37 HKT 2023
     */
    protected boolean distinct;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database table D_Scope
     *
     * @mbg.generated Mon Jan 09 11:17:37 HKT 2023
     */
    protected List<Criteria> oredCriteria;

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table D_Scope
     *
     * @mbg.generated Mon Jan 09 11:17:37 HKT 2023
     */
    public DScopeExample() {
        oredCriteria = new ArrayList<>();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table D_Scope
     *
     * @mbg.generated Mon Jan 09 11:17:37 HKT 2023
     */
    public void setOrderByClause(String orderByClause) {
        this.orderByClause = orderByClause;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table D_Scope
     *
     * @mbg.generated Mon Jan 09 11:17:37 HKT 2023
     */
    public String getOrderByClause() {
        return orderByClause;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table D_Scope
     *
     * @mbg.generated Mon Jan 09 11:17:37 HKT 2023
     */
    public void setDistinct(boolean distinct) {
        this.distinct = distinct;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table D_Scope
     *
     * @mbg.generated Mon Jan 09 11:17:37 HKT 2023
     */
    public boolean isDistinct() {
        return distinct;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table D_Scope
     *
     * @mbg.generated Mon Jan 09 11:17:37 HKT 2023
     */
    public List<Criteria> getOredCriteria() {
        return oredCriteria;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table D_Scope
     *
     * @mbg.generated Mon Jan 09 11:17:37 HKT 2023
     */
    public void or(Criteria criteria) {
        oredCriteria.add(criteria);
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table D_Scope
     *
     * @mbg.generated Mon Jan 09 11:17:37 HKT 2023
     */
    public Criteria or() {
        Criteria criteria = createCriteriaInternal();
        oredCriteria.add(criteria);
        return criteria;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table D_Scope
     *
     * @mbg.generated Mon Jan 09 11:17:37 HKT 2023
     */
    public Criteria createCriteria() {
        Criteria criteria = createCriteriaInternal();
        if (oredCriteria.size() == 0) {
            oredCriteria.add(criteria);
        }
        return criteria;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table D_Scope
     *
     * @mbg.generated Mon Jan 09 11:17:37 HKT 2023
     */
    protected Criteria createCriteriaInternal() {
        Criteria criteria = new Criteria();
        return criteria;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table D_Scope
     *
     * @mbg.generated Mon Jan 09 11:17:37 HKT 2023
     */
    public void clear() {
        oredCriteria.clear();
        orderByClause = null;
        distinct = false;
    }

    /**
     * This class was generated by MyBatis Generator.
     * This class corresponds to the database table D_Scope
     *
     * @mbg.generated Mon Jan 09 11:17:37 HKT 2023
     */
    protected abstract static class GeneratedCriteria {
        protected List<Criterion> criteria;

        protected GeneratedCriteria() {
            super();
            criteria = new ArrayList<>();
        }

        public boolean isValid() {
            return criteria.size() > 0;
        }

        public List<Criterion> getAllCriteria() {
            return criteria;
        }

        public List<Criterion> getCriteria() {
            return criteria;
        }

        protected void addCriterion(String condition) {
            if (condition == null) {
                throw new RuntimeException("Value for condition cannot be null");
            }
            criteria.add(new Criterion(condition));
        }

        protected void addCriterion(String condition, Object value, String property) {
            if (value == null) {
                throw new RuntimeException("Value for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value));
        }

        protected void addCriterion(String condition, Object value1, Object value2, String property) {
            if (value1 == null || value2 == null) {
                throw new RuntimeException("Between values for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value1, value2));
        }

        public Criteria andProtocolIsNull() {
            addCriterion("Protocol is null");
            return (Criteria) this;
        }

        public Criteria andProtocolIsNotNull() {
            addCriterion("Protocol is not null");
            return (Criteria) this;
        }

        public Criteria andProtocolEqualTo(String value) {
            addCriterion("Protocol =", value, "protocol");
            return (Criteria) this;
        }

        public Criteria andProtocolNotEqualTo(String value) {
            addCriterion("Protocol <>", value, "protocol");
            return (Criteria) this;
        }

        public Criteria andProtocolGreaterThan(String value) {
            addCriterion("Protocol >", value, "protocol");
            return (Criteria) this;
        }

        public Criteria andProtocolGreaterThanOrEqualTo(String value) {
            addCriterion("Protocol >=", value, "protocol");
            return (Criteria) this;
        }

        public Criteria andProtocolLessThan(String value) {
            addCriterion("Protocol <", value, "protocol");
            return (Criteria) this;
        }

        public Criteria andProtocolLessThanOrEqualTo(String value) {
            addCriterion("Protocol <=", value, "protocol");
            return (Criteria) this;
        }

        public Criteria andProtocolLike(String value) {
            addCriterion("Protocol like", value, "protocol");
            return (Criteria) this;
        }

        public Criteria andProtocolNotLike(String value) {
            addCriterion("Protocol not like", value, "protocol");
            return (Criteria) this;
        }

        public Criteria andProtocolIn(List<String> values) {
            addCriterion("Protocol in", values, "protocol");
            return (Criteria) this;
        }

        public Criteria andProtocolNotIn(List<String> values) {
            addCriterion("Protocol not in", values, "protocol");
            return (Criteria) this;
        }

        public Criteria andProtocolBetween(String value1, String value2) {
            addCriterion("Protocol between", value1, value2, "protocol");
            return (Criteria) this;
        }

        public Criteria andProtocolNotBetween(String value1, String value2) {
            addCriterion("Protocol not between", value1, value2, "protocol");
            return (Criteria) this;
        }

        public Criteria andCarbonemissionlocationIsNull() {
            addCriterion("CarbonEmissionLocation is null");
            return (Criteria) this;
        }

        public Criteria andCarbonemissionlocationIsNotNull() {
            addCriterion("CarbonEmissionLocation is not null");
            return (Criteria) this;
        }

        public Criteria andCarbonemissionlocationEqualTo(String value) {
            addCriterion("CarbonEmissionLocation =", value, "carbonemissionlocation");
            return (Criteria) this;
        }

        public Criteria andCarbonemissionlocationNotEqualTo(String value) {
            addCriterion("CarbonEmissionLocation <>", value, "carbonemissionlocation");
            return (Criteria) this;
        }

        public Criteria andCarbonemissionlocationGreaterThan(String value) {
            addCriterion("CarbonEmissionLocation >", value, "carbonemissionlocation");
            return (Criteria) this;
        }

        public Criteria andCarbonemissionlocationGreaterThanOrEqualTo(String value) {
            addCriterion("CarbonEmissionLocation >=", value, "carbonemissionlocation");
            return (Criteria) this;
        }

        public Criteria andCarbonemissionlocationLessThan(String value) {
            addCriterion("CarbonEmissionLocation <", value, "carbonemissionlocation");
            return (Criteria) this;
        }

        public Criteria andCarbonemissionlocationLessThanOrEqualTo(String value) {
            addCriterion("CarbonEmissionLocation <=", value, "carbonemissionlocation");
            return (Criteria) this;
        }

        public Criteria andCarbonemissionlocationLike(String value) {
            addCriterion("CarbonEmissionLocation like", value, "carbonemissionlocation");
            return (Criteria) this;
        }

        public Criteria andCarbonemissionlocationNotLike(String value) {
            addCriterion("CarbonEmissionLocation not like", value, "carbonemissionlocation");
            return (Criteria) this;
        }

        public Criteria andCarbonemissionlocationIn(List<String> values) {
            addCriterion("CarbonEmissionLocation in", values, "carbonemissionlocation");
            return (Criteria) this;
        }

        public Criteria andCarbonemissionlocationNotIn(List<String> values) {
            addCriterion("CarbonEmissionLocation not in", values, "carbonemissionlocation");
            return (Criteria) this;
        }

        public Criteria andCarbonemissionlocationBetween(String value1, String value2) {
            addCriterion("CarbonEmissionLocation between", value1, value2, "carbonemissionlocation");
            return (Criteria) this;
        }

        public Criteria andCarbonemissionlocationNotBetween(String value1, String value2) {
            addCriterion("CarbonEmissionLocation not between", value1, value2, "carbonemissionlocation");
            return (Criteria) this;
        }

        public Criteria andScopemainIsNull() {
            addCriterion("ScopeMain is null");
            return (Criteria) this;
        }

        public Criteria andScopemainIsNotNull() {
            addCriterion("ScopeMain is not null");
            return (Criteria) this;
        }

        public Criteria andScopemainEqualTo(String value) {
            addCriterion("ScopeMain =", value, "scopemain");
            return (Criteria) this;
        }

        public Criteria andScopemainNotEqualTo(String value) {
            addCriterion("ScopeMain <>", value, "scopemain");
            return (Criteria) this;
        }

        public Criteria andScopemainGreaterThan(String value) {
            addCriterion("ScopeMain >", value, "scopemain");
            return (Criteria) this;
        }

        public Criteria andScopemainGreaterThanOrEqualTo(String value) {
            addCriterion("ScopeMain >=", value, "scopemain");
            return (Criteria) this;
        }

        public Criteria andScopemainLessThan(String value) {
            addCriterion("ScopeMain <", value, "scopemain");
            return (Criteria) this;
        }

        public Criteria andScopemainLessThanOrEqualTo(String value) {
            addCriterion("ScopeMain <=", value, "scopemain");
            return (Criteria) this;
        }

        public Criteria andScopemainLike(String value) {
            addCriterion("ScopeMain like", value, "scopemain");
            return (Criteria) this;
        }

        public Criteria andScopemainNotLike(String value) {
            addCriterion("ScopeMain not like", value, "scopemain");
            return (Criteria) this;
        }

        public Criteria andScopemainIn(List<String> values) {
            addCriterion("ScopeMain in", values, "scopemain");
            return (Criteria) this;
        }

        public Criteria andScopemainNotIn(List<String> values) {
            addCriterion("ScopeMain not in", values, "scopemain");
            return (Criteria) this;
        }

        public Criteria andScopemainBetween(String value1, String value2) {
            addCriterion("ScopeMain between", value1, value2, "scopemain");
            return (Criteria) this;
        }

        public Criteria andScopemainNotBetween(String value1, String value2) {
            addCriterion("ScopeMain not between", value1, value2, "scopemain");
            return (Criteria) this;
        }

        public Criteria andScopedetailIsNull() {
            addCriterion("ScopeDetail is null");
            return (Criteria) this;
        }

        public Criteria andScopedetailIsNotNull() {
            addCriterion("ScopeDetail is not null");
            return (Criteria) this;
        }

        public Criteria andScopedetailEqualTo(String value) {
            addCriterion("ScopeDetail =", value, "scopedetail");
            return (Criteria) this;
        }

        public Criteria andScopedetailNotEqualTo(String value) {
            addCriterion("ScopeDetail <>", value, "scopedetail");
            return (Criteria) this;
        }

        public Criteria andScopedetailGreaterThan(String value) {
            addCriterion("ScopeDetail >", value, "scopedetail");
            return (Criteria) this;
        }

        public Criteria andScopedetailGreaterThanOrEqualTo(String value) {
            addCriterion("ScopeDetail >=", value, "scopedetail");
            return (Criteria) this;
        }

        public Criteria andScopedetailLessThan(String value) {
            addCriterion("ScopeDetail <", value, "scopedetail");
            return (Criteria) this;
        }

        public Criteria andScopedetailLessThanOrEqualTo(String value) {
            addCriterion("ScopeDetail <=", value, "scopedetail");
            return (Criteria) this;
        }

        public Criteria andScopedetailLike(String value) {
            addCriterion("ScopeDetail like", value, "scopedetail");
            return (Criteria) this;
        }

        public Criteria andScopedetailNotLike(String value) {
            addCriterion("ScopeDetail not like", value, "scopedetail");
            return (Criteria) this;
        }

        public Criteria andScopedetailIn(List<String> values) {
            addCriterion("ScopeDetail in", values, "scopedetail");
            return (Criteria) this;
        }

        public Criteria andScopedetailNotIn(List<String> values) {
            addCriterion("ScopeDetail not in", values, "scopedetail");
            return (Criteria) this;
        }

        public Criteria andScopedetailBetween(String value1, String value2) {
            addCriterion("ScopeDetail between", value1, value2, "scopedetail");
            return (Criteria) this;
        }

        public Criteria andScopedetailNotBetween(String value1, String value2) {
            addCriterion("ScopeDetail not between", value1, value2, "scopedetail");
            return (Criteria) this;
        }
    }

    /**
     * This class was generated by MyBatis Generator.
     * This class corresponds to the database table D_Scope
     *
     * @mbg.generated do_not_delete_during_merge Mon Jan 09 11:17:37 HKT 2023
     */
    public static class Criteria extends GeneratedCriteria {
        protected Criteria() {
            super();
        }
    }

    /**
     * This class was generated by MyBatis Generator.
     * This class corresponds to the database table D_Scope
     *
     * @mbg.generated Mon Jan 09 11:17:37 HKT 2023
     */
    public static class Criterion {
        private String condition;

        private Object value;

        private Object secondValue;

        private boolean noValue;

        private boolean singleValue;

        private boolean betweenValue;

        private boolean listValue;

        private String typeHandler;

        public String getCondition() {
            return condition;
        }

        public Object getValue() {
            return value;
        }

        public Object getSecondValue() {
            return secondValue;
        }

        public boolean isNoValue() {
            return noValue;
        }

        public boolean isSingleValue() {
            return singleValue;
        }

        public boolean isBetweenValue() {
            return betweenValue;
        }

        public boolean isListValue() {
            return listValue;
        }

        public String getTypeHandler() {
            return typeHandler;
        }

        protected Criterion(String condition) {
            super();
            this.condition = condition;
            this.typeHandler = null;
            this.noValue = true;
        }

        protected Criterion(String condition, Object value, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.typeHandler = typeHandler;
            if (value instanceof List<?>) {
                this.listValue = true;
            } else {
                this.singleValue = true;
            }
        }

        protected Criterion(String condition, Object value) {
            this(condition, value, null);
        }

        protected Criterion(String condition, Object value, Object secondValue, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.secondValue = secondValue;
            this.typeHandler = typeHandler;
            this.betweenValue = true;
        }

        protected Criterion(String condition, Object value, Object secondValue) {
            this(condition, value, secondValue, null);
        }
    }
}