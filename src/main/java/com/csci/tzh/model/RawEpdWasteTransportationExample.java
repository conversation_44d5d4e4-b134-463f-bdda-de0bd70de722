package com.csci.tzh.model;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;

public class RawEpdWasteTransportationExample {
    /**
	 * This field was generated by MyBatis Generator. This field corresponds to the database table RawEpdWasteTransportation
	 * @mbg.generated  Wed Dec 14 17:16:15 HKT 2022
	 */
	protected String orderByClause;
	/**
	 * This field was generated by MyBatis Generator. This field corresponds to the database table RawEpdWasteTransportation
	 * @mbg.generated  Wed Dec 14 17:16:15 HKT 2022
	 */
	protected boolean distinct;
	/**
	 * This field was generated by MyBatis Generator. This field corresponds to the database table RawEpdWasteTransportation
	 * @mbg.generated  Wed Dec 14 17:16:15 HKT 2022
	 */
	protected List<Criteria> oredCriteria;

	/**
	 * This method was generated by MyBatis Generator. This method corresponds to the database table RawEpdWasteTransportation
	 * @mbg.generated  Wed Dec 14 17:16:15 HKT 2022
	 */
	public RawEpdWasteTransportationExample() {
		oredCriteria = new ArrayList<>();
	}

	/**
	 * This method was generated by MyBatis Generator. This method corresponds to the database table RawEpdWasteTransportation
	 * @mbg.generated  Wed Dec 14 17:16:15 HKT 2022
	 */
	public void setOrderByClause(String orderByClause) {
		this.orderByClause = orderByClause;
	}

	/**
	 * This method was generated by MyBatis Generator. This method corresponds to the database table RawEpdWasteTransportation
	 * @mbg.generated  Wed Dec 14 17:16:15 HKT 2022
	 */
	public String getOrderByClause() {
		return orderByClause;
	}

	/**
	 * This method was generated by MyBatis Generator. This method corresponds to the database table RawEpdWasteTransportation
	 * @mbg.generated  Wed Dec 14 17:16:15 HKT 2022
	 */
	public void setDistinct(boolean distinct) {
		this.distinct = distinct;
	}

	/**
	 * This method was generated by MyBatis Generator. This method corresponds to the database table RawEpdWasteTransportation
	 * @mbg.generated  Wed Dec 14 17:16:15 HKT 2022
	 */
	public boolean isDistinct() {
		return distinct;
	}

	/**
	 * This method was generated by MyBatis Generator. This method corresponds to the database table RawEpdWasteTransportation
	 * @mbg.generated  Wed Dec 14 17:16:15 HKT 2022
	 */
	public List<Criteria> getOredCriteria() {
		return oredCriteria;
	}

	/**
	 * This method was generated by MyBatis Generator. This method corresponds to the database table RawEpdWasteTransportation
	 * @mbg.generated  Wed Dec 14 17:16:15 HKT 2022
	 */
	public void or(Criteria criteria) {
		oredCriteria.add(criteria);
	}

	/**
	 * This method was generated by MyBatis Generator. This method corresponds to the database table RawEpdWasteTransportation
	 * @mbg.generated  Wed Dec 14 17:16:15 HKT 2022
	 */
	public Criteria or() {
		Criteria criteria = createCriteriaInternal();
		oredCriteria.add(criteria);
		return criteria;
	}

	/**
	 * This method was generated by MyBatis Generator. This method corresponds to the database table RawEpdWasteTransportation
	 * @mbg.generated  Wed Dec 14 17:16:15 HKT 2022
	 */
	public Criteria createCriteria() {
		Criteria criteria = createCriteriaInternal();
		if (oredCriteria.size() == 0) {
			oredCriteria.add(criteria);
		}
		return criteria;
	}

	/**
	 * This method was generated by MyBatis Generator. This method corresponds to the database table RawEpdWasteTransportation
	 * @mbg.generated  Wed Dec 14 17:16:15 HKT 2022
	 */
	protected Criteria createCriteriaInternal() {
		Criteria criteria = new Criteria();
		return criteria;
	}

	/**
	 * This method was generated by MyBatis Generator. This method corresponds to the database table RawEpdWasteTransportation
	 * @mbg.generated  Wed Dec 14 17:16:15 HKT 2022
	 */
	public void clear() {
		oredCriteria.clear();
		orderByClause = null;
		distinct = false;
	}

	/**
	 * This class was generated by MyBatis Generator. This class corresponds to the database table RawEpdWasteTransportation
	 * @mbg.generated  Wed Dec 14 17:16:15 HKT 2022
	 */
	protected abstract static class GeneratedCriteria {
		protected List<Criterion> criteria;

		protected GeneratedCriteria() {
			super();
			criteria = new ArrayList<>();
		}

		public boolean isValid() {
			return criteria.size() > 0;
		}

		public List<Criterion> getAllCriteria() {
			return criteria;
		}

		public List<Criterion> getCriteria() {
			return criteria;
		}

		protected void addCriterion(String condition) {
			if (condition == null) {
				throw new RuntimeException("Value for condition cannot be null");
			}
			criteria.add(new Criterion(condition));
		}

		protected void addCriterion(String condition, Object value, String property) {
			if (value == null) {
				throw new RuntimeException("Value for " + property + " cannot be null");
			}
			criteria.add(new Criterion(condition, value));
		}

		protected void addCriterion(String condition, Object value1, Object value2, String property) {
			if (value1 == null || value2 == null) {
				throw new RuntimeException("Between values for " + property + " cannot be null");
			}
			criteria.add(new Criterion(condition, value1, value2));
		}

		public Criteria andIdIsNull() {
			addCriterion("Id is null");
			return (Criteria) this;
		}

		public Criteria andIdIsNotNull() {
			addCriterion("Id is not null");
			return (Criteria) this;
		}

		public Criteria andIdEqualTo(String value) {
			addCriterion("Id =", value, "id");
			return (Criteria) this;
		}

		public Criteria andIdNotEqualTo(String value) {
			addCriterion("Id <>", value, "id");
			return (Criteria) this;
		}

		public Criteria andIdGreaterThan(String value) {
			addCriterion("Id >", value, "id");
			return (Criteria) this;
		}

		public Criteria andIdGreaterThanOrEqualTo(String value) {
			addCriterion("Id >=", value, "id");
			return (Criteria) this;
		}

		public Criteria andIdLessThan(String value) {
			addCriterion("Id <", value, "id");
			return (Criteria) this;
		}

		public Criteria andIdLessThanOrEqualTo(String value) {
			addCriterion("Id <=", value, "id");
			return (Criteria) this;
		}

		public Criteria andIdLike(String value) {
			addCriterion("Id like", value, "id");
			return (Criteria) this;
		}

		public Criteria andIdNotLike(String value) {
			addCriterion("Id not like", value, "id");
			return (Criteria) this;
		}

		public Criteria andIdIn(List<String> values) {
			addCriterion("Id in", values, "id");
			return (Criteria) this;
		}

		public Criteria andIdNotIn(List<String> values) {
			addCriterion("Id not in", values, "id");
			return (Criteria) this;
		}

		public Criteria andIdBetween(String value1, String value2) {
			addCriterion("Id between", value1, value2, "id");
			return (Criteria) this;
		}

		public Criteria andIdNotBetween(String value1, String value2) {
			addCriterion("Id not between", value1, value2, "id");
			return (Criteria) this;
		}

		public Criteria andFacilityIsNull() {
			addCriterion("Facility is null");
			return (Criteria) this;
		}

		public Criteria andFacilityIsNotNull() {
			addCriterion("Facility is not null");
			return (Criteria) this;
		}

		public Criteria andFacilityEqualTo(String value) {
			addCriterion("Facility =", value, "facility");
			return (Criteria) this;
		}

		public Criteria andFacilityNotEqualTo(String value) {
			addCriterion("Facility <>", value, "facility");
			return (Criteria) this;
		}

		public Criteria andFacilityGreaterThan(String value) {
			addCriterion("Facility >", value, "facility");
			return (Criteria) this;
		}

		public Criteria andFacilityGreaterThanOrEqualTo(String value) {
			addCriterion("Facility >=", value, "facility");
			return (Criteria) this;
		}

		public Criteria andFacilityLessThan(String value) {
			addCriterion("Facility <", value, "facility");
			return (Criteria) this;
		}

		public Criteria andFacilityLessThanOrEqualTo(String value) {
			addCriterion("Facility <=", value, "facility");
			return (Criteria) this;
		}

		public Criteria andFacilityLike(String value) {
			addCriterion("Facility like", value, "facility");
			return (Criteria) this;
		}

		public Criteria andFacilityNotLike(String value) {
			addCriterion("Facility not like", value, "facility");
			return (Criteria) this;
		}

		public Criteria andFacilityIn(List<String> values) {
			addCriterion("Facility in", values, "facility");
			return (Criteria) this;
		}

		public Criteria andFacilityNotIn(List<String> values) {
			addCriterion("Facility not in", values, "facility");
			return (Criteria) this;
		}

		public Criteria andFacilityBetween(String value1, String value2) {
			addCriterion("Facility between", value1, value2, "facility");
			return (Criteria) this;
		}

		public Criteria andFacilityNotBetween(String value1, String value2) {
			addCriterion("Facility not between", value1, value2, "facility");
			return (Criteria) this;
		}

		public Criteria andTransactiondateIsNull() {
			addCriterion("TransactionDate is null");
			return (Criteria) this;
		}

		public Criteria andTransactiondateIsNotNull() {
			addCriterion("TransactionDate is not null");
			return (Criteria) this;
		}

		public Criteria andTransactiondateEqualTo(String value) {
			addCriterion("TransactionDate =", value, "transactiondate");
			return (Criteria) this;
		}

		public Criteria andTransactiondateNotEqualTo(String value) {
			addCriterion("TransactionDate <>", value, "transactiondate");
			return (Criteria) this;
		}

		public Criteria andTransactiondateGreaterThan(String value) {
			addCriterion("TransactionDate >", value, "transactiondate");
			return (Criteria) this;
		}

		public Criteria andTransactiondateGreaterThanOrEqualTo(String value) {
			addCriterion("TransactionDate >=", value, "transactiondate");
			return (Criteria) this;
		}

		public Criteria andTransactiondateLessThan(String value) {
			addCriterion("TransactionDate <", value, "transactiondate");
			return (Criteria) this;
		}

		public Criteria andTransactiondateLessThanOrEqualTo(String value) {
			addCriterion("TransactionDate <=", value, "transactiondate");
			return (Criteria) this;
		}

		public Criteria andTransactiondateLike(String value) {
			addCriterion("TransactionDate like", value, "transactiondate");
			return (Criteria) this;
		}

		public Criteria andTransactiondateNotLike(String value) {
			addCriterion("TransactionDate not like", value, "transactiondate");
			return (Criteria) this;
		}

		public Criteria andTransactiondateIn(List<String> values) {
			addCriterion("TransactionDate in", values, "transactiondate");
			return (Criteria) this;
		}

		public Criteria andTransactiondateNotIn(List<String> values) {
			addCriterion("TransactionDate not in", values, "transactiondate");
			return (Criteria) this;
		}

		public Criteria andTransactiondateBetween(String value1, String value2) {
			addCriterion("TransactionDate between", value1, value2, "transactiondate");
			return (Criteria) this;
		}

		public Criteria andTransactiondateNotBetween(String value1, String value2) {
			addCriterion("TransactionDate not between", value1, value2, "transactiondate");
			return (Criteria) this;
		}

		public Criteria andVehiclenoIsNull() {
			addCriterion("VehicleNo is null");
			return (Criteria) this;
		}

		public Criteria andVehiclenoIsNotNull() {
			addCriterion("VehicleNo is not null");
			return (Criteria) this;
		}

		public Criteria andVehiclenoEqualTo(String value) {
			addCriterion("VehicleNo =", value, "vehicleno");
			return (Criteria) this;
		}

		public Criteria andVehiclenoNotEqualTo(String value) {
			addCriterion("VehicleNo <>", value, "vehicleno");
			return (Criteria) this;
		}

		public Criteria andVehiclenoGreaterThan(String value) {
			addCriterion("VehicleNo >", value, "vehicleno");
			return (Criteria) this;
		}

		public Criteria andVehiclenoGreaterThanOrEqualTo(String value) {
			addCriterion("VehicleNo >=", value, "vehicleno");
			return (Criteria) this;
		}

		public Criteria andVehiclenoLessThan(String value) {
			addCriterion("VehicleNo <", value, "vehicleno");
			return (Criteria) this;
		}

		public Criteria andVehiclenoLessThanOrEqualTo(String value) {
			addCriterion("VehicleNo <=", value, "vehicleno");
			return (Criteria) this;
		}

		public Criteria andVehiclenoLike(String value) {
			addCriterion("VehicleNo like", value, "vehicleno");
			return (Criteria) this;
		}

		public Criteria andVehiclenoNotLike(String value) {
			addCriterion("VehicleNo not like", value, "vehicleno");
			return (Criteria) this;
		}

		public Criteria andVehiclenoIn(List<String> values) {
			addCriterion("VehicleNo in", values, "vehicleno");
			return (Criteria) this;
		}

		public Criteria andVehiclenoNotIn(List<String> values) {
			addCriterion("VehicleNo not in", values, "vehicleno");
			return (Criteria) this;
		}

		public Criteria andVehiclenoBetween(String value1, String value2) {
			addCriterion("VehicleNo between", value1, value2, "vehicleno");
			return (Criteria) this;
		}

		public Criteria andVehiclenoNotBetween(String value1, String value2) {
			addCriterion("VehicleNo not between", value1, value2, "vehicleno");
			return (Criteria) this;
		}

		public Criteria andAccountnoIsNull() {
			addCriterion("AccountNo is null");
			return (Criteria) this;
		}

		public Criteria andAccountnoIsNotNull() {
			addCriterion("AccountNo is not null");
			return (Criteria) this;
		}

		public Criteria andAccountnoEqualTo(String value) {
			addCriterion("AccountNo =", value, "accountno");
			return (Criteria) this;
		}

		public Criteria andAccountnoNotEqualTo(String value) {
			addCriterion("AccountNo <>", value, "accountno");
			return (Criteria) this;
		}

		public Criteria andAccountnoGreaterThan(String value) {
			addCriterion("AccountNo >", value, "accountno");
			return (Criteria) this;
		}

		public Criteria andAccountnoGreaterThanOrEqualTo(String value) {
			addCriterion("AccountNo >=", value, "accountno");
			return (Criteria) this;
		}

		public Criteria andAccountnoLessThan(String value) {
			addCriterion("AccountNo <", value, "accountno");
			return (Criteria) this;
		}

		public Criteria andAccountnoLessThanOrEqualTo(String value) {
			addCriterion("AccountNo <=", value, "accountno");
			return (Criteria) this;
		}

		public Criteria andAccountnoLike(String value) {
			addCriterion("AccountNo like", value, "accountno");
			return (Criteria) this;
		}

		public Criteria andAccountnoNotLike(String value) {
			addCriterion("AccountNo not like", value, "accountno");
			return (Criteria) this;
		}

		public Criteria andAccountnoIn(List<String> values) {
			addCriterion("AccountNo in", values, "accountno");
			return (Criteria) this;
		}

		public Criteria andAccountnoNotIn(List<String> values) {
			addCriterion("AccountNo not in", values, "accountno");
			return (Criteria) this;
		}

		public Criteria andAccountnoBetween(String value1, String value2) {
			addCriterion("AccountNo between", value1, value2, "accountno");
			return (Criteria) this;
		}

		public Criteria andAccountnoNotBetween(String value1, String value2) {
			addCriterion("AccountNo not between", value1, value2, "accountno");
			return (Criteria) this;
		}

		public Criteria andChitnoIsNull() {
			addCriterion("ChitNo is null");
			return (Criteria) this;
		}

		public Criteria andChitnoIsNotNull() {
			addCriterion("ChitNo is not null");
			return (Criteria) this;
		}

		public Criteria andChitnoEqualTo(String value) {
			addCriterion("ChitNo =", value, "chitno");
			return (Criteria) this;
		}

		public Criteria andChitnoNotEqualTo(String value) {
			addCriterion("ChitNo <>", value, "chitno");
			return (Criteria) this;
		}

		public Criteria andChitnoGreaterThan(String value) {
			addCriterion("ChitNo >", value, "chitno");
			return (Criteria) this;
		}

		public Criteria andChitnoGreaterThanOrEqualTo(String value) {
			addCriterion("ChitNo >=", value, "chitno");
			return (Criteria) this;
		}

		public Criteria andChitnoLessThan(String value) {
			addCriterion("ChitNo <", value, "chitno");
			return (Criteria) this;
		}

		public Criteria andChitnoLessThanOrEqualTo(String value) {
			addCriterion("ChitNo <=", value, "chitno");
			return (Criteria) this;
		}

		public Criteria andChitnoLike(String value) {
			addCriterion("ChitNo like", value, "chitno");
			return (Criteria) this;
		}

		public Criteria andChitnoNotLike(String value) {
			addCriterion("ChitNo not like", value, "chitno");
			return (Criteria) this;
		}

		public Criteria andChitnoIn(List<String> values) {
			addCriterion("ChitNo in", values, "chitno");
			return (Criteria) this;
		}

		public Criteria andChitnoNotIn(List<String> values) {
			addCriterion("ChitNo not in", values, "chitno");
			return (Criteria) this;
		}

		public Criteria andChitnoBetween(String value1, String value2) {
			addCriterion("ChitNo between", value1, value2, "chitno");
			return (Criteria) this;
		}

		public Criteria andChitnoNotBetween(String value1, String value2) {
			addCriterion("ChitNo not between", value1, value2, "chitno");
			return (Criteria) this;
		}

		public Criteria andTimeinIsNull() {
			addCriterion("TimeIn is null");
			return (Criteria) this;
		}

		public Criteria andTimeinIsNotNull() {
			addCriterion("TimeIn is not null");
			return (Criteria) this;
		}

		public Criteria andTimeinEqualTo(String value) {
			addCriterion("TimeIn =", value, "timein");
			return (Criteria) this;
		}

		public Criteria andTimeinNotEqualTo(String value) {
			addCriterion("TimeIn <>", value, "timein");
			return (Criteria) this;
		}

		public Criteria andTimeinGreaterThan(String value) {
			addCriterion("TimeIn >", value, "timein");
			return (Criteria) this;
		}

		public Criteria andTimeinGreaterThanOrEqualTo(String value) {
			addCriterion("TimeIn >=", value, "timein");
			return (Criteria) this;
		}

		public Criteria andTimeinLessThan(String value) {
			addCriterion("TimeIn <", value, "timein");
			return (Criteria) this;
		}

		public Criteria andTimeinLessThanOrEqualTo(String value) {
			addCriterion("TimeIn <=", value, "timein");
			return (Criteria) this;
		}

		public Criteria andTimeinLike(String value) {
			addCriterion("TimeIn like", value, "timein");
			return (Criteria) this;
		}

		public Criteria andTimeinNotLike(String value) {
			addCriterion("TimeIn not like", value, "timein");
			return (Criteria) this;
		}

		public Criteria andTimeinIn(List<String> values) {
			addCriterion("TimeIn in", values, "timein");
			return (Criteria) this;
		}

		public Criteria andTimeinNotIn(List<String> values) {
			addCriterion("TimeIn not in", values, "timein");
			return (Criteria) this;
		}

		public Criteria andTimeinBetween(String value1, String value2) {
			addCriterion("TimeIn between", value1, value2, "timein");
			return (Criteria) this;
		}

		public Criteria andTimeinNotBetween(String value1, String value2) {
			addCriterion("TimeIn not between", value1, value2, "timein");
			return (Criteria) this;
		}

		public Criteria andTimeoutIsNull() {
			addCriterion("TimeOut is null");
			return (Criteria) this;
		}

		public Criteria andTimeoutIsNotNull() {
			addCriterion("TimeOut is not null");
			return (Criteria) this;
		}

		public Criteria andTimeoutEqualTo(String value) {
			addCriterion("TimeOut =", value, "timeout");
			return (Criteria) this;
		}

		public Criteria andTimeoutNotEqualTo(String value) {
			addCriterion("TimeOut <>", value, "timeout");
			return (Criteria) this;
		}

		public Criteria andTimeoutGreaterThan(String value) {
			addCriterion("TimeOut >", value, "timeout");
			return (Criteria) this;
		}

		public Criteria andTimeoutGreaterThanOrEqualTo(String value) {
			addCriterion("TimeOut >=", value, "timeout");
			return (Criteria) this;
		}

		public Criteria andTimeoutLessThan(String value) {
			addCriterion("TimeOut <", value, "timeout");
			return (Criteria) this;
		}

		public Criteria andTimeoutLessThanOrEqualTo(String value) {
			addCriterion("TimeOut <=", value, "timeout");
			return (Criteria) this;
		}

		public Criteria andTimeoutLike(String value) {
			addCriterion("TimeOut like", value, "timeout");
			return (Criteria) this;
		}

		public Criteria andTimeoutNotLike(String value) {
			addCriterion("TimeOut not like", value, "timeout");
			return (Criteria) this;
		}

		public Criteria andTimeoutIn(List<String> values) {
			addCriterion("TimeOut in", values, "timeout");
			return (Criteria) this;
		}

		public Criteria andTimeoutNotIn(List<String> values) {
			addCriterion("TimeOut not in", values, "timeout");
			return (Criteria) this;
		}

		public Criteria andTimeoutBetween(String value1, String value2) {
			addCriterion("TimeOut between", value1, value2, "timeout");
			return (Criteria) this;
		}

		public Criteria andTimeoutNotBetween(String value1, String value2) {
			addCriterion("TimeOut not between", value1, value2, "timeout");
			return (Criteria) this;
		}

		public Criteria andWastedepthIsNull() {
			addCriterion("WasteDepth is null");
			return (Criteria) this;
		}

		public Criteria andWastedepthIsNotNull() {
			addCriterion("WasteDepth is not null");
			return (Criteria) this;
		}

		public Criteria andWastedepthEqualTo(String value) {
			addCriterion("WasteDepth =", value, "wastedepth");
			return (Criteria) this;
		}

		public Criteria andWastedepthNotEqualTo(String value) {
			addCriterion("WasteDepth <>", value, "wastedepth");
			return (Criteria) this;
		}

		public Criteria andWastedepthGreaterThan(String value) {
			addCriterion("WasteDepth >", value, "wastedepth");
			return (Criteria) this;
		}

		public Criteria andWastedepthGreaterThanOrEqualTo(String value) {
			addCriterion("WasteDepth >=", value, "wastedepth");
			return (Criteria) this;
		}

		public Criteria andWastedepthLessThan(String value) {
			addCriterion("WasteDepth <", value, "wastedepth");
			return (Criteria) this;
		}

		public Criteria andWastedepthLessThanOrEqualTo(String value) {
			addCriterion("WasteDepth <=", value, "wastedepth");
			return (Criteria) this;
		}

		public Criteria andWastedepthLike(String value) {
			addCriterion("WasteDepth like", value, "wastedepth");
			return (Criteria) this;
		}

		public Criteria andWastedepthNotLike(String value) {
			addCriterion("WasteDepth not like", value, "wastedepth");
			return (Criteria) this;
		}

		public Criteria andWastedepthIn(List<String> values) {
			addCriterion("WasteDepth in", values, "wastedepth");
			return (Criteria) this;
		}

		public Criteria andWastedepthNotIn(List<String> values) {
			addCriterion("WasteDepth not in", values, "wastedepth");
			return (Criteria) this;
		}

		public Criteria andWastedepthBetween(String value1, String value2) {
			addCriterion("WasteDepth between", value1, value2, "wastedepth");
			return (Criteria) this;
		}

		public Criteria andWastedepthNotBetween(String value1, String value2) {
			addCriterion("WasteDepth not between", value1, value2, "wastedepth");
			return (Criteria) this;
		}

		public Criteria andWeightinIsNull() {
			addCriterion("WeightIn is null");
			return (Criteria) this;
		}

		public Criteria andWeightinIsNotNull() {
			addCriterion("WeightIn is not null");
			return (Criteria) this;
		}

		public Criteria andWeightinEqualTo(String value) {
			addCriterion("WeightIn =", value, "weightin");
			return (Criteria) this;
		}

		public Criteria andWeightinNotEqualTo(String value) {
			addCriterion("WeightIn <>", value, "weightin");
			return (Criteria) this;
		}

		public Criteria andWeightinGreaterThan(String value) {
			addCriterion("WeightIn >", value, "weightin");
			return (Criteria) this;
		}

		public Criteria andWeightinGreaterThanOrEqualTo(String value) {
			addCriterion("WeightIn >=", value, "weightin");
			return (Criteria) this;
		}

		public Criteria andWeightinLessThan(String value) {
			addCriterion("WeightIn <", value, "weightin");
			return (Criteria) this;
		}

		public Criteria andWeightinLessThanOrEqualTo(String value) {
			addCriterion("WeightIn <=", value, "weightin");
			return (Criteria) this;
		}

		public Criteria andWeightinLike(String value) {
			addCriterion("WeightIn like", value, "weightin");
			return (Criteria) this;
		}

		public Criteria andWeightinNotLike(String value) {
			addCriterion("WeightIn not like", value, "weightin");
			return (Criteria) this;
		}

		public Criteria andWeightinIn(List<String> values) {
			addCriterion("WeightIn in", values, "weightin");
			return (Criteria) this;
		}

		public Criteria andWeightinNotIn(List<String> values) {
			addCriterion("WeightIn not in", values, "weightin");
			return (Criteria) this;
		}

		public Criteria andWeightinBetween(String value1, String value2) {
			addCriterion("WeightIn between", value1, value2, "weightin");
			return (Criteria) this;
		}

		public Criteria andWeightinNotBetween(String value1, String value2) {
			addCriterion("WeightIn not between", value1, value2, "weightin");
			return (Criteria) this;
		}

		public Criteria andWeightoutIsNull() {
			addCriterion("WeightOut is null");
			return (Criteria) this;
		}

		public Criteria andWeightoutIsNotNull() {
			addCriterion("WeightOut is not null");
			return (Criteria) this;
		}

		public Criteria andWeightoutEqualTo(String value) {
			addCriterion("WeightOut =", value, "weightout");
			return (Criteria) this;
		}

		public Criteria andWeightoutNotEqualTo(String value) {
			addCriterion("WeightOut <>", value, "weightout");
			return (Criteria) this;
		}

		public Criteria andWeightoutGreaterThan(String value) {
			addCriterion("WeightOut >", value, "weightout");
			return (Criteria) this;
		}

		public Criteria andWeightoutGreaterThanOrEqualTo(String value) {
			addCriterion("WeightOut >=", value, "weightout");
			return (Criteria) this;
		}

		public Criteria andWeightoutLessThan(String value) {
			addCriterion("WeightOut <", value, "weightout");
			return (Criteria) this;
		}

		public Criteria andWeightoutLessThanOrEqualTo(String value) {
			addCriterion("WeightOut <=", value, "weightout");
			return (Criteria) this;
		}

		public Criteria andWeightoutLike(String value) {
			addCriterion("WeightOut like", value, "weightout");
			return (Criteria) this;
		}

		public Criteria andWeightoutNotLike(String value) {
			addCriterion("WeightOut not like", value, "weightout");
			return (Criteria) this;
		}

		public Criteria andWeightoutIn(List<String> values) {
			addCriterion("WeightOut in", values, "weightout");
			return (Criteria) this;
		}

		public Criteria andWeightoutNotIn(List<String> values) {
			addCriterion("WeightOut not in", values, "weightout");
			return (Criteria) this;
		}

		public Criteria andWeightoutBetween(String value1, String value2) {
			addCriterion("WeightOut between", value1, value2, "weightout");
			return (Criteria) this;
		}

		public Criteria andWeightoutNotBetween(String value1, String value2) {
			addCriterion("WeightOut not between", value1, value2, "weightout");
			return (Criteria) this;
		}

		public Criteria andNetweightIsNull() {
			addCriterion("NetWeight is null");
			return (Criteria) this;
		}

		public Criteria andNetweightIsNotNull() {
			addCriterion("NetWeight is not null");
			return (Criteria) this;
		}

		public Criteria andNetweightEqualTo(String value) {
			addCriterion("NetWeight =", value, "netweight");
			return (Criteria) this;
		}

		public Criteria andNetweightNotEqualTo(String value) {
			addCriterion("NetWeight <>", value, "netweight");
			return (Criteria) this;
		}

		public Criteria andNetweightGreaterThan(String value) {
			addCriterion("NetWeight >", value, "netweight");
			return (Criteria) this;
		}

		public Criteria andNetweightGreaterThanOrEqualTo(String value) {
			addCriterion("NetWeight >=", value, "netweight");
			return (Criteria) this;
		}

		public Criteria andNetweightLessThan(String value) {
			addCriterion("NetWeight <", value, "netweight");
			return (Criteria) this;
		}

		public Criteria andNetweightLessThanOrEqualTo(String value) {
			addCriterion("NetWeight <=", value, "netweight");
			return (Criteria) this;
		}

		public Criteria andNetweightLike(String value) {
			addCriterion("NetWeight like", value, "netweight");
			return (Criteria) this;
		}

		public Criteria andNetweightNotLike(String value) {
			addCriterion("NetWeight not like", value, "netweight");
			return (Criteria) this;
		}

		public Criteria andNetweightIn(List<String> values) {
			addCriterion("NetWeight in", values, "netweight");
			return (Criteria) this;
		}

		public Criteria andNetweightNotIn(List<String> values) {
			addCriterion("NetWeight not in", values, "netweight");
			return (Criteria) this;
		}

		public Criteria andNetweightBetween(String value1, String value2) {
			addCriterion("NetWeight between", value1, value2, "netweight");
			return (Criteria) this;
		}

		public Criteria andNetweightNotBetween(String value1, String value2) {
			addCriterion("NetWeight not between", value1, value2, "netweight");
			return (Criteria) this;
		}

		public Criteria andInputdatetimeIsNull() {
			addCriterion("InputDatetime is null");
			return (Criteria) this;
		}

		public Criteria andInputdatetimeIsNotNull() {
			addCriterion("InputDatetime is not null");
			return (Criteria) this;
		}

		public Criteria andInputdatetimeEqualTo(LocalDateTime value) {
			addCriterion("InputDatetime =", value, "inputdatetime");
			return (Criteria) this;
		}

		public Criteria andInputdatetimeNotEqualTo(LocalDateTime value) {
			addCriterion("InputDatetime <>", value, "inputdatetime");
			return (Criteria) this;
		}

		public Criteria andInputdatetimeGreaterThan(LocalDateTime value) {
			addCriterion("InputDatetime >", value, "inputdatetime");
			return (Criteria) this;
		}

		public Criteria andInputdatetimeGreaterThanOrEqualTo(LocalDateTime value) {
			addCriterion("InputDatetime >=", value, "inputdatetime");
			return (Criteria) this;
		}

		public Criteria andInputdatetimeLessThan(LocalDateTime value) {
			addCriterion("InputDatetime <", value, "inputdatetime");
			return (Criteria) this;
		}

		public Criteria andInputdatetimeLessThanOrEqualTo(LocalDateTime value) {
			addCriterion("InputDatetime <=", value, "inputdatetime");
			return (Criteria) this;
		}

		public Criteria andInputdatetimeIn(List<LocalDateTime> values) {
			addCriterion("InputDatetime in", values, "inputdatetime");
			return (Criteria) this;
		}

		public Criteria andInputdatetimeNotIn(List<LocalDateTime> values) {
			addCriterion("InputDatetime not in", values, "inputdatetime");
			return (Criteria) this;
		}

		public Criteria andInputdatetimeBetween(LocalDateTime value1, LocalDateTime value2) {
			addCriterion("InputDatetime between", value1, value2, "inputdatetime");
			return (Criteria) this;
		}

		public Criteria andInputdatetimeNotBetween(LocalDateTime value1, LocalDateTime value2) {
			addCriterion("InputDatetime not between", value1, value2, "inputdatetime");
			return (Criteria) this;
		}

		public Criteria andSyncdatetimeIsNull() {
			addCriterion("SyncDatetime is null");
			return (Criteria) this;
		}

		public Criteria andSyncdatetimeIsNotNull() {
			addCriterion("SyncDatetime is not null");
			return (Criteria) this;
		}

		public Criteria andSyncdatetimeEqualTo(LocalDateTime value) {
			addCriterion("SyncDatetime =", value, "syncdatetime");
			return (Criteria) this;
		}

		public Criteria andSyncdatetimeNotEqualTo(LocalDateTime value) {
			addCriterion("SyncDatetime <>", value, "syncdatetime");
			return (Criteria) this;
		}

		public Criteria andSyncdatetimeGreaterThan(LocalDateTime value) {
			addCriterion("SyncDatetime >", value, "syncdatetime");
			return (Criteria) this;
		}

		public Criteria andSyncdatetimeGreaterThanOrEqualTo(LocalDateTime value) {
			addCriterion("SyncDatetime >=", value, "syncdatetime");
			return (Criteria) this;
		}

		public Criteria andSyncdatetimeLessThan(LocalDateTime value) {
			addCriterion("SyncDatetime <", value, "syncdatetime");
			return (Criteria) this;
		}

		public Criteria andSyncdatetimeLessThanOrEqualTo(LocalDateTime value) {
			addCriterion("SyncDatetime <=", value, "syncdatetime");
			return (Criteria) this;
		}

		public Criteria andSyncdatetimeIn(List<LocalDateTime> values) {
			addCriterion("SyncDatetime in", values, "syncdatetime");
			return (Criteria) this;
		}

		public Criteria andSyncdatetimeNotIn(List<LocalDateTime> values) {
			addCriterion("SyncDatetime not in", values, "syncdatetime");
			return (Criteria) this;
		}

		public Criteria andSyncdatetimeBetween(LocalDateTime value1, LocalDateTime value2) {
			addCriterion("SyncDatetime between", value1, value2, "syncdatetime");
			return (Criteria) this;
		}

		public Criteria andSyncdatetimeNotBetween(LocalDateTime value1, LocalDateTime value2) {
			addCriterion("SyncDatetime not between", value1, value2, "syncdatetime");
			return (Criteria) this;
		}

		public Criteria andSynclogidIsNull() {
			addCriterion("SyncLogId is null");
			return (Criteria) this;
		}

		public Criteria andSynclogidIsNotNull() {
			addCriterion("SyncLogId is not null");
			return (Criteria) this;
		}

		public Criteria andSynclogidEqualTo(String value) {
			addCriterion("SyncLogId =", value, "synclogid");
			return (Criteria) this;
		}

		public Criteria andSynclogidNotEqualTo(String value) {
			addCriterion("SyncLogId <>", value, "synclogid");
			return (Criteria) this;
		}

		public Criteria andSynclogidGreaterThan(String value) {
			addCriterion("SyncLogId >", value, "synclogid");
			return (Criteria) this;
		}

		public Criteria andSynclogidGreaterThanOrEqualTo(String value) {
			addCriterion("SyncLogId >=", value, "synclogid");
			return (Criteria) this;
		}

		public Criteria andSynclogidLessThan(String value) {
			addCriterion("SyncLogId <", value, "synclogid");
			return (Criteria) this;
		}

		public Criteria andSynclogidLessThanOrEqualTo(String value) {
			addCriterion("SyncLogId <=", value, "synclogid");
			return (Criteria) this;
		}

		public Criteria andSynclogidLike(String value) {
			addCriterion("SyncLogId like", value, "synclogid");
			return (Criteria) this;
		}

		public Criteria andSynclogidNotLike(String value) {
			addCriterion("SyncLogId not like", value, "synclogid");
			return (Criteria) this;
		}

		public Criteria andSynclogidIn(List<String> values) {
			addCriterion("SyncLogId in", values, "synclogid");
			return (Criteria) this;
		}

		public Criteria andSynclogidNotIn(List<String> values) {
			addCriterion("SyncLogId not in", values, "synclogid");
			return (Criteria) this;
		}

		public Criteria andSynclogidBetween(String value1, String value2) {
			addCriterion("SyncLogId between", value1, value2, "synclogid");
			return (Criteria) this;
		}

		public Criteria andSynclogidNotBetween(String value1, String value2) {
			addCriterion("SyncLogId not between", value1, value2, "synclogid");
			return (Criteria) this;
		}

		public Criteria andProcessdatetimeIsNull() {
			addCriterion("ProcessDatetime is null");
			return (Criteria) this;
		}

		public Criteria andProcessdatetimeIsNotNull() {
			addCriterion("ProcessDatetime is not null");
			return (Criteria) this;
		}

		public Criteria andProcessdatetimeEqualTo(LocalDateTime value) {
			addCriterion("ProcessDatetime =", value, "processdatetime");
			return (Criteria) this;
		}

		public Criteria andProcessdatetimeNotEqualTo(LocalDateTime value) {
			addCriterion("ProcessDatetime <>", value, "processdatetime");
			return (Criteria) this;
		}

		public Criteria andProcessdatetimeGreaterThan(LocalDateTime value) {
			addCriterion("ProcessDatetime >", value, "processdatetime");
			return (Criteria) this;
		}

		public Criteria andProcessdatetimeGreaterThanOrEqualTo(LocalDateTime value) {
			addCriterion("ProcessDatetime >=", value, "processdatetime");
			return (Criteria) this;
		}

		public Criteria andProcessdatetimeLessThan(LocalDateTime value) {
			addCriterion("ProcessDatetime <", value, "processdatetime");
			return (Criteria) this;
		}

		public Criteria andProcessdatetimeLessThanOrEqualTo(LocalDateTime value) {
			addCriterion("ProcessDatetime <=", value, "processdatetime");
			return (Criteria) this;
		}

		public Criteria andProcessdatetimeIn(List<LocalDateTime> values) {
			addCriterion("ProcessDatetime in", values, "processdatetime");
			return (Criteria) this;
		}

		public Criteria andProcessdatetimeNotIn(List<LocalDateTime> values) {
			addCriterion("ProcessDatetime not in", values, "processdatetime");
			return (Criteria) this;
		}

		public Criteria andProcessdatetimeBetween(LocalDateTime value1, LocalDateTime value2) {
			addCriterion("ProcessDatetime between", value1, value2, "processdatetime");
			return (Criteria) this;
		}

		public Criteria andProcessdatetimeNotBetween(LocalDateTime value1, LocalDateTime value2) {
			addCriterion("ProcessDatetime not between", value1, value2, "processdatetime");
			return (Criteria) this;
		}
	}

	/**
	 * This class was generated by MyBatis Generator. This class corresponds to the database table RawEpdWasteTransportation
	 * @mbg.generated  Wed Dec 14 17:16:15 HKT 2022
	 */
	public static class Criterion {
		private String condition;
		private Object value;
		private Object secondValue;
		private boolean noValue;
		private boolean singleValue;
		private boolean betweenValue;
		private boolean listValue;
		private String typeHandler;

		public String getCondition() {
			return condition;
		}

		public Object getValue() {
			return value;
		}

		public Object getSecondValue() {
			return secondValue;
		}

		public boolean isNoValue() {
			return noValue;
		}

		public boolean isSingleValue() {
			return singleValue;
		}

		public boolean isBetweenValue() {
			return betweenValue;
		}

		public boolean isListValue() {
			return listValue;
		}

		public String getTypeHandler() {
			return typeHandler;
		}

		protected Criterion(String condition) {
			super();
			this.condition = condition;
			this.typeHandler = null;
			this.noValue = true;
		}

		protected Criterion(String condition, Object value, String typeHandler) {
			super();
			this.condition = condition;
			this.value = value;
			this.typeHandler = typeHandler;
			if (value instanceof List<?>) {
				this.listValue = true;
			} else {
				this.singleValue = true;
			}
		}

		protected Criterion(String condition, Object value) {
			this(condition, value, null);
		}

		protected Criterion(String condition, Object value, Object secondValue, String typeHandler) {
			super();
			this.condition = condition;
			this.value = value;
			this.secondValue = secondValue;
			this.typeHandler = typeHandler;
			this.betweenValue = true;
		}

		protected Criterion(String condition, Object value, Object secondValue) {
			this(condition, value, secondValue, null);
		}
	}

	/**
     * This class was generated by MyBatis Generator.
     * This class corresponds to the database table RawEpdWasteTransportation
     *
     * @mbg.generated do_not_delete_during_merge Tue Dec 06 15:42:47 HKT 2022
     */
    public static class Criteria extends GeneratedCriteria {
        protected Criteria() {
            super();
        }
    }
}