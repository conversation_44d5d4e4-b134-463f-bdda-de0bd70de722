<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.csci.susdev.mapper.OrganizationCustomMapper">
    <select id="listOrganization" resultType="com.csci.susdev.vo.OrganizationVO">
        SELECT  org.id, org.parent_id parentId, org.no, org.name, org.code,
                org.unit_code unitCode, org.title, org.address, org.latitude, org.longitude,
                org.currency, org.division, org.region, org.power_supply supply, org.start_date startDate,
                org.end_date endDate, org.is_completed isCompleted, org.jv, org.total_cfa totalCfa, org.sort,
                org.scene, org.area_id areaId, org.type,org.last_update_version lastUpdateVersion,area.name areaName
        FROM t_organization org
                 left join t_area area on area.id=org.area_id
        WHERE (org.is_deleted = 0)
          <if test="listOrganizationId != null and listOrganizationId.size() > 0">
              and org.id in
              <foreach collection="listOrganizationId" open="(" close=")" separator="," item="id">
                #{id}
              </foreach>
          </if>
          <if test="parentId != null and parentId != ''">
              and org.parent_id = #{parentId}
          </if>
          <if test="parentId != null and parentId != ''">
              and org.name like concat('%',#{name}, '%')
          </if>
            <if test="showCompanyOnly != null and showCompanyOnly != false ">
                and LEN(org.no) &lt;= 6
            </if>
        order by org.no
    </select>
</mapper>