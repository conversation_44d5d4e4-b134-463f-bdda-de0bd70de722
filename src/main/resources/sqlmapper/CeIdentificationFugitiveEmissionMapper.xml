<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.csci.susdev.mapper.CeIdentificationFugitiveEmissionMapper">
  <resultMap id="BaseResultMap" type="com.csci.susdev.model.CeIdentificationFugitiveEmission">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Wed Apr 02 16:23:01 CST 2025.
    -->
    <id column="id" jdbcType="CHAR" property="id" />
    <result column="ce_identification_head_id" jdbcType="CHAR" property="ceIdentificationHeadId" />
    <result column="fugitive_module" jdbcType="NVARCHAR" property="fugitiveModule" />
    <result column="confirmation_item_one" jdbcType="NVARCHAR" property="confirmationItemOne" />
    <result column="confirmation_item_two" jdbcType="NVARCHAR" property="confirmationItemTwo" />
    <result column="confirmation_item_three" jdbcType="NVARCHAR" property="confirmationItemThree" />
    <result column="instructions " jdbcType="NVARCHAR" property="instructions" />
    <result column="input_value" jdbcType="NVARCHAR" property="inputValue" />
    <result column="seq" jdbcType="INTEGER" property="seq" />
    <result column="creation_time" jdbcType="TIMESTAMP" property="creationTime" />
    <result column="create_username" jdbcType="NVARCHAR" property="createUsername" />
    <result column="create_user_id" jdbcType="NVARCHAR" property="createUserId" />
    <result column="last_update_time" jdbcType="TIMESTAMP" property="lastUpdateTime" />
    <result column="last_update_username" jdbcType="NVARCHAR" property="lastUpdateUsername" />
    <result column="last_update_user_id" jdbcType="NVARCHAR" property="lastUpdateUserId" />
    <result column="last_update_version" jdbcType="INTEGER" property="lastUpdateVersion" />
    <result column="is_deleted" jdbcType="BIT" property="isDeleted" />
    <result column="control_col" jdbcType="NVARCHAR" property="controlCol" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Wed Apr 02 16:23:01 CST 2025.
    -->
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Wed Apr 02 16:23:01 CST 2025.
    -->
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Wed Apr 02 16:23:01 CST 2025.
    -->
    id, ce_identification_head_id, fugitive_module, confirmation_item_one, confirmation_item_two, 
    confirmation_item_three, "instructions ", input_value, seq, creation_time, create_username, 
    create_user_id, last_update_time, last_update_username, last_update_user_id, last_update_version, 
    is_deleted, control_col
  </sql>
  <select id="selectByExample" parameterType="com.csci.susdev.model.CeIdentificationFugitiveEmissionExample" resultMap="BaseResultMap">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Wed Apr 02 16:23:01 CST 2025.
    -->
    select
    <if test="distinct">
      distinct
    </if>
    'true' as QUERYID,
    <include refid="Base_Column_List" />
    from t_ce_identification_fugitive_emission
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.String" resultMap="BaseResultMap">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Wed Apr 02 16:23:01 CST 2025.
    -->
    select 
    <include refid="Base_Column_List" />
    from t_ce_identification_fugitive_emission
    where id = #{id,jdbcType=CHAR}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.String">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Wed Apr 02 16:23:01 CST 2025.
    -->
    delete from t_ce_identification_fugitive_emission
    where id = #{id,jdbcType=CHAR}
  </delete>
  <delete id="deleteByExample" parameterType="com.csci.susdev.model.CeIdentificationFugitiveEmissionExample">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Wed Apr 02 16:23:01 CST 2025.
    -->
    delete from t_ce_identification_fugitive_emission
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.csci.susdev.model.CeIdentificationFugitiveEmission">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Wed Apr 02 16:23:01 CST 2025.
    -->
    insert into t_ce_identification_fugitive_emission (id, ce_identification_head_id, fugitive_module, 
      confirmation_item_one, confirmation_item_two, 
      confirmation_item_three, "instructions ", 
      input_value, seq, creation_time, 
      create_username, create_user_id, last_update_time, 
      last_update_username, last_update_user_id, 
      last_update_version, is_deleted, control_col
      )
    values (#{id,jdbcType=CHAR}, #{ceIdentificationHeadId,jdbcType=CHAR}, #{fugitiveModule,jdbcType=NVARCHAR}, 
      #{confirmationItemOne,jdbcType=NVARCHAR}, #{confirmationItemTwo,jdbcType=NVARCHAR}, 
      #{confirmationItemThree,jdbcType=NVARCHAR}, #{instructions,jdbcType=NVARCHAR}, 
      #{inputValue,jdbcType=NVARCHAR}, #{seq,jdbcType=INTEGER}, #{creationTime,jdbcType=TIMESTAMP}, 
      #{createUsername,jdbcType=NVARCHAR}, #{createUserId,jdbcType=NVARCHAR}, #{lastUpdateTime,jdbcType=TIMESTAMP}, 
      #{lastUpdateUsername,jdbcType=NVARCHAR}, #{lastUpdateUserId,jdbcType=NVARCHAR}, 
      #{lastUpdateVersion,jdbcType=INTEGER}, #{isDeleted,jdbcType=BIT}, #{controlCol,jdbcType=NVARCHAR}
      )
  </insert>
  <insert id="insertSelective" parameterType="com.csci.susdev.model.CeIdentificationFugitiveEmission">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Wed Apr 02 16:23:01 CST 2025.
    -->
    insert into t_ce_identification_fugitive_emission
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="ceIdentificationHeadId != null">
        ce_identification_head_id,
      </if>
      <if test="fugitiveModule != null">
        fugitive_module,
      </if>
      <if test="confirmationItemOne != null">
        confirmation_item_one,
      </if>
      <if test="confirmationItemTwo != null">
        confirmation_item_two,
      </if>
      <if test="confirmationItemThree != null">
        confirmation_item_three,
      </if>
      <if test="instructions != null">
        "instructions ",
      </if>
      <if test="inputValue != null">
        input_value,
      </if>
      <if test="seq != null">
        seq,
      </if>
      <if test="creationTime != null">
        creation_time,
      </if>
      <if test="createUsername != null">
        create_username,
      </if>
      <if test="createUserId != null">
        create_user_id,
      </if>
      <if test="lastUpdateTime != null">
        last_update_time,
      </if>
      <if test="lastUpdateUsername != null">
        last_update_username,
      </if>
      <if test="lastUpdateUserId != null">
        last_update_user_id,
      </if>
      <if test="lastUpdateVersion != null">
        last_update_version,
      </if>
      <if test="isDeleted != null">
        is_deleted,
      </if>
      <if test="controlCol != null">
        control_col,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=CHAR},
      </if>
      <if test="ceIdentificationHeadId != null">
        #{ceIdentificationHeadId,jdbcType=CHAR},
      </if>
      <if test="fugitiveModule != null">
        #{fugitiveModule,jdbcType=NVARCHAR},
      </if>
      <if test="confirmationItemOne != null">
        #{confirmationItemOne,jdbcType=NVARCHAR},
      </if>
      <if test="confirmationItemTwo != null">
        #{confirmationItemTwo,jdbcType=NVARCHAR},
      </if>
      <if test="confirmationItemThree != null">
        #{confirmationItemThree,jdbcType=NVARCHAR},
      </if>
      <if test="instructions != null">
        #{instructions,jdbcType=NVARCHAR},
      </if>
      <if test="inputValue != null">
        #{inputValue,jdbcType=NVARCHAR},
      </if>
      <if test="seq != null">
        #{seq,jdbcType=INTEGER},
      </if>
      <if test="creationTime != null">
        #{creationTime,jdbcType=TIMESTAMP},
      </if>
      <if test="createUsername != null">
        #{createUsername,jdbcType=NVARCHAR},
      </if>
      <if test="createUserId != null">
        #{createUserId,jdbcType=NVARCHAR},
      </if>
      <if test="lastUpdateTime != null">
        #{lastUpdateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="lastUpdateUsername != null">
        #{lastUpdateUsername,jdbcType=NVARCHAR},
      </if>
      <if test="lastUpdateUserId != null">
        #{lastUpdateUserId,jdbcType=NVARCHAR},
      </if>
      <if test="lastUpdateVersion != null">
        #{lastUpdateVersion,jdbcType=INTEGER},
      </if>
      <if test="isDeleted != null">
        #{isDeleted,jdbcType=BIT},
      </if>
      <if test="controlCol != null">
        #{controlCol,jdbcType=NVARCHAR},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.csci.susdev.model.CeIdentificationFugitiveEmissionExample" resultType="java.lang.Long">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Wed Apr 02 16:23:01 CST 2025.
    -->
    select count(*) from t_ce_identification_fugitive_emission
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Wed Apr 02 16:23:01 CST 2025.
    -->
    update t_ce_identification_fugitive_emission
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=CHAR},
      </if>
      <if test="record.ceIdentificationHeadId != null">
        ce_identification_head_id = #{record.ceIdentificationHeadId,jdbcType=CHAR},
      </if>
      <if test="record.fugitiveModule != null">
        fugitive_module = #{record.fugitiveModule,jdbcType=NVARCHAR},
      </if>
      <if test="record.confirmationItemOne != null">
        confirmation_item_one = #{record.confirmationItemOne,jdbcType=NVARCHAR},
      </if>
      <if test="record.confirmationItemTwo != null">
        confirmation_item_two = #{record.confirmationItemTwo,jdbcType=NVARCHAR},
      </if>
      <if test="record.confirmationItemThree != null">
        confirmation_item_three = #{record.confirmationItemThree,jdbcType=NVARCHAR},
      </if>
      <if test="record.instructions != null">
        "instructions " = #{record.instructions,jdbcType=NVARCHAR},
      </if>
      <if test="record.inputValue != null">
        input_value = #{record.inputValue,jdbcType=NVARCHAR},
      </if>
      <if test="record.seq != null">
        seq = #{record.seq,jdbcType=INTEGER},
      </if>
      <if test="record.creationTime != null">
        creation_time = #{record.creationTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.createUsername != null">
        create_username = #{record.createUsername,jdbcType=NVARCHAR},
      </if>
      <if test="record.createUserId != null">
        create_user_id = #{record.createUserId,jdbcType=NVARCHAR},
      </if>
      <if test="record.lastUpdateTime != null">
        last_update_time = #{record.lastUpdateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.lastUpdateUsername != null">
        last_update_username = #{record.lastUpdateUsername,jdbcType=NVARCHAR},
      </if>
      <if test="record.lastUpdateUserId != null">
        last_update_user_id = #{record.lastUpdateUserId,jdbcType=NVARCHAR},
      </if>
      <if test="record.lastUpdateVersion != null">
        last_update_version = #{record.lastUpdateVersion,jdbcType=INTEGER},
      </if>
      <if test="record.isDeleted != null">
        is_deleted = #{record.isDeleted,jdbcType=BIT},
      </if>
      <if test="record.controlCol != null">
        control_col = #{record.controlCol,jdbcType=NVARCHAR},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Wed Apr 02 16:23:01 CST 2025.
    -->
    update t_ce_identification_fugitive_emission
    set id = #{record.id,jdbcType=CHAR},
      ce_identification_head_id = #{record.ceIdentificationHeadId,jdbcType=CHAR},
      fugitive_module = #{record.fugitiveModule,jdbcType=NVARCHAR},
      confirmation_item_one = #{record.confirmationItemOne,jdbcType=NVARCHAR},
      confirmation_item_two = #{record.confirmationItemTwo,jdbcType=NVARCHAR},
      confirmation_item_three = #{record.confirmationItemThree,jdbcType=NVARCHAR},
      "instructions " = #{record.instructions,jdbcType=NVARCHAR},
      input_value = #{record.inputValue,jdbcType=NVARCHAR},
      seq = #{record.seq,jdbcType=INTEGER},
      creation_time = #{record.creationTime,jdbcType=TIMESTAMP},
      create_username = #{record.createUsername,jdbcType=NVARCHAR},
      create_user_id = #{record.createUserId,jdbcType=NVARCHAR},
      last_update_time = #{record.lastUpdateTime,jdbcType=TIMESTAMP},
      last_update_username = #{record.lastUpdateUsername,jdbcType=NVARCHAR},
      last_update_user_id = #{record.lastUpdateUserId,jdbcType=NVARCHAR},
      last_update_version = #{record.lastUpdateVersion,jdbcType=INTEGER},
      is_deleted = #{record.isDeleted,jdbcType=BIT},
      control_col = #{record.controlCol,jdbcType=NVARCHAR}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.csci.susdev.model.CeIdentificationFugitiveEmission">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Wed Apr 02 16:23:01 CST 2025.
    -->
    update t_ce_identification_fugitive_emission
    <set>
      <if test="ceIdentificationHeadId != null">
        ce_identification_head_id = #{ceIdentificationHeadId,jdbcType=CHAR},
      </if>
      <if test="fugitiveModule != null">
        fugitive_module = #{fugitiveModule,jdbcType=NVARCHAR},
      </if>
      <if test="confirmationItemOne != null">
        confirmation_item_one = #{confirmationItemOne,jdbcType=NVARCHAR},
      </if>
      <if test="confirmationItemTwo != null">
        confirmation_item_two = #{confirmationItemTwo,jdbcType=NVARCHAR},
      </if>
      <if test="confirmationItemThree != null">
        confirmation_item_three = #{confirmationItemThree,jdbcType=NVARCHAR},
      </if>
      <if test="instructions != null">
        "instructions " = #{instructions,jdbcType=NVARCHAR},
      </if>
      <if test="inputValue != null">
        input_value = #{inputValue,jdbcType=NVARCHAR},
      </if>
      <if test="seq != null">
        seq = #{seq,jdbcType=INTEGER},
      </if>
      <if test="creationTime != null">
        creation_time = #{creationTime,jdbcType=TIMESTAMP},
      </if>
      <if test="createUsername != null">
        create_username = #{createUsername,jdbcType=NVARCHAR},
      </if>
      <if test="createUserId != null">
        create_user_id = #{createUserId,jdbcType=NVARCHAR},
      </if>
      <if test="lastUpdateTime != null">
        last_update_time = #{lastUpdateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="lastUpdateUsername != null">
        last_update_username = #{lastUpdateUsername,jdbcType=NVARCHAR},
      </if>
      <if test="lastUpdateUserId != null">
        last_update_user_id = #{lastUpdateUserId,jdbcType=NVARCHAR},
      </if>
      <if test="lastUpdateVersion != null">
        last_update_version = #{lastUpdateVersion,jdbcType=INTEGER},
      </if>
      <if test="isDeleted != null">
        is_deleted = #{isDeleted,jdbcType=BIT},
      </if>
      <if test="controlCol != null">
        control_col = #{controlCol,jdbcType=NVARCHAR},
      </if>
    </set>
    where id = #{id,jdbcType=CHAR}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.csci.susdev.model.CeIdentificationFugitiveEmission">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Wed Apr 02 16:23:01 CST 2025.
    -->
    update t_ce_identification_fugitive_emission
    set ce_identification_head_id = #{ceIdentificationHeadId,jdbcType=CHAR},
      fugitive_module = #{fugitiveModule,jdbcType=NVARCHAR},
      confirmation_item_one = #{confirmationItemOne,jdbcType=NVARCHAR},
      confirmation_item_two = #{confirmationItemTwo,jdbcType=NVARCHAR},
      confirmation_item_three = #{confirmationItemThree,jdbcType=NVARCHAR},
      "instructions " = #{instructions,jdbcType=NVARCHAR},
      input_value = #{inputValue,jdbcType=NVARCHAR},
      seq = #{seq,jdbcType=INTEGER},
      creation_time = #{creationTime,jdbcType=TIMESTAMP},
      create_username = #{createUsername,jdbcType=NVARCHAR},
      create_user_id = #{createUserId,jdbcType=NVARCHAR},
      last_update_time = #{lastUpdateTime,jdbcType=TIMESTAMP},
      last_update_username = #{lastUpdateUsername,jdbcType=NVARCHAR},
      last_update_user_id = #{lastUpdateUserId,jdbcType=NVARCHAR},
      last_update_version = #{lastUpdateVersion,jdbcType=INTEGER},
      is_deleted = #{isDeleted,jdbcType=BIT},
      control_col = #{controlCol,jdbcType=NVARCHAR}
    where id = #{id,jdbcType=CHAR}
  </update>
</mapper>