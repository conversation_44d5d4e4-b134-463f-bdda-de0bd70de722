<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.csci.susdev.mapper.FlightInfoCustomMapper">

  <insert id="batchInsert">
    INSERT INTO t_flight_info (id,record_year,start_place,destination,LEVEL,
    ticket_type,person_count,flight_distance,carbon_emission,creation_time,
    create_username,create_user_id,last_update_time,last_update_username,last_update_user_id)
    VALUES
    <foreach collection ="list" item="flightInfo" separator =",">
      (#{flightInfo.id}, #{flightInfo.recordYear}, #{flightInfo.startPlace},#{flightInfo.destination}, #{flightInfo.level},
      #{flightInfo.ticketType},#{flightInfo.personCount}, #{flightInfo.flightDistance},#{flightInfo.carbonEmission},#{flightInfo.creationTime},
      #{flightInfo.createUsername},#{flightInfo.createUserId},#{flightInfo.lastUpdateTime}, #{flightInfo.lastUpdateUsername}, #{flightInfo.lastUpdateUserId})
    </foreach>
  </insert>
</mapper>