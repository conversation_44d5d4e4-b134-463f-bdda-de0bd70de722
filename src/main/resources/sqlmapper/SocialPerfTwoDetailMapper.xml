<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.csci.susdev.mapper.SocialPerfTwoDetailMapper">
  <resultMap id="BaseResultMap" type="com.csci.susdev.model.SocialPerfTwoDetail">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    <id column="id" jdbcType="VARCHAR" property="id" />
    <result column="head_id" jdbcType="VARCHAR" property="headId" />
    <result column="category" jdbcType="VARCHAR" property="category" />
    <result column="report_item" jdbcType="VARCHAR" property="reportItem" />
    <result column="classification1" jdbcType="VARCHAR" property="classification1" />
    <result column="classification2" jdbcType="VARCHAR" property="classification2" />
    <result column="classification3" jdbcType="VARCHAR" property="classification3" />
    <result column="classification4" jdbcType="VARCHAR" property="classification4" />
    <result column="classification5" jdbcType="VARCHAR" property="classification5" />
    <result column="classification6" jdbcType="VARCHAR" property="classification6" />
    <result column="remark" jdbcType="VARCHAR" property="remark" />
    <result column="is_header" jdbcType="BIT" property="header" />
    <result column="seq" jdbcType="INTEGER" property="seq" />
    <result column="creation_time" jdbcType="TIMESTAMP" property="creationTime" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    id, head_id, category, report_item, classification1, classification2, classification3, 
    classification4, classification5, classification6, remark, is_header, seq, creation_time
  </sql>
  <select id="selectByExample" parameterType="com.csci.susdev.model.SocialPerfTwoDetailExample" resultMap="BaseResultMap">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from t_social_perf_two_detail
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.String" resultMap="BaseResultMap">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    select 
    <include refid="Base_Column_List" />
    from t_social_perf_two_detail
    where id = #{id,jdbcType=VARCHAR}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.String">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    delete from t_social_perf_two_detail
    where id = #{id,jdbcType=VARCHAR}
  </delete>
  <delete id="deleteByExample" parameterType="com.csci.susdev.model.SocialPerfTwoDetailExample">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    delete from t_social_perf_two_detail
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.csci.susdev.model.SocialPerfTwoDetail">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    insert into t_social_perf_two_detail (id, head_id, category, 
      report_item, classification1, classification2, 
      classification3, classification4, classification5, 
      classification6, remark, is_header, 
      seq, creation_time)
    values (#{id,jdbcType=VARCHAR}, #{headId,jdbcType=VARCHAR}, #{category,jdbcType=VARCHAR}, 
      #{reportItem,jdbcType=VARCHAR}, #{classification1,jdbcType=VARCHAR}, #{classification2,jdbcType=VARCHAR}, 
      #{classification3,jdbcType=VARCHAR}, #{classification4,jdbcType=VARCHAR}, #{classification5,jdbcType=VARCHAR}, 
      #{classification6,jdbcType=VARCHAR}, #{remark,jdbcType=VARCHAR}, #{header,jdbcType=BIT}, 
      #{seq,jdbcType=INTEGER}, #{creationTime,jdbcType=TIMESTAMP})
  </insert>
  <insert id="insertSelective" parameterType="com.csci.susdev.model.SocialPerfTwoDetail">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    insert into t_social_perf_two_detail
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="headId != null">
        head_id,
      </if>
      <if test="category != null">
        category,
      </if>
      <if test="reportItem != null">
        report_item,
      </if>
      <if test="classification1 != null">
        classification1,
      </if>
      <if test="classification2 != null">
        classification2,
      </if>
      <if test="classification3 != null">
        classification3,
      </if>
      <if test="classification4 != null">
        classification4,
      </if>
      <if test="classification5 != null">
        classification5,
      </if>
      <if test="classification6 != null">
        classification6,
      </if>
      <if test="remark != null">
        remark,
      </if>
      <if test="header != null">
        is_header,
      </if>
      <if test="seq != null">
        seq,
      </if>
      <if test="creationTime != null">
        creation_time,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=VARCHAR},
      </if>
      <if test="headId != null">
        #{headId,jdbcType=VARCHAR},
      </if>
      <if test="category != null">
        #{category,jdbcType=VARCHAR},
      </if>
      <if test="reportItem != null">
        #{reportItem,jdbcType=VARCHAR},
      </if>
      <if test="classification1 != null">
        #{classification1,jdbcType=VARCHAR},
      </if>
      <if test="classification2 != null">
        #{classification2,jdbcType=VARCHAR},
      </if>
      <if test="classification3 != null">
        #{classification3,jdbcType=VARCHAR},
      </if>
      <if test="classification4 != null">
        #{classification4,jdbcType=VARCHAR},
      </if>
      <if test="classification5 != null">
        #{classification5,jdbcType=VARCHAR},
      </if>
      <if test="classification6 != null">
        #{classification6,jdbcType=VARCHAR},
      </if>
      <if test="remark != null">
        #{remark,jdbcType=VARCHAR},
      </if>
      <if test="header != null">
        #{header,jdbcType=BIT},
      </if>
      <if test="seq != null">
        #{seq,jdbcType=INTEGER},
      </if>
      <if test="creationTime != null">
        #{creationTime,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.csci.susdev.model.SocialPerfTwoDetailExample" resultType="java.lang.Long">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    select count(*) from t_social_perf_two_detail
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    update t_social_perf_two_detail
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=VARCHAR},
      </if>
      <if test="record.headId != null">
        head_id = #{record.headId,jdbcType=VARCHAR},
      </if>
      <if test="record.category != null">
        category = #{record.category,jdbcType=VARCHAR},
      </if>
      <if test="record.reportItem != null">
        report_item = #{record.reportItem,jdbcType=VARCHAR},
      </if>
      <if test="record.classification1 != null">
        classification1 = #{record.classification1,jdbcType=VARCHAR},
      </if>
      <if test="record.classification2 != null">
        classification2 = #{record.classification2,jdbcType=VARCHAR},
      </if>
      <if test="record.classification3 != null">
        classification3 = #{record.classification3,jdbcType=VARCHAR},
      </if>
      <if test="record.classification4 != null">
        classification4 = #{record.classification4,jdbcType=VARCHAR},
      </if>
      <if test="record.classification5 != null">
        classification5 = #{record.classification5,jdbcType=VARCHAR},
      </if>
      <if test="record.classification6 != null">
        classification6 = #{record.classification6,jdbcType=VARCHAR},
      </if>
      <if test="record.remark != null">
        remark = #{record.remark,jdbcType=VARCHAR},
      </if>
      <if test="record.header != null">
        is_header = #{record.header,jdbcType=BIT},
      </if>
      <if test="record.seq != null">
        seq = #{record.seq,jdbcType=INTEGER},
      </if>
      <if test="record.creationTime != null">
        creation_time = #{record.creationTime,jdbcType=TIMESTAMP},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    update t_social_perf_two_detail
    set id = #{record.id,jdbcType=VARCHAR},
      head_id = #{record.headId,jdbcType=VARCHAR},
      category = #{record.category,jdbcType=VARCHAR},
      report_item = #{record.reportItem,jdbcType=VARCHAR},
      classification1 = #{record.classification1,jdbcType=VARCHAR},
      classification2 = #{record.classification2,jdbcType=VARCHAR},
      classification3 = #{record.classification3,jdbcType=VARCHAR},
      classification4 = #{record.classification4,jdbcType=VARCHAR},
      classification5 = #{record.classification5,jdbcType=VARCHAR},
      classification6 = #{record.classification6,jdbcType=VARCHAR},
      remark = #{record.remark,jdbcType=VARCHAR},
      is_header = #{record.header,jdbcType=BIT},
      seq = #{record.seq,jdbcType=INTEGER},
      creation_time = #{record.creationTime,jdbcType=TIMESTAMP}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.csci.susdev.model.SocialPerfTwoDetail">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    update t_social_perf_two_detail
    <set>
      <if test="headId != null">
        head_id = #{headId,jdbcType=VARCHAR},
      </if>
      <if test="category != null">
        category = #{category,jdbcType=VARCHAR},
      </if>
      <if test="reportItem != null">
        report_item = #{reportItem,jdbcType=VARCHAR},
      </if>
      <if test="classification1 != null">
        classification1 = #{classification1,jdbcType=VARCHAR},
      </if>
      <if test="classification2 != null">
        classification2 = #{classification2,jdbcType=VARCHAR},
      </if>
      <if test="classification3 != null">
        classification3 = #{classification3,jdbcType=VARCHAR},
      </if>
      <if test="classification4 != null">
        classification4 = #{classification4,jdbcType=VARCHAR},
      </if>
      <if test="classification5 != null">
        classification5 = #{classification5,jdbcType=VARCHAR},
      </if>
      <if test="classification6 != null">
        classification6 = #{classification6,jdbcType=VARCHAR},
      </if>
      <if test="remark != null">
        remark = #{remark,jdbcType=VARCHAR},
      </if>
      <if test="header != null">
        is_header = #{header,jdbcType=BIT},
      </if>
      <if test="seq != null">
        seq = #{seq,jdbcType=INTEGER},
      </if>
      <if test="creationTime != null">
        creation_time = #{creationTime,jdbcType=TIMESTAMP},
      </if>
    </set>
    where id = #{id,jdbcType=VARCHAR}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.csci.susdev.model.SocialPerfTwoDetail">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    update t_social_perf_two_detail
    set head_id = #{headId,jdbcType=VARCHAR},
      category = #{category,jdbcType=VARCHAR},
      report_item = #{reportItem,jdbcType=VARCHAR},
      classification1 = #{classification1,jdbcType=VARCHAR},
      classification2 = #{classification2,jdbcType=VARCHAR},
      classification3 = #{classification3,jdbcType=VARCHAR},
      classification4 = #{classification4,jdbcType=VARCHAR},
      classification5 = #{classification5,jdbcType=VARCHAR},
      classification6 = #{classification6,jdbcType=VARCHAR},
      remark = #{remark,jdbcType=VARCHAR},
      is_header = #{header,jdbcType=BIT},
      seq = #{seq,jdbcType=INTEGER},
      creation_time = #{creationTime,jdbcType=TIMESTAMP}
    where id = #{id,jdbcType=VARCHAR}
  </update>
</mapper>