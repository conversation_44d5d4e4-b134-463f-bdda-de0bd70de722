<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.csci.susdev.mapper.FlightInfoMapper">
  <resultMap id="BaseResultMap" type="com.csci.susdev.model.FlightInfo">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Thu Jun 08 11:34:14 HKT 2023.
    -->
    <id column="id" jdbcType="VARCHAR" property="id" />
    <result column="record_year" jdbcType="INTEGER" property="recordYear" />
    <result column="start_place" jdbcType="VARCHAR" property="startPlace" />
    <result column="destination" jdbcType="VARCHAR" property="destination" />
    <result column="level" jdbcType="VARCHAR" property="level" />
    <result column="ticket_type" jdbcType="INTEGER" property="ticketType" />
    <result column="person_count" jdbcType="INTEGER" property="personCount" />
    <result column="flight_distance" jdbcType="DECIMAL" property="flightDistance" />
    <result column="carbon_emission" jdbcType="DECIMAL" property="carbonEmission" />
    <result column="creation_time" jdbcType="TIMESTAMP" property="creationTime" />
    <result column="create_username" jdbcType="VARCHAR" property="createUsername" />
    <result column="create_user_id" jdbcType="VARCHAR" property="createUserId" />
    <result column="last_update_time" jdbcType="TIMESTAMP" property="lastUpdateTime" />
    <result column="last_update_username" jdbcType="VARCHAR" property="lastUpdateUsername" />
    <result column="last_update_user_id" jdbcType="VARCHAR" property="lastUpdateUserId" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Thu Jun 08 11:34:14 HKT 2023.
    -->
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Thu Jun 08 11:34:14 HKT 2023.
    -->
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Thu Jun 08 11:34:14 HKT 2023.
    -->
    id, record_year, start_place, destination, level, ticket_type, person_count, flight_distance, 
    carbon_emission, creation_time, create_username, create_user_id, last_update_time, 
    last_update_username, last_update_user_id
  </sql>
  <select id="selectByExample" parameterType="com.csci.susdev.model.FlightInfoExample" resultMap="BaseResultMap">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Thu Jun 08 11:34:14 HKT 2023.
    -->
    select
    <if test="distinct">
      distinct
    </if>
    'true' as QUERYID,
    <include refid="Base_Column_List" />
    from t_flight_info
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.String" resultMap="BaseResultMap">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Thu Jun 08 11:34:14 HKT 2023.
    -->
    select 
    <include refid="Base_Column_List" />
    from t_flight_info
    where id = #{id,jdbcType=VARCHAR}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.String">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Thu Jun 08 11:34:14 HKT 2023.
    -->
    delete from t_flight_info
    where id = #{id,jdbcType=VARCHAR}
  </delete>
  <delete id="deleteByExample" parameterType="com.csci.susdev.model.FlightInfoExample">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Thu Jun 08 11:34:14 HKT 2023.
    -->
    delete from t_flight_info
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.csci.susdev.model.FlightInfo">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Thu Jun 08 11:34:14 HKT 2023.
    -->
    insert into t_flight_info (id, record_year, start_place, 
      destination, level, ticket_type, 
      person_count, flight_distance, carbon_emission, 
      creation_time, create_username, create_user_id, 
      last_update_time, last_update_username, last_update_user_id
      )
    values (#{id,jdbcType=VARCHAR}, #{recordYear,jdbcType=INTEGER}, #{startPlace,jdbcType=VARCHAR}, 
      #{destination,jdbcType=VARCHAR}, #{level,jdbcType=VARCHAR}, #{ticketType,jdbcType=INTEGER}, 
      #{personCount,jdbcType=INTEGER}, #{flightDistance,jdbcType=DECIMAL}, #{carbonEmission,jdbcType=DECIMAL}, 
      #{creationTime,jdbcType=TIMESTAMP}, #{createUsername,jdbcType=VARCHAR}, #{createUserId,jdbcType=VARCHAR}, 
      #{lastUpdateTime,jdbcType=TIMESTAMP}, #{lastUpdateUsername,jdbcType=VARCHAR}, #{lastUpdateUserId,jdbcType=VARCHAR}
      )
  </insert>
  <insert id="insertSelective" parameterType="com.csci.susdev.model.FlightInfo">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Thu Jun 08 11:34:14 HKT 2023.
    -->
    insert into t_flight_info
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="recordYear != null">
        record_year,
      </if>
      <if test="startPlace != null">
        start_place,
      </if>
      <if test="destination != null">
        destination,
      </if>
      <if test="level != null">
        level,
      </if>
      <if test="ticketType != null">
        ticket_type,
      </if>
      <if test="personCount != null">
        person_count,
      </if>
      <if test="flightDistance != null">
        flight_distance,
      </if>
      <if test="carbonEmission != null">
        carbon_emission,
      </if>
      <if test="creationTime != null">
        creation_time,
      </if>
      <if test="createUsername != null">
        create_username,
      </if>
      <if test="createUserId != null">
        create_user_id,
      </if>
      <if test="lastUpdateTime != null">
        last_update_time,
      </if>
      <if test="lastUpdateUsername != null">
        last_update_username,
      </if>
      <if test="lastUpdateUserId != null">
        last_update_user_id,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=VARCHAR},
      </if>
      <if test="recordYear != null">
        #{recordYear,jdbcType=INTEGER},
      </if>
      <if test="startPlace != null">
        #{startPlace,jdbcType=VARCHAR},
      </if>
      <if test="destination != null">
        #{destination,jdbcType=VARCHAR},
      </if>
      <if test="level != null">
        #{level,jdbcType=VARCHAR},
      </if>
      <if test="ticketType != null">
        #{ticketType,jdbcType=INTEGER},
      </if>
      <if test="personCount != null">
        #{personCount,jdbcType=INTEGER},
      </if>
      <if test="flightDistance != null">
        #{flightDistance,jdbcType=DECIMAL},
      </if>
      <if test="carbonEmission != null">
        #{carbonEmission,jdbcType=DECIMAL},
      </if>
      <if test="creationTime != null">
        #{creationTime,jdbcType=TIMESTAMP},
      </if>
      <if test="createUsername != null">
        #{createUsername,jdbcType=VARCHAR},
      </if>
      <if test="createUserId != null">
        #{createUserId,jdbcType=VARCHAR},
      </if>
      <if test="lastUpdateTime != null">
        #{lastUpdateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="lastUpdateUsername != null">
        #{lastUpdateUsername,jdbcType=VARCHAR},
      </if>
      <if test="lastUpdateUserId != null">
        #{lastUpdateUserId,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.csci.susdev.model.FlightInfoExample" resultType="java.lang.Long">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Thu Jun 08 11:34:14 HKT 2023.
    -->
    select count(*) from t_flight_info
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Thu Jun 08 11:34:14 HKT 2023.
    -->
    update t_flight_info
    <set>
      <if test="row.id != null">
        id = #{row.id,jdbcType=VARCHAR},
      </if>
      <if test="row.recordYear != null">
        record_year = #{row.recordYear,jdbcType=INTEGER},
      </if>
      <if test="row.startPlace != null">
        start_place = #{row.startPlace,jdbcType=VARCHAR},
      </if>
      <if test="row.destination != null">
        destination = #{row.destination,jdbcType=VARCHAR},
      </if>
      <if test="row.level != null">
        level = #{row.level,jdbcType=VARCHAR},
      </if>
      <if test="row.ticketType != null">
        ticket_type = #{row.ticketType,jdbcType=INTEGER},
      </if>
      <if test="row.personCount != null">
        person_count = #{row.personCount,jdbcType=INTEGER},
      </if>
      <if test="row.flightDistance != null">
        flight_distance = #{row.flightDistance,jdbcType=DECIMAL},
      </if>
      <if test="row.carbonEmission != null">
        carbon_emission = #{row.carbonEmission,jdbcType=DECIMAL},
      </if>
      <if test="row.creationTime != null">
        creation_time = #{row.creationTime,jdbcType=TIMESTAMP},
      </if>
      <if test="row.createUsername != null">
        create_username = #{row.createUsername,jdbcType=VARCHAR},
      </if>
      <if test="row.createUserId != null">
        create_user_id = #{row.createUserId,jdbcType=VARCHAR},
      </if>
      <if test="row.lastUpdateTime != null">
        last_update_time = #{row.lastUpdateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="row.lastUpdateUsername != null">
        last_update_username = #{row.lastUpdateUsername,jdbcType=VARCHAR},
      </if>
      <if test="row.lastUpdateUserId != null">
        last_update_user_id = #{row.lastUpdateUserId,jdbcType=VARCHAR},
      </if>
    </set>
    <if test="example != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Thu Jun 08 11:34:14 HKT 2023.
    -->
    update t_flight_info
    set id = #{row.id,jdbcType=VARCHAR},
      record_year = #{row.recordYear,jdbcType=INTEGER},
      start_place = #{row.startPlace,jdbcType=VARCHAR},
      destination = #{row.destination,jdbcType=VARCHAR},
      level = #{row.level,jdbcType=VARCHAR},
      ticket_type = #{row.ticketType,jdbcType=INTEGER},
      person_count = #{row.personCount,jdbcType=INTEGER},
      flight_distance = #{row.flightDistance,jdbcType=DECIMAL},
      carbon_emission = #{row.carbonEmission,jdbcType=DECIMAL},
      creation_time = #{row.creationTime,jdbcType=TIMESTAMP},
      create_username = #{row.createUsername,jdbcType=VARCHAR},
      create_user_id = #{row.createUserId,jdbcType=VARCHAR},
      last_update_time = #{row.lastUpdateTime,jdbcType=TIMESTAMP},
      last_update_username = #{row.lastUpdateUsername,jdbcType=VARCHAR},
      last_update_user_id = #{row.lastUpdateUserId,jdbcType=VARCHAR}
    <if test="example != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.csci.susdev.model.FlightInfo">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Thu Jun 08 11:34:14 HKT 2023.
    -->
    update t_flight_info
    <set>
      <if test="recordYear != null">
        record_year = #{recordYear,jdbcType=INTEGER},
      </if>
      <if test="startPlace != null">
        start_place = #{startPlace,jdbcType=VARCHAR},
      </if>
      <if test="destination != null">
        destination = #{destination,jdbcType=VARCHAR},
      </if>
      <if test="level != null">
        level = #{level,jdbcType=VARCHAR},
      </if>
      <if test="ticketType != null">
        ticket_type = #{ticketType,jdbcType=INTEGER},
      </if>
      <if test="personCount != null">
        person_count = #{personCount,jdbcType=INTEGER},
      </if>
      <if test="flightDistance != null">
        flight_distance = #{flightDistance,jdbcType=DECIMAL},
      </if>
      <if test="carbonEmission != null">
        carbon_emission = #{carbonEmission,jdbcType=DECIMAL},
      </if>
      <if test="creationTime != null">
        creation_time = #{creationTime,jdbcType=TIMESTAMP},
      </if>
      <if test="createUsername != null">
        create_username = #{createUsername,jdbcType=VARCHAR},
      </if>
      <if test="createUserId != null">
        create_user_id = #{createUserId,jdbcType=VARCHAR},
      </if>
      <if test="lastUpdateTime != null">
        last_update_time = #{lastUpdateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="lastUpdateUsername != null">
        last_update_username = #{lastUpdateUsername,jdbcType=VARCHAR},
      </if>
      <if test="lastUpdateUserId != null">
        last_update_user_id = #{lastUpdateUserId,jdbcType=VARCHAR},
      </if>
    </set>
    where id = #{id,jdbcType=VARCHAR}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.csci.susdev.model.FlightInfo">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Thu Jun 08 11:34:14 HKT 2023.
    -->
    update t_flight_info
    set record_year = #{recordYear,jdbcType=INTEGER},
      start_place = #{startPlace,jdbcType=VARCHAR},
      destination = #{destination,jdbcType=VARCHAR},
      level = #{level,jdbcType=VARCHAR},
      ticket_type = #{ticketType,jdbcType=INTEGER},
      person_count = #{personCount,jdbcType=INTEGER},
      flight_distance = #{flightDistance,jdbcType=DECIMAL},
      carbon_emission = #{carbonEmission,jdbcType=DECIMAL},
      creation_time = #{creationTime,jdbcType=TIMESTAMP},
      create_username = #{createUsername,jdbcType=VARCHAR},
      create_user_id = #{createUserId,jdbcType=VARCHAR},
      last_update_time = #{lastUpdateTime,jdbcType=TIMESTAMP},
      last_update_username = #{lastUpdateUsername,jdbcType=VARCHAR},
      last_update_user_id = #{lastUpdateUserId,jdbcType=VARCHAR}
    where id = #{id,jdbcType=VARCHAR}
  </update>
</mapper>