<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.csci.susdev.mapper.TzhBsUserRoleMapper">
  <resultMap id="BaseResultMap" type="com.csci.susdev.model.TzhBsUserRole">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Tue Jun 04 12:09:18 HKT 2024.
    -->
    <id column="Id" jdbcType="CHAR" property="id" />
    <result column="UserName" jdbcType="VARCHAR" property="username" />
    <result column="RoleName" jdbcType="VARCHAR" property="rolename" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Tue Jun 04 12:09:18 HKT 2024.
    -->
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Tue Jun 04 12:09:18 HKT 2024.
    -->
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Tue Jun 04 12:09:18 HKT 2024.
    -->
    Id, UserName, RoleName
  </sql>
  <select id="selectByExample" parameterType="com.csci.susdev.model.TzhBsUserRoleExample" resultMap="BaseResultMap">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Tue Jun 04 12:09:18 HKT 2024.
    -->
    select
    <if test="distinct">
      distinct
    </if>
    'true' as QUERYID,
    <include refid="Base_Column_List" />
    from Tzh_Bs_UserRole
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.String" resultMap="BaseResultMap">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Tue Jun 04 12:09:18 HKT 2024.
    -->
    select 
    <include refid="Base_Column_List" />
    from Tzh_Bs_UserRole
    where Id = #{id,jdbcType=CHAR}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.String">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Tue Jun 04 12:09:18 HKT 2024.
    -->
    delete from Tzh_Bs_UserRole
    where Id = #{id,jdbcType=CHAR}
  </delete>
  <delete id="deleteByExample" parameterType="com.csci.susdev.model.TzhBsUserRoleExample">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Tue Jun 04 12:09:18 HKT 2024.
    -->
    delete from Tzh_Bs_UserRole
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.csci.susdev.model.TzhBsUserRole">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Tue Jun 04 12:09:18 HKT 2024.
    -->
    insert into Tzh_Bs_UserRole (Id, UserName, RoleName
      )
    values (#{id,jdbcType=CHAR}, #{username,jdbcType=VARCHAR}, #{rolename,jdbcType=VARCHAR}
      )
  </insert>
  <insert id="insertSelective" parameterType="com.csci.susdev.model.TzhBsUserRole">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Tue Jun 04 12:09:18 HKT 2024.
    -->
    insert into Tzh_Bs_UserRole
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        Id,
      </if>
      <if test="username != null">
        UserName,
      </if>
      <if test="rolename != null">
        RoleName,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=CHAR},
      </if>
      <if test="username != null">
        #{username,jdbcType=VARCHAR},
      </if>
      <if test="rolename != null">
        #{rolename,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.csci.susdev.model.TzhBsUserRoleExample" resultType="java.lang.Long">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Tue Jun 04 12:09:18 HKT 2024.
    -->
    select count(*) from Tzh_Bs_UserRole
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Tue Jun 04 12:09:18 HKT 2024.
    -->
    update Tzh_Bs_UserRole
    <set>
      <if test="record.id != null">
        Id = #{record.id,jdbcType=CHAR},
      </if>
      <if test="record.username != null">
        UserName = #{record.username,jdbcType=VARCHAR},
      </if>
      <if test="record.rolename != null">
        RoleName = #{record.rolename,jdbcType=VARCHAR},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Tue Jun 04 12:09:18 HKT 2024.
    -->
    update Tzh_Bs_UserRole
    set Id = #{record.id,jdbcType=CHAR},
      UserName = #{record.username,jdbcType=VARCHAR},
      RoleName = #{record.rolename,jdbcType=VARCHAR}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.csci.susdev.model.TzhBsUserRole">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Tue Jun 04 12:09:18 HKT 2024.
    -->
    update Tzh_Bs_UserRole
    <set>
      <if test="username != null">
        UserName = #{username,jdbcType=VARCHAR},
      </if>
      <if test="rolename != null">
        RoleName = #{rolename,jdbcType=VARCHAR},
      </if>
    </set>
    where Id = #{id,jdbcType=CHAR}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.csci.susdev.model.TzhBsUserRole">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Tue Jun 04 12:09:18 HKT 2024.
    -->
    update Tzh_Bs_UserRole
    set UserName = #{username,jdbcType=VARCHAR},
      RoleName = #{rolename,jdbcType=VARCHAR}
    where Id = #{id,jdbcType=CHAR}
  </update>
</mapper>