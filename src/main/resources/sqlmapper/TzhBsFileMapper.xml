<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.csci.susdev.mapper.TzhBsFileMapper">
  <resultMap id="BaseResultMap" type="com.csci.susdev.model.TzhBsFile">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Fri Jun 07 11:29:56 HKT 2024.
    -->
    <result column="Id" jdbcType="CHAR" property="id" />
    <result column="SiteName" jdbcType="NVARCHAR" property="sitename" />
    <result column="ProtocolName" jdbcType="NVARCHAR" property="protocolname" />
    <result column="RefId" jdbcType="NVARCHAR" property="refid" />
    <result column="Section" jdbcType="NVARCHAR" property="section" />
    <result column="Category" jdbcType="NVARCHAR" property="category" />
    <result column="Name" jdbcType="NVARCHAR" property="name" />
    <result column="Type" jdbcType="NVARCHAR" property="type" />
    <result column="CreatedBy" jdbcType="VARCHAR" property="createdby" />
    <result column="CreatedTime" jdbcType="TIMESTAMP" property="createdtime" />
    <result column="DeletedBy" jdbcType="VARCHAR" property="deletedby" />
    <result column="DeletedTime" jdbcType="TIMESTAMP" property="deletedtime" />
    <result column="IsDeleted" jdbcType="BIT" property="isdeleted" />
  </resultMap>
  <resultMap extends="BaseResultMap" id="ResultMapWithBLOBs" type="com.csci.susdev.model.TzhBsFile">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Fri Jun 07 11:29:56 HKT 2024.
    -->
    <result column="Data" jdbcType="VARBINARY" property="data" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Fri Jun 07 11:29:56 HKT 2024.
    -->
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Fri Jun 07 11:29:56 HKT 2024.
    -->
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Fri Jun 07 11:29:56 HKT 2024.
    -->
    Id, SiteName, ProtocolName, RefId, Section, Category, Name, Type, CreatedBy, CreatedTime, 
    DeletedBy, DeletedTime, IsDeleted
  </sql>
  <sql id="Blob_Column_List">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Fri Jun 07 11:29:56 HKT 2024.
    -->
    Data
  </sql>
  <select id="selectByExampleWithBLOBs" parameterType="com.csci.susdev.model.TzhBsFileExample" resultMap="ResultMapWithBLOBs">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Fri Jun 07 11:29:56 HKT 2024.
    -->
    select
    <if test="distinct">
      distinct
    </if>
    'true' as QUERYID,
    <include refid="Base_Column_List" />
    ,
    <include refid="Blob_Column_List" />
    from Tzh_Bs_File
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByExample" parameterType="com.csci.susdev.model.TzhBsFileExample" resultMap="BaseResultMap">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Fri Jun 07 11:29:56 HKT 2024.
    -->
    select
    <if test="distinct">
      distinct
    </if>
    'true' as QUERYID,
    <include refid="Base_Column_List" />
    from Tzh_Bs_File
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>
  <delete id="deleteByExample" parameterType="com.csci.susdev.model.TzhBsFileExample">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Fri Jun 07 11:29:56 HKT 2024.
    -->
    delete from Tzh_Bs_File
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.csci.susdev.model.TzhBsFile">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Fri Jun 07 11:29:56 HKT 2024.
    -->
    insert into Tzh_Bs_File (Id, SiteName, ProtocolName, 
      RefId, Section, Category, 
      Name, Type, CreatedBy, 
      CreatedTime, DeletedBy, DeletedTime, 
      IsDeleted, Data)
    values (#{id,jdbcType=CHAR}, #{sitename,jdbcType=NVARCHAR}, #{protocolname,jdbcType=NVARCHAR}, 
      #{refid,jdbcType=NVARCHAR}, #{section,jdbcType=NVARCHAR}, #{category,jdbcType=NVARCHAR}, 
      #{name,jdbcType=NVARCHAR}, #{type,jdbcType=NVARCHAR}, #{createdby,jdbcType=VARCHAR}, 
      #{createdtime,jdbcType=TIMESTAMP}, #{deletedby,jdbcType=VARCHAR}, #{deletedtime,jdbcType=TIMESTAMP}, 
      #{isdeleted,jdbcType=BIT}, #{data,jdbcType=VARBINARY})
  </insert>
  <insert id="insertSelective" parameterType="com.csci.susdev.model.TzhBsFile">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Fri Jun 07 11:29:56 HKT 2024.
    -->
    insert into Tzh_Bs_File
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        Id,
      </if>
      <if test="sitename != null">
        SiteName,
      </if>
      <if test="protocolname != null">
        ProtocolName,
      </if>
      <if test="refid != null">
        RefId,
      </if>
      <if test="section != null">
        Section,
      </if>
      <if test="category != null">
        Category,
      </if>
      <if test="name != null">
        Name,
      </if>
      <if test="type != null">
        Type,
      </if>
      <if test="createdby != null">
        CreatedBy,
      </if>
      <if test="createdtime != null">
        CreatedTime,
      </if>
      <if test="deletedby != null">
        DeletedBy,
      </if>
      <if test="deletedtime != null">
        DeletedTime,
      </if>
      <if test="isdeleted != null">
        IsDeleted,
      </if>
      <if test="data != null">
        Data,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=CHAR},
      </if>
      <if test="sitename != null">
        #{sitename,jdbcType=NVARCHAR},
      </if>
      <if test="protocolname != null">
        #{protocolname,jdbcType=NVARCHAR},
      </if>
      <if test="refid != null">
        #{refid,jdbcType=NVARCHAR},
      </if>
      <if test="section != null">
        #{section,jdbcType=NVARCHAR},
      </if>
      <if test="category != null">
        #{category,jdbcType=NVARCHAR},
      </if>
      <if test="name != null">
        #{name,jdbcType=NVARCHAR},
      </if>
      <if test="type != null">
        #{type,jdbcType=NVARCHAR},
      </if>
      <if test="createdby != null">
        #{createdby,jdbcType=VARCHAR},
      </if>
      <if test="createdtime != null">
        #{createdtime,jdbcType=TIMESTAMP},
      </if>
      <if test="deletedby != null">
        #{deletedby,jdbcType=VARCHAR},
      </if>
      <if test="deletedtime != null">
        #{deletedtime,jdbcType=TIMESTAMP},
      </if>
      <if test="isdeleted != null">
        #{isdeleted,jdbcType=BIT},
      </if>
      <if test="data != null">
        #{data,jdbcType=VARBINARY},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.csci.susdev.model.TzhBsFileExample" resultType="java.lang.Long">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Fri Jun 07 11:29:56 HKT 2024.
    -->
    select count(*) from Tzh_Bs_File
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Fri Jun 07 11:29:56 HKT 2024.
    -->
    update Tzh_Bs_File
    <set>
      <if test="record.id != null">
        Id = #{record.id,jdbcType=CHAR},
      </if>
      <if test="record.sitename != null">
        SiteName = #{record.sitename,jdbcType=NVARCHAR},
      </if>
      <if test="record.protocolname != null">
        ProtocolName = #{record.protocolname,jdbcType=NVARCHAR},
      </if>
      <if test="record.refid != null">
        RefId = #{record.refid,jdbcType=NVARCHAR},
      </if>
      <if test="record.section != null">
        Section = #{record.section,jdbcType=NVARCHAR},
      </if>
      <if test="record.category != null">
        Category = #{record.category,jdbcType=NVARCHAR},
      </if>
      <if test="record.name != null">
        Name = #{record.name,jdbcType=NVARCHAR},
      </if>
      <if test="record.type != null">
        Type = #{record.type,jdbcType=NVARCHAR},
      </if>
      <if test="record.createdby != null">
        CreatedBy = #{record.createdby,jdbcType=VARCHAR},
      </if>
      <if test="record.createdtime != null">
        CreatedTime = #{record.createdtime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.deletedby != null">
        DeletedBy = #{record.deletedby,jdbcType=VARCHAR},
      </if>
      <if test="record.deletedtime != null">
        DeletedTime = #{record.deletedtime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.isdeleted != null">
        IsDeleted = #{record.isdeleted,jdbcType=BIT},
      </if>
      <if test="record.data != null">
        Data = #{record.data,jdbcType=VARBINARY},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExampleWithBLOBs" parameterType="map">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Fri Jun 07 11:29:56 HKT 2024.
    -->
    update Tzh_Bs_File
    set Id = #{record.id,jdbcType=CHAR},
      SiteName = #{record.sitename,jdbcType=NVARCHAR},
      ProtocolName = #{record.protocolname,jdbcType=NVARCHAR},
      RefId = #{record.refid,jdbcType=NVARCHAR},
      Section = #{record.section,jdbcType=NVARCHAR},
      Category = #{record.category,jdbcType=NVARCHAR},
      Name = #{record.name,jdbcType=NVARCHAR},
      Type = #{record.type,jdbcType=NVARCHAR},
      CreatedBy = #{record.createdby,jdbcType=VARCHAR},
      CreatedTime = #{record.createdtime,jdbcType=TIMESTAMP},
      DeletedBy = #{record.deletedby,jdbcType=VARCHAR},
      DeletedTime = #{record.deletedtime,jdbcType=TIMESTAMP},
      IsDeleted = #{record.isdeleted,jdbcType=BIT},
      Data = #{record.data,jdbcType=VARBINARY}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Fri Jun 07 11:29:56 HKT 2024.
    -->
    update Tzh_Bs_File
    set Id = #{record.id,jdbcType=CHAR},
      SiteName = #{record.sitename,jdbcType=NVARCHAR},
      ProtocolName = #{record.protocolname,jdbcType=NVARCHAR},
      RefId = #{record.refid,jdbcType=NVARCHAR},
      Section = #{record.section,jdbcType=NVARCHAR},
      Category = #{record.category,jdbcType=NVARCHAR},
      Name = #{record.name,jdbcType=NVARCHAR},
      Type = #{record.type,jdbcType=NVARCHAR},
      CreatedBy = #{record.createdby,jdbcType=VARCHAR},
      CreatedTime = #{record.createdtime,jdbcType=TIMESTAMP},
      DeletedBy = #{record.deletedby,jdbcType=VARCHAR},
      DeletedTime = #{record.deletedtime,jdbcType=TIMESTAMP},
      IsDeleted = #{record.isdeleted,jdbcType=BIT}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
</mapper>