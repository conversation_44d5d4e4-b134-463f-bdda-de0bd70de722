<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.csci.susdev.mapper.AmbientEnergyBillMapper">
  <resultMap id="BaseResultMap" type="com.csci.susdev.model.AmbientEnergyBill">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Mon Oct 09 15:36:35 HKT 2023.
    -->
    <id column="id" jdbcType="VARCHAR" property="id" />
    <result column="head_id" jdbcType="VARCHAR" property="headId" />
    <result column="type" jdbcType="VARCHAR" property="type" />
    <result column="bill_no" jdbcType="VARCHAR" property="billNo" />
    <result column="from_time" jdbcType="TIMESTAMP" property="fromTime" />
    <result column="to_time" jdbcType="TIMESTAMP" property="toTime" />
    <result column="consumption" jdbcType="DECIMAL" property="consumption" />
    <result column="attachment_id" jdbcType="VARCHAR" property="attachmentId" />
    <result column="creation_time" jdbcType="TIMESTAMP" property="creationTime" />
    <result column="create_username" jdbcType="VARCHAR" property="createUsername" />
    <result column="create_user_id" jdbcType="VARCHAR" property="createUserId" />
    <result column="last_update_time" jdbcType="TIMESTAMP" property="lastUpdateTime" />
    <result column="last_update_username" jdbcType="VARCHAR" property="lastUpdateUsername" />
    <result column="last_update_user_id" jdbcType="VARCHAR" property="lastUpdateUserId" />
    <result column="last_update_version" jdbcType="INTEGER" property="lastUpdateVersion" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Mon Oct 09 15:36:35 HKT 2023.
    -->
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Mon Oct 09 15:36:35 HKT 2023.
    -->
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Mon Oct 09 15:36:35 HKT 2023.
    -->
    id, head_id, type, bill_no, from_time, to_time, consumption, attachment_id, creation_time, 
    create_username, create_user_id, last_update_time, last_update_username, last_update_user_id, 
    last_update_version
  </sql>
  <select id="selectByExample" parameterType="com.csci.susdev.model.AmbientEnergyBillExample" resultMap="BaseResultMap">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Mon Oct 09 15:36:35 HKT 2023.
    -->
    select
    <if test="distinct">
      distinct
    </if>
    'true' as QUERYID,
    <include refid="Base_Column_List" />
    from t_ambient_energy_bill
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.String" resultMap="BaseResultMap">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Mon Oct 09 15:36:35 HKT 2023.
    -->
    select 
    <include refid="Base_Column_List" />
    from t_ambient_energy_bill
    where id = #{id,jdbcType=VARCHAR}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.String">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Mon Oct 09 15:36:35 HKT 2023.
    -->
    delete from t_ambient_energy_bill
    where id = #{id,jdbcType=VARCHAR}
  </delete>
  <delete id="deleteByExample" parameterType="com.csci.susdev.model.AmbientEnergyBillExample">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Mon Oct 09 15:36:35 HKT 2023.
    -->
    delete from t_ambient_energy_bill
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.csci.susdev.model.AmbientEnergyBill">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Mon Oct 09 15:36:35 HKT 2023.
    -->
    insert into t_ambient_energy_bill (id, head_id, type, 
      bill_no, from_time, to_time, 
      consumption, attachment_id, creation_time, 
      create_username, create_user_id, last_update_time, 
      last_update_username, last_update_user_id, last_update_version
      )
    values (#{id,jdbcType=VARCHAR}, #{headId,jdbcType=VARCHAR}, #{type,jdbcType=VARCHAR}, 
      #{billNo,jdbcType=VARCHAR}, #{fromTime,jdbcType=TIMESTAMP}, #{toTime,jdbcType=TIMESTAMP}, 
      #{consumption,jdbcType=DECIMAL}, #{attachmentId,jdbcType=VARCHAR}, #{creationTime,jdbcType=TIMESTAMP}, 
      #{createUsername,jdbcType=VARCHAR}, #{createUserId,jdbcType=VARCHAR}, #{lastUpdateTime,jdbcType=TIMESTAMP}, 
      #{lastUpdateUsername,jdbcType=VARCHAR}, #{lastUpdateUserId,jdbcType=VARCHAR}, #{lastUpdateVersion,jdbcType=INTEGER}
      )
  </insert>
  <insert id="insertSelective" parameterType="com.csci.susdev.model.AmbientEnergyBill">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Mon Oct 09 15:36:35 HKT 2023.
    -->
    insert into t_ambient_energy_bill
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="headId != null">
        head_id,
      </if>
      <if test="type != null">
        type,
      </if>
      <if test="billNo != null">
        bill_no,
      </if>
      <if test="fromTime != null">
        from_time,
      </if>
      <if test="toTime != null">
        to_time,
      </if>
      <if test="consumption != null">
        consumption,
      </if>
      <if test="attachmentId != null">
        attachment_id,
      </if>
      <if test="creationTime != null">
        creation_time,
      </if>
      <if test="createUsername != null">
        create_username,
      </if>
      <if test="createUserId != null">
        create_user_id,
      </if>
      <if test="lastUpdateTime != null">
        last_update_time,
      </if>
      <if test="lastUpdateUsername != null">
        last_update_username,
      </if>
      <if test="lastUpdateUserId != null">
        last_update_user_id,
      </if>
      <if test="lastUpdateVersion != null">
        last_update_version,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=VARCHAR},
      </if>
      <if test="headId != null">
        #{headId,jdbcType=VARCHAR},
      </if>
      <if test="type != null">
        #{type,jdbcType=VARCHAR},
      </if>
      <if test="billNo != null">
        #{billNo,jdbcType=VARCHAR},
      </if>
      <if test="fromTime != null">
        #{fromTime,jdbcType=TIMESTAMP},
      </if>
      <if test="toTime != null">
        #{toTime,jdbcType=TIMESTAMP},
      </if>
      <if test="consumption != null">
        #{consumption,jdbcType=DECIMAL},
      </if>
      <if test="attachmentId != null">
        #{attachmentId,jdbcType=VARCHAR},
      </if>
      <if test="creationTime != null">
        #{creationTime,jdbcType=TIMESTAMP},
      </if>
      <if test="createUsername != null">
        #{createUsername,jdbcType=VARCHAR},
      </if>
      <if test="createUserId != null">
        #{createUserId,jdbcType=VARCHAR},
      </if>
      <if test="lastUpdateTime != null">
        #{lastUpdateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="lastUpdateUsername != null">
        #{lastUpdateUsername,jdbcType=VARCHAR},
      </if>
      <if test="lastUpdateUserId != null">
        #{lastUpdateUserId,jdbcType=VARCHAR},
      </if>
      <if test="lastUpdateVersion != null">
        #{lastUpdateVersion,jdbcType=INTEGER},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.csci.susdev.model.AmbientEnergyBillExample" resultType="java.lang.Long">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Mon Oct 09 15:36:35 HKT 2023.
    -->
    select count(*) from t_ambient_energy_bill
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Mon Oct 09 15:36:35 HKT 2023.
    -->
    update t_ambient_energy_bill
    <set>
      <if test="row.id != null">
        id = #{row.id,jdbcType=VARCHAR},
      </if>
      <if test="row.headId != null">
        head_id = #{row.headId,jdbcType=VARCHAR},
      </if>
      <if test="row.type != null">
        type = #{row.type,jdbcType=VARCHAR},
      </if>
      <if test="row.billNo != null">
        bill_no = #{row.billNo,jdbcType=VARCHAR},
      </if>
      <if test="row.fromTime != null">
        from_time = #{row.fromTime,jdbcType=TIMESTAMP},
      </if>
      <if test="row.toTime != null">
        to_time = #{row.toTime,jdbcType=TIMESTAMP},
      </if>
      <if test="row.consumption != null">
        consumption = #{row.consumption,jdbcType=DECIMAL},
      </if>
      <if test="row.attachmentId != null">
        attachment_id = #{row.attachmentId,jdbcType=VARCHAR},
      </if>
      <if test="row.creationTime != null">
        creation_time = #{row.creationTime,jdbcType=TIMESTAMP},
      </if>
      <if test="row.createUsername != null">
        create_username = #{row.createUsername,jdbcType=VARCHAR},
      </if>
      <if test="row.createUserId != null">
        create_user_id = #{row.createUserId,jdbcType=VARCHAR},
      </if>
      <if test="row.lastUpdateTime != null">
        last_update_time = #{row.lastUpdateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="row.lastUpdateUsername != null">
        last_update_username = #{row.lastUpdateUsername,jdbcType=VARCHAR},
      </if>
      <if test="row.lastUpdateUserId != null">
        last_update_user_id = #{row.lastUpdateUserId,jdbcType=VARCHAR},
      </if>
      <if test="row.lastUpdateVersion != null">
        last_update_version = #{row.lastUpdateVersion,jdbcType=INTEGER},
      </if>
    </set>
    <if test="example != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Mon Oct 09 15:36:35 HKT 2023.
    -->
    update t_ambient_energy_bill
    set id = #{row.id,jdbcType=VARCHAR},
      head_id = #{row.headId,jdbcType=VARCHAR},
      type = #{row.type,jdbcType=VARCHAR},
      bill_no = #{row.billNo,jdbcType=VARCHAR},
      from_time = #{row.fromTime,jdbcType=TIMESTAMP},
      to_time = #{row.toTime,jdbcType=TIMESTAMP},
      consumption = #{row.consumption,jdbcType=DECIMAL},
      attachment_id = #{row.attachmentId,jdbcType=VARCHAR},
      creation_time = #{row.creationTime,jdbcType=TIMESTAMP},
      create_username = #{row.createUsername,jdbcType=VARCHAR},
      create_user_id = #{row.createUserId,jdbcType=VARCHAR},
      last_update_time = #{row.lastUpdateTime,jdbcType=TIMESTAMP},
      last_update_username = #{row.lastUpdateUsername,jdbcType=VARCHAR},
      last_update_user_id = #{row.lastUpdateUserId,jdbcType=VARCHAR},
      last_update_version = #{row.lastUpdateVersion,jdbcType=INTEGER}
    <if test="example != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.csci.susdev.model.AmbientEnergyBill">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Mon Oct 09 15:36:35 HKT 2023.
    -->
    update t_ambient_energy_bill
    <set>
      <if test="headId != null">
        head_id = #{headId,jdbcType=VARCHAR},
      </if>
      <if test="type != null">
        type = #{type,jdbcType=VARCHAR},
      </if>
      <if test="billNo != null">
        bill_no = #{billNo,jdbcType=VARCHAR},
      </if>
      <if test="fromTime != null">
        from_time = #{fromTime,jdbcType=TIMESTAMP},
      </if>
      <if test="toTime != null">
        to_time = #{toTime,jdbcType=TIMESTAMP},
      </if>
      <if test="consumption != null">
        consumption = #{consumption,jdbcType=DECIMAL},
      </if>
      <if test="attachmentId != null">
        attachment_id = #{attachmentId,jdbcType=VARCHAR},
      </if>
      <if test="creationTime != null">
        creation_time = #{creationTime,jdbcType=TIMESTAMP},
      </if>
      <if test="createUsername != null">
        create_username = #{createUsername,jdbcType=VARCHAR},
      </if>
      <if test="createUserId != null">
        create_user_id = #{createUserId,jdbcType=VARCHAR},
      </if>
      <if test="lastUpdateTime != null">
        last_update_time = #{lastUpdateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="lastUpdateUsername != null">
        last_update_username = #{lastUpdateUsername,jdbcType=VARCHAR},
      </if>
      <if test="lastUpdateUserId != null">
        last_update_user_id = #{lastUpdateUserId,jdbcType=VARCHAR},
      </if>
      <if test="lastUpdateVersion != null">
        last_update_version = #{lastUpdateVersion,jdbcType=INTEGER},
      </if>
    </set>
    where id = #{id,jdbcType=VARCHAR}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.csci.susdev.model.AmbientEnergyBill">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Mon Oct 09 15:36:35 HKT 2023.
    -->
    update t_ambient_energy_bill
    set head_id = #{headId,jdbcType=VARCHAR},
      type = #{type,jdbcType=VARCHAR},
      bill_no = #{billNo,jdbcType=VARCHAR},
      from_time = #{fromTime,jdbcType=TIMESTAMP},
      to_time = #{toTime,jdbcType=TIMESTAMP},
      consumption = #{consumption,jdbcType=DECIMAL},
      attachment_id = #{attachmentId,jdbcType=VARCHAR},
      creation_time = #{creationTime,jdbcType=TIMESTAMP},
      create_username = #{createUsername,jdbcType=VARCHAR},
      create_user_id = #{createUserId,jdbcType=VARCHAR},
      last_update_time = #{lastUpdateTime,jdbcType=TIMESTAMP},
      last_update_username = #{lastUpdateUsername,jdbcType=VARCHAR},
      last_update_user_id = #{lastUpdateUserId,jdbcType=VARCHAR},
      last_update_version = #{lastUpdateVersion,jdbcType=INTEGER}
    where id = #{id,jdbcType=VARCHAR}
  </update>
</mapper>