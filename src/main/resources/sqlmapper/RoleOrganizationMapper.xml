<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.csci.susdev.mapper.RoleOrganizationMapper">
  <resultMap id="BaseResultMap" type="com.csci.susdev.model.RoleOrganization">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Thu May 08 14:18:18 CST 2025.
    -->
    <id column="id" jdbcType="NVARCHAR" property="id" />
    <result column="role_id" jdbcType="NVARCHAR" property="roleId" />
    <result column="organization_id" jdbcType="NVARCHAR" property="organizationId" />
    <result column="creation_time" jdbcType="TIMESTAMP" property="creationTime" />
    <result column="create_username" jdbcType="NVARCHAR" property="createUsername" />
    <result column="create_user_id" jdbcType="NVARCHAR" property="createUserId" />
    <result column="last_update_time" jdbcType="TIMESTAMP" property="lastUpdateTime" />
    <result column="last_update_username" jdbcType="NVARCHAR" property="lastUpdateUsername" />
    <result column="last_update_user_id" jdbcType="NVARCHAR" property="lastUpdateUserId" />
    <result column="is_deleted" jdbcType="BIT" property="isDeleted" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Thu May 08 14:18:18 CST 2025.
    -->
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Thu May 08 14:18:18 CST 2025.
    -->
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Thu May 08 14:18:18 CST 2025.
    -->
    id, role_id, organization_id, creation_time, create_username, create_user_id, last_update_time, 
    last_update_username, last_update_user_id, is_deleted
  </sql>
  <select id="selectByExample" parameterType="com.csci.susdev.model.RoleOrganizationExample" resultMap="BaseResultMap">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Thu May 08 14:18:18 CST 2025.
    -->
    select
    <if test="distinct">
      distinct
    </if>
    'true' as QUERYID,
    <include refid="Base_Column_List" />
    from t_role_organization
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.String" resultMap="BaseResultMap">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Thu May 08 14:18:18 CST 2025.
    -->
    select 
    <include refid="Base_Column_List" />
    from t_role_organization
    where id = #{id,jdbcType=NVARCHAR}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.String">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Thu May 08 14:18:18 CST 2025.
    -->
    delete from t_role_organization
    where id = #{id,jdbcType=NVARCHAR}
  </delete>
  <delete id="deleteByExample" parameterType="com.csci.susdev.model.RoleOrganizationExample">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Thu May 08 14:18:18 CST 2025.
    -->
    delete from t_role_organization
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.csci.susdev.model.RoleOrganization">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Thu May 08 14:18:18 CST 2025.
    -->
    insert into t_role_organization (id, role_id, organization_id, 
      creation_time, create_username, create_user_id, 
      last_update_time, last_update_username, 
      last_update_user_id, is_deleted)
    values (#{id,jdbcType=NVARCHAR}, #{roleId,jdbcType=NVARCHAR}, #{organizationId,jdbcType=NVARCHAR}, 
      #{creationTime,jdbcType=TIMESTAMP}, #{createUsername,jdbcType=NVARCHAR}, #{createUserId,jdbcType=NVARCHAR}, 
      #{lastUpdateTime,jdbcType=TIMESTAMP}, #{lastUpdateUsername,jdbcType=NVARCHAR}, 
      #{lastUpdateUserId,jdbcType=NVARCHAR}, #{isDeleted,jdbcType=BIT})
  </insert>
  <insert id="insertSelective" parameterType="com.csci.susdev.model.RoleOrganization">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Thu May 08 14:18:18 CST 2025.
    -->
    insert into t_role_organization
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="roleId != null">
        role_id,
      </if>
      <if test="organizationId != null">
        organization_id,
      </if>
      <if test="creationTime != null">
        creation_time,
      </if>
      <if test="createUsername != null">
        create_username,
      </if>
      <if test="createUserId != null">
        create_user_id,
      </if>
      <if test="lastUpdateTime != null">
        last_update_time,
      </if>
      <if test="lastUpdateUsername != null">
        last_update_username,
      </if>
      <if test="lastUpdateUserId != null">
        last_update_user_id,
      </if>
      <if test="isDeleted != null">
        is_deleted,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=NVARCHAR},
      </if>
      <if test="roleId != null">
        #{roleId,jdbcType=NVARCHAR},
      </if>
      <if test="organizationId != null">
        #{organizationId,jdbcType=NVARCHAR},
      </if>
      <if test="creationTime != null">
        #{creationTime,jdbcType=TIMESTAMP},
      </if>
      <if test="createUsername != null">
        #{createUsername,jdbcType=NVARCHAR},
      </if>
      <if test="createUserId != null">
        #{createUserId,jdbcType=NVARCHAR},
      </if>
      <if test="lastUpdateTime != null">
        #{lastUpdateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="lastUpdateUsername != null">
        #{lastUpdateUsername,jdbcType=NVARCHAR},
      </if>
      <if test="lastUpdateUserId != null">
        #{lastUpdateUserId,jdbcType=NVARCHAR},
      </if>
      <if test="isDeleted != null">
        #{isDeleted,jdbcType=BIT},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.csci.susdev.model.RoleOrganizationExample" resultType="java.lang.Long">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Thu May 08 14:18:18 CST 2025.
    -->
    select count(*) from t_role_organization
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Thu May 08 14:18:18 CST 2025.
    -->
    update t_role_organization
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=NVARCHAR},
      </if>
      <if test="record.roleId != null">
        role_id = #{record.roleId,jdbcType=NVARCHAR},
      </if>
      <if test="record.organizationId != null">
        organization_id = #{record.organizationId,jdbcType=NVARCHAR},
      </if>
      <if test="record.creationTime != null">
        creation_time = #{record.creationTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.createUsername != null">
        create_username = #{record.createUsername,jdbcType=NVARCHAR},
      </if>
      <if test="record.createUserId != null">
        create_user_id = #{record.createUserId,jdbcType=NVARCHAR},
      </if>
      <if test="record.lastUpdateTime != null">
        last_update_time = #{record.lastUpdateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.lastUpdateUsername != null">
        last_update_username = #{record.lastUpdateUsername,jdbcType=NVARCHAR},
      </if>
      <if test="record.lastUpdateUserId != null">
        last_update_user_id = #{record.lastUpdateUserId,jdbcType=NVARCHAR},
      </if>
      <if test="record.isDeleted != null">
        is_deleted = #{record.isDeleted,jdbcType=BIT},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Thu May 08 14:18:18 CST 2025.
    -->
    update t_role_organization
    set id = #{record.id,jdbcType=NVARCHAR},
      role_id = #{record.roleId,jdbcType=NVARCHAR},
      organization_id = #{record.organizationId,jdbcType=NVARCHAR},
      creation_time = #{record.creationTime,jdbcType=TIMESTAMP},
      create_username = #{record.createUsername,jdbcType=NVARCHAR},
      create_user_id = #{record.createUserId,jdbcType=NVARCHAR},
      last_update_time = #{record.lastUpdateTime,jdbcType=TIMESTAMP},
      last_update_username = #{record.lastUpdateUsername,jdbcType=NVARCHAR},
      last_update_user_id = #{record.lastUpdateUserId,jdbcType=NVARCHAR},
      is_deleted = #{record.isDeleted,jdbcType=BIT}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.csci.susdev.model.RoleOrganization">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Thu May 08 14:18:18 CST 2025.
    -->
    update t_role_organization
    <set>
      <if test="roleId != null">
        role_id = #{roleId,jdbcType=NVARCHAR},
      </if>
      <if test="organizationId != null">
        organization_id = #{organizationId,jdbcType=NVARCHAR},
      </if>
      <if test="creationTime != null">
        creation_time = #{creationTime,jdbcType=TIMESTAMP},
      </if>
      <if test="createUsername != null">
        create_username = #{createUsername,jdbcType=NVARCHAR},
      </if>
      <if test="createUserId != null">
        create_user_id = #{createUserId,jdbcType=NVARCHAR},
      </if>
      <if test="lastUpdateTime != null">
        last_update_time = #{lastUpdateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="lastUpdateUsername != null">
        last_update_username = #{lastUpdateUsername,jdbcType=NVARCHAR},
      </if>
      <if test="lastUpdateUserId != null">
        last_update_user_id = #{lastUpdateUserId,jdbcType=NVARCHAR},
      </if>
      <if test="isDeleted != null">
        is_deleted = #{isDeleted,jdbcType=BIT},
      </if>
    </set>
    where id = #{id,jdbcType=NVARCHAR}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.csci.susdev.model.RoleOrganization">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Thu May 08 14:18:18 CST 2025.
    -->
    update t_role_organization
    set role_id = #{roleId,jdbcType=NVARCHAR},
      organization_id = #{organizationId,jdbcType=NVARCHAR},
      creation_time = #{creationTime,jdbcType=TIMESTAMP},
      create_username = #{createUsername,jdbcType=NVARCHAR},
      create_user_id = #{createUserId,jdbcType=NVARCHAR},
      last_update_time = #{lastUpdateTime,jdbcType=TIMESTAMP},
      last_update_username = #{lastUpdateUsername,jdbcType=NVARCHAR},
      last_update_user_id = #{lastUpdateUserId,jdbcType=NVARCHAR},
      is_deleted = #{isDeleted,jdbcType=BIT}
    where id = #{id,jdbcType=NVARCHAR}
  </update>
</mapper>