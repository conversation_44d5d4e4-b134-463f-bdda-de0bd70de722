<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.csci.susdev.mapper.MenuMapper">
  <resultMap id="BaseResultMap" type="com.csci.susdev.model.Menu">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Fri Apr 26 10:55:29 HKT 2024.
    -->
    <id column="id" jdbcType="NVARCHAR" property="id" />
    <result column="parent_id" jdbcType="NVARCHAR" property="parentId" />
    <result column="path" jdbcType="NVARCHAR" property="path" />
    <result column="description" jdbcType="NVARCHAR" property="description" />
    <result column="route_icon" jdbcType="NVARCHAR" property="routeIcon" />
    <result column="route_title" jdbcType="NVARCHAR" property="routeTitle" />
    <result column="route_path" jdbcType="NVARCHAR" property="routePath" />
    <result column="route_redirect" jdbcType="NVARCHAR" property="routeRedirect" />
    <result column="route_component" jdbcType="NVARCHAR" property="routeComponent" />
    <result column="seq" jdbcType="INTEGER" property="seq" />
    <result column="is_active" jdbcType="BIT" property="isActive" />
    <result column="creation_time" jdbcType="TIMESTAMP" property="creationTime" />
    <result column="create_username" jdbcType="NVARCHAR" property="createUsername" />
    <result column="create_user_id" jdbcType="NVARCHAR" property="createUserId" />
    <result column="last_update_time" jdbcType="TIMESTAMP" property="lastUpdateTime" />
    <result column="last_update_username" jdbcType="NVARCHAR" property="lastUpdateUsername" />
    <result column="last_update_user_id" jdbcType="NVARCHAR" property="lastUpdateUserId" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Fri Apr 26 10:55:29 HKT 2024.
    -->
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Fri Apr 26 10:55:29 HKT 2024.
    -->
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Fri Apr 26 10:55:29 HKT 2024.
    -->
    id, parent_id, path, description, route_icon, route_title, route_path, route_redirect, 
    route_component, seq, is_active, creation_time, create_username, create_user_id, 
    last_update_time, last_update_username, last_update_user_id
  </sql>
  <select id="selectByExample" parameterType="com.csci.susdev.model.MenuExample" resultMap="BaseResultMap">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Fri Apr 26 10:55:29 HKT 2024.
    -->
    select
    <if test="distinct">
      distinct
    </if>
    'true' as QUERYID,
    <include refid="Base_Column_List" />
    from t_menu
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>

  <select id="selectAllMenuDataByExample" parameterType="java.lang.String" resultType="java.lang.String">
  SELECT description value FROM t_menu WHERE route_path = #{path,jdbcType=NVARCHAR} AND description IS NOT NULL and description &lt;&gt;''
  UNION ALL
  SELECT title value FROM Tzh_Bs_Menu WHERE name = #{path,jdbcType=NVARCHAR} AND title IS NOT NULL AND title &lt;&gt;''
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.String" resultMap="BaseResultMap">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Fri Apr 26 10:55:29 HKT 2024.
    -->
    select 
    <include refid="Base_Column_List" />
    from t_menu
    where id = #{id,jdbcType=NVARCHAR}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.String">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Fri Apr 26 10:55:29 HKT 2024.
    -->
    delete from t_menu
    where id = #{id,jdbcType=NVARCHAR}
  </delete>
  <delete id="deleteByExample" parameterType="com.csci.susdev.model.MenuExample">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Fri Apr 26 10:55:29 HKT 2024.
    -->
    delete from t_menu
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.csci.susdev.model.Menu">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Fri Apr 26 10:55:29 HKT 2024.
    -->
    insert into t_menu (id, parent_id, path, 
      description, route_icon, route_title, 
      route_path, route_redirect, route_component, 
      seq, is_active, creation_time, 
      create_username, create_user_id, last_update_time, 
      last_update_username, last_update_user_id)
    values (#{id,jdbcType=NVARCHAR}, #{parentId,jdbcType=NVARCHAR}, #{path,jdbcType=NVARCHAR}, 
      #{description,jdbcType=NVARCHAR}, #{routeIcon,jdbcType=NVARCHAR}, #{routeTitle,jdbcType=NVARCHAR}, 
      #{routePath,jdbcType=NVARCHAR}, #{routeRedirect,jdbcType=NVARCHAR}, #{routeComponent,jdbcType=NVARCHAR}, 
      #{seq,jdbcType=INTEGER}, #{isActive,jdbcType=BIT}, #{creationTime,jdbcType=TIMESTAMP}, 
      #{createUsername,jdbcType=NVARCHAR}, #{createUserId,jdbcType=NVARCHAR}, #{lastUpdateTime,jdbcType=TIMESTAMP}, 
      #{lastUpdateUsername,jdbcType=NVARCHAR}, #{lastUpdateUserId,jdbcType=NVARCHAR})
  </insert>
  <insert id="insertSelective" parameterType="com.csci.susdev.model.Menu">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Fri Apr 26 10:55:29 HKT 2024.
    -->
    insert into t_menu
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="parentId != null">
        parent_id,
      </if>
      <if test="path != null">
        path,
      </if>
      <if test="description != null">
        description,
      </if>
      <if test="routeIcon != null">
        route_icon,
      </if>
      <if test="routeTitle != null">
        route_title,
      </if>
      <if test="routePath != null">
        route_path,
      </if>
      <if test="routeRedirect != null">
        route_redirect,
      </if>
      <if test="routeComponent != null">
        route_component,
      </if>
      <if test="seq != null">
        seq,
      </if>
      <if test="isActive != null">
        is_active,
      </if>
      <if test="creationTime != null">
        creation_time,
      </if>
      <if test="createUsername != null">
        create_username,
      </if>
      <if test="createUserId != null">
        create_user_id,
      </if>
      <if test="lastUpdateTime != null">
        last_update_time,
      </if>
      <if test="lastUpdateUsername != null">
        last_update_username,
      </if>
      <if test="lastUpdateUserId != null">
        last_update_user_id,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=NVARCHAR},
      </if>
      <if test="parentId != null">
        #{parentId,jdbcType=NVARCHAR},
      </if>
      <if test="path != null">
        #{path,jdbcType=NVARCHAR},
      </if>
      <if test="description != null">
        #{description,jdbcType=NVARCHAR},
      </if>
      <if test="routeIcon != null">
        #{routeIcon,jdbcType=NVARCHAR},
      </if>
      <if test="routeTitle != null">
        #{routeTitle,jdbcType=NVARCHAR},
      </if>
      <if test="routePath != null">
        #{routePath,jdbcType=NVARCHAR},
      </if>
      <if test="routeRedirect != null">
        #{routeRedirect,jdbcType=NVARCHAR},
      </if>
      <if test="routeComponent != null">
        #{routeComponent,jdbcType=NVARCHAR},
      </if>
      <if test="seq != null">
        #{seq,jdbcType=INTEGER},
      </if>
      <if test="isActive != null">
        #{isActive,jdbcType=BIT},
      </if>
      <if test="creationTime != null">
        #{creationTime,jdbcType=TIMESTAMP},
      </if>
      <if test="createUsername != null">
        #{createUsername,jdbcType=NVARCHAR},
      </if>
      <if test="createUserId != null">
        #{createUserId,jdbcType=NVARCHAR},
      </if>
      <if test="lastUpdateTime != null">
        #{lastUpdateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="lastUpdateUsername != null">
        #{lastUpdateUsername,jdbcType=NVARCHAR},
      </if>
      <if test="lastUpdateUserId != null">
        #{lastUpdateUserId,jdbcType=NVARCHAR},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.csci.susdev.model.MenuExample" resultType="java.lang.Long">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Fri Apr 26 10:55:29 HKT 2024.
    -->
    select count(*) from t_menu
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Fri Apr 26 10:55:29 HKT 2024.
    -->
    update t_menu
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=NVARCHAR},
      </if>
      <if test="record.parentId != null">
        parent_id = #{record.parentId,jdbcType=NVARCHAR},
      </if>
      <if test="record.path != null">
        path = #{record.path,jdbcType=NVARCHAR},
      </if>
      <if test="record.description != null">
        description = #{record.description,jdbcType=NVARCHAR},
      </if>
      <if test="record.routeIcon != null">
        route_icon = #{record.routeIcon,jdbcType=NVARCHAR},
      </if>
      <if test="record.routeTitle != null">
        route_title = #{record.routeTitle,jdbcType=NVARCHAR},
      </if>
      <if test="record.routePath != null">
        route_path = #{record.routePath,jdbcType=NVARCHAR},
      </if>
      <if test="record.routeRedirect != null">
        route_redirect = #{record.routeRedirect,jdbcType=NVARCHAR},
      </if>
      <if test="record.routeComponent != null">
        route_component = #{record.routeComponent,jdbcType=NVARCHAR},
      </if>
      <if test="record.seq != null">
        seq = #{record.seq,jdbcType=INTEGER},
      </if>
      <if test="record.isActive != null">
        is_active = #{record.isActive,jdbcType=BIT},
      </if>
      <if test="record.creationTime != null">
        creation_time = #{record.creationTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.createUsername != null">
        create_username = #{record.createUsername,jdbcType=NVARCHAR},
      </if>
      <if test="record.createUserId != null">
        create_user_id = #{record.createUserId,jdbcType=NVARCHAR},
      </if>
      <if test="record.lastUpdateTime != null">
        last_update_time = #{record.lastUpdateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.lastUpdateUsername != null">
        last_update_username = #{record.lastUpdateUsername,jdbcType=NVARCHAR},
      </if>
      <if test="record.lastUpdateUserId != null">
        last_update_user_id = #{record.lastUpdateUserId,jdbcType=NVARCHAR},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Fri Apr 26 10:55:29 HKT 2024.
    -->
    update t_menu
    set id = #{record.id,jdbcType=NVARCHAR},
      parent_id = #{record.parentId,jdbcType=NVARCHAR},
      path = #{record.path,jdbcType=NVARCHAR},
      description = #{record.description,jdbcType=NVARCHAR},
      route_icon = #{record.routeIcon,jdbcType=NVARCHAR},
      route_title = #{record.routeTitle,jdbcType=NVARCHAR},
      route_path = #{record.routePath,jdbcType=NVARCHAR},
      route_redirect = #{record.routeRedirect,jdbcType=NVARCHAR},
      route_component = #{record.routeComponent,jdbcType=NVARCHAR},
      seq = #{record.seq,jdbcType=INTEGER},
      is_active = #{record.isActive,jdbcType=BIT},
      creation_time = #{record.creationTime,jdbcType=TIMESTAMP},
      create_username = #{record.createUsername,jdbcType=NVARCHAR},
      create_user_id = #{record.createUserId,jdbcType=NVARCHAR},
      last_update_time = #{record.lastUpdateTime,jdbcType=TIMESTAMP},
      last_update_username = #{record.lastUpdateUsername,jdbcType=NVARCHAR},
      last_update_user_id = #{record.lastUpdateUserId,jdbcType=NVARCHAR}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.csci.susdev.model.Menu">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Fri Apr 26 10:55:29 HKT 2024.
    -->
    update t_menu
    <set>
      <if test="parentId != null">
        parent_id = #{parentId,jdbcType=NVARCHAR},
      </if>
      <if test="path != null">
        path = #{path,jdbcType=NVARCHAR},
      </if>
      <if test="description != null">
        description = #{description,jdbcType=NVARCHAR},
      </if>
      <if test="routeIcon != null">
        route_icon = #{routeIcon,jdbcType=NVARCHAR},
      </if>
      <if test="routeTitle != null">
        route_title = #{routeTitle,jdbcType=NVARCHAR},
      </if>
      <if test="routePath != null">
        route_path = #{routePath,jdbcType=NVARCHAR},
      </if>
      <if test="routeRedirect != null">
        route_redirect = #{routeRedirect,jdbcType=NVARCHAR},
      </if>
      <if test="routeComponent != null">
        route_component = #{routeComponent,jdbcType=NVARCHAR},
      </if>
      <if test="seq != null">
        seq = #{seq,jdbcType=INTEGER},
      </if>
      <if test="isActive != null">
        is_active = #{isActive,jdbcType=BIT},
      </if>
      <if test="creationTime != null">
        creation_time = #{creationTime,jdbcType=TIMESTAMP},
      </if>
      <if test="createUsername != null">
        create_username = #{createUsername,jdbcType=NVARCHAR},
      </if>
      <if test="createUserId != null">
        create_user_id = #{createUserId,jdbcType=NVARCHAR},
      </if>
      <if test="lastUpdateTime != null">
        last_update_time = #{lastUpdateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="lastUpdateUsername != null">
        last_update_username = #{lastUpdateUsername,jdbcType=NVARCHAR},
      </if>
      <if test="lastUpdateUserId != null">
        last_update_user_id = #{lastUpdateUserId,jdbcType=NVARCHAR},
      </if>
    </set>
    where id = #{id,jdbcType=NVARCHAR}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.csci.susdev.model.Menu">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Fri Apr 26 10:55:29 HKT 2024.
    -->
    update t_menu
    set parent_id = #{parentId,jdbcType=NVARCHAR},
      path = #{path,jdbcType=NVARCHAR},
      description = #{description,jdbcType=NVARCHAR},
      route_icon = #{routeIcon,jdbcType=NVARCHAR},
      route_title = #{routeTitle,jdbcType=NVARCHAR},
      route_path = #{routePath,jdbcType=NVARCHAR},
      route_redirect = #{routeRedirect,jdbcType=NVARCHAR},
      route_component = #{routeComponent,jdbcType=NVARCHAR},
      seq = #{seq,jdbcType=INTEGER},
      is_active = #{isActive,jdbcType=BIT},
      creation_time = #{creationTime,jdbcType=TIMESTAMP},
      create_username = #{createUsername,jdbcType=NVARCHAR},
      create_user_id = #{createUserId,jdbcType=NVARCHAR},
      last_update_time = #{lastUpdateTime,jdbcType=TIMESTAMP},
      last_update_username = #{lastUpdateUsername,jdbcType=NVARCHAR},
      last_update_user_id = #{lastUpdateUserId,jdbcType=NVARCHAR}
    where id = #{id,jdbcType=NVARCHAR}
  </update>
</mapper>