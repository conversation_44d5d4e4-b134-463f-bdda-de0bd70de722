<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.csci.susdev.mapper.EmissionReductionInfoMapper">
  <resultMap id="BaseResultMap" type="com.csci.susdev.model.EmissionReductionInfo">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Thu Jun 20 18:41:32 HKT 2024.
    -->
    <id column="id" jdbcType="NVARCHAR" property="id" />
    <result column="head_id" jdbcType="NVARCHAR" property="headId" />
    <result column="description" jdbcType="NVARCHAR" property="description" />
    <result column="implement_time" jdbcType="TIMESTAMP" property="implementTime" />
    <result column="est_annual_energy_saving" jdbcType="NUMERIC" property="estAnnualEnergySaving" />
    <result column="est_annual_emission_reduction" jdbcType="NUMERIC" property="estAnnualEmissionReduction" />
    <result column="est_annual_energy_saving_last_year" jdbcType="NUMERIC" property="estAnnualEnergySavingLastYear" />
    <result column="est_annual_emission_reduction_last_year" jdbcType="NUMERIC" property="estAnnualEmissionReductionLastYear" />
    <result column="evidence" jdbcType="NVARCHAR" property="evidence" />
    <result column="creation_time" jdbcType="TIMESTAMP" property="creationTime" />
    <result column="create_username" jdbcType="NVARCHAR" property="createUsername" />
    <result column="create_user_id" jdbcType="NVARCHAR" property="createUserId" />
    <result column="last_update_time" jdbcType="TIMESTAMP" property="lastUpdateTime" />
    <result column="last_update_username" jdbcType="NVARCHAR" property="lastUpdateUsername" />
    <result column="last_update_user_id" jdbcType="NVARCHAR" property="lastUpdateUserId" />
    <result column="last_update_version" jdbcType="INTEGER" property="lastUpdateVersion" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Thu Jun 20 18:41:32 HKT 2024.
    -->
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Thu Jun 20 18:41:32 HKT 2024.
    -->
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Thu Jun 20 18:41:32 HKT 2024.
    -->
    id, head_id, description, implement_time, est_annual_energy_saving, est_annual_emission_reduction, 
    est_annual_energy_saving_last_year, est_annual_emission_reduction_last_year, evidence, 
    creation_time, create_username, create_user_id, last_update_time, last_update_username, 
    last_update_user_id, last_update_version
  </sql>
  <select id="selectByExample" parameterType="com.csci.susdev.model.EmissionReductionInfoExample" resultMap="BaseResultMap">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Thu Jun 20 18:41:32 HKT 2024.
    -->
    select
    <if test="distinct">
      distinct
    </if>
    'true' as QUERYID,
    <include refid="Base_Column_List" />
    from t_emission_reduction_info
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.String" resultMap="BaseResultMap">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Thu Jun 20 18:41:32 HKT 2024.
    -->
    select 
    <include refid="Base_Column_List" />
    from t_emission_reduction_info
    where id = #{id,jdbcType=NVARCHAR}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.String">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Thu Jun 20 18:41:32 HKT 2024.
    -->
    delete from t_emission_reduction_info
    where id = #{id,jdbcType=NVARCHAR}
  </delete>
  <delete id="deleteByExample" parameterType="com.csci.susdev.model.EmissionReductionInfoExample">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Thu Jun 20 18:41:32 HKT 2024.
    -->
    delete from t_emission_reduction_info
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.csci.susdev.model.EmissionReductionInfo">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Thu Jun 20 18:41:32 HKT 2024.
    -->
    insert into t_emission_reduction_info (id, head_id, description, 
      implement_time, est_annual_energy_saving, 
      est_annual_emission_reduction, est_annual_energy_saving_last_year, 
      est_annual_emission_reduction_last_year, evidence, 
      creation_time, create_username, create_user_id, 
      last_update_time, last_update_username, 
      last_update_user_id, last_update_version)
    values (#{id,jdbcType=NVARCHAR}, #{headId,jdbcType=NVARCHAR}, #{description,jdbcType=NVARCHAR}, 
      #{implementTime,jdbcType=TIMESTAMP}, #{estAnnualEnergySaving,jdbcType=NUMERIC}, 
      #{estAnnualEmissionReduction,jdbcType=NUMERIC}, #{estAnnualEnergySavingLastYear,jdbcType=NUMERIC}, 
      #{estAnnualEmissionReductionLastYear,jdbcType=NUMERIC}, #{evidence,jdbcType=NVARCHAR}, 
      #{creationTime,jdbcType=TIMESTAMP}, #{createUsername,jdbcType=NVARCHAR}, #{createUserId,jdbcType=NVARCHAR}, 
      #{lastUpdateTime,jdbcType=TIMESTAMP}, #{lastUpdateUsername,jdbcType=NVARCHAR}, 
      #{lastUpdateUserId,jdbcType=NVARCHAR}, #{lastUpdateVersion,jdbcType=INTEGER})
  </insert>
  <insert id="insertSelective" parameterType="com.csci.susdev.model.EmissionReductionInfo">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Thu Jun 20 18:41:32 HKT 2024.
    -->
    insert into t_emission_reduction_info
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="headId != null">
        head_id,
      </if>
      <if test="description != null">
        description,
      </if>
      <if test="implementTime != null">
        implement_time,
      </if>
      <if test="estAnnualEnergySaving != null">
        est_annual_energy_saving,
      </if>
      <if test="estAnnualEmissionReduction != null">
        est_annual_emission_reduction,
      </if>
      <if test="estAnnualEnergySavingLastYear != null">
        est_annual_energy_saving_last_year,
      </if>
      <if test="estAnnualEmissionReductionLastYear != null">
        est_annual_emission_reduction_last_year,
      </if>
      <if test="evidence != null">
        evidence,
      </if>
      <if test="creationTime != null">
        creation_time,
      </if>
      <if test="createUsername != null">
        create_username,
      </if>
      <if test="createUserId != null">
        create_user_id,
      </if>
      <if test="lastUpdateTime != null">
        last_update_time,
      </if>
      <if test="lastUpdateUsername != null">
        last_update_username,
      </if>
      <if test="lastUpdateUserId != null">
        last_update_user_id,
      </if>
      <if test="lastUpdateVersion != null">
        last_update_version,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=NVARCHAR},
      </if>
      <if test="headId != null">
        #{headId,jdbcType=NVARCHAR},
      </if>
      <if test="description != null">
        #{description,jdbcType=NVARCHAR},
      </if>
      <if test="implementTime != null">
        #{implementTime,jdbcType=TIMESTAMP},
      </if>
      <if test="estAnnualEnergySaving != null">
        #{estAnnualEnergySaving,jdbcType=NUMERIC},
      </if>
      <if test="estAnnualEmissionReduction != null">
        #{estAnnualEmissionReduction,jdbcType=NUMERIC},
      </if>
      <if test="estAnnualEnergySavingLastYear != null">
        #{estAnnualEnergySavingLastYear,jdbcType=NUMERIC},
      </if>
      <if test="estAnnualEmissionReductionLastYear != null">
        #{estAnnualEmissionReductionLastYear,jdbcType=NUMERIC},
      </if>
      <if test="evidence != null">
        #{evidence,jdbcType=NVARCHAR},
      </if>
      <if test="creationTime != null">
        #{creationTime,jdbcType=TIMESTAMP},
      </if>
      <if test="createUsername != null">
        #{createUsername,jdbcType=NVARCHAR},
      </if>
      <if test="createUserId != null">
        #{createUserId,jdbcType=NVARCHAR},
      </if>
      <if test="lastUpdateTime != null">
        #{lastUpdateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="lastUpdateUsername != null">
        #{lastUpdateUsername,jdbcType=NVARCHAR},
      </if>
      <if test="lastUpdateUserId != null">
        #{lastUpdateUserId,jdbcType=NVARCHAR},
      </if>
      <if test="lastUpdateVersion != null">
        #{lastUpdateVersion,jdbcType=INTEGER},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.csci.susdev.model.EmissionReductionInfoExample" resultType="java.lang.Long">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Thu Jun 20 18:41:32 HKT 2024.
    -->
    select count(*) from t_emission_reduction_info
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Thu Jun 20 18:41:32 HKT 2024.
    -->
    update t_emission_reduction_info
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=NVARCHAR},
      </if>
      <if test="record.headId != null">
        head_id = #{record.headId,jdbcType=NVARCHAR},
      </if>
      <if test="record.description != null">
        description = #{record.description,jdbcType=NVARCHAR},
      </if>
      <if test="record.implementTime != null">
        implement_time = #{record.implementTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.estAnnualEnergySaving != null">
        est_annual_energy_saving = #{record.estAnnualEnergySaving,jdbcType=NUMERIC},
      </if>
      <if test="record.estAnnualEmissionReduction != null">
        est_annual_emission_reduction = #{record.estAnnualEmissionReduction,jdbcType=NUMERIC},
      </if>
      <if test="record.estAnnualEnergySavingLastYear != null">
        est_annual_energy_saving_last_year = #{record.estAnnualEnergySavingLastYear,jdbcType=NUMERIC},
      </if>
      <if test="record.estAnnualEmissionReductionLastYear != null">
        est_annual_emission_reduction_last_year = #{record.estAnnualEmissionReductionLastYear,jdbcType=NUMERIC},
      </if>
      <if test="record.evidence != null">
        evidence = #{record.evidence,jdbcType=NVARCHAR},
      </if>
      <if test="record.creationTime != null">
        creation_time = #{record.creationTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.createUsername != null">
        create_username = #{record.createUsername,jdbcType=NVARCHAR},
      </if>
      <if test="record.createUserId != null">
        create_user_id = #{record.createUserId,jdbcType=NVARCHAR},
      </if>
      <if test="record.lastUpdateTime != null">
        last_update_time = #{record.lastUpdateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.lastUpdateUsername != null">
        last_update_username = #{record.lastUpdateUsername,jdbcType=NVARCHAR},
      </if>
      <if test="record.lastUpdateUserId != null">
        last_update_user_id = #{record.lastUpdateUserId,jdbcType=NVARCHAR},
      </if>
      <if test="record.lastUpdateVersion != null">
        last_update_version = #{record.lastUpdateVersion,jdbcType=INTEGER},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Thu Jun 20 18:41:32 HKT 2024.
    -->
    update t_emission_reduction_info
    set id = #{record.id,jdbcType=NVARCHAR},
      head_id = #{record.headId,jdbcType=NVARCHAR},
      description = #{record.description,jdbcType=NVARCHAR},
      implement_time = #{record.implementTime,jdbcType=TIMESTAMP},
      est_annual_energy_saving = #{record.estAnnualEnergySaving,jdbcType=NUMERIC},
      est_annual_emission_reduction = #{record.estAnnualEmissionReduction,jdbcType=NUMERIC},
      est_annual_energy_saving_last_year = #{record.estAnnualEnergySavingLastYear,jdbcType=NUMERIC},
      est_annual_emission_reduction_last_year = #{record.estAnnualEmissionReductionLastYear,jdbcType=NUMERIC},
      evidence = #{record.evidence,jdbcType=NVARCHAR},
      creation_time = #{record.creationTime,jdbcType=TIMESTAMP},
      create_username = #{record.createUsername,jdbcType=NVARCHAR},
      create_user_id = #{record.createUserId,jdbcType=NVARCHAR},
      last_update_time = #{record.lastUpdateTime,jdbcType=TIMESTAMP},
      last_update_username = #{record.lastUpdateUsername,jdbcType=NVARCHAR},
      last_update_user_id = #{record.lastUpdateUserId,jdbcType=NVARCHAR},
      last_update_version = #{record.lastUpdateVersion,jdbcType=INTEGER}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.csci.susdev.model.EmissionReductionInfo">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Thu Jun 20 18:41:32 HKT 2024.
    -->
    update t_emission_reduction_info
    <set>
      <if test="headId != null">
        head_id = #{headId,jdbcType=NVARCHAR},
      </if>
      <if test="description != null">
        description = #{description,jdbcType=NVARCHAR},
      </if>
      <if test="implementTime != null">
        implement_time = #{implementTime,jdbcType=TIMESTAMP},
      </if>
      <if test="estAnnualEnergySaving != null">
        est_annual_energy_saving = #{estAnnualEnergySaving,jdbcType=NUMERIC},
      </if>
      <if test="estAnnualEmissionReduction != null">
        est_annual_emission_reduction = #{estAnnualEmissionReduction,jdbcType=NUMERIC},
      </if>
      <if test="estAnnualEnergySavingLastYear != null">
        est_annual_energy_saving_last_year = #{estAnnualEnergySavingLastYear,jdbcType=NUMERIC},
      </if>
      <if test="estAnnualEmissionReductionLastYear != null">
        est_annual_emission_reduction_last_year = #{estAnnualEmissionReductionLastYear,jdbcType=NUMERIC},
      </if>
      <if test="evidence != null">
        evidence = #{evidence,jdbcType=NVARCHAR},
      </if>
      <if test="creationTime != null">
        creation_time = #{creationTime,jdbcType=TIMESTAMP},
      </if>
      <if test="createUsername != null">
        create_username = #{createUsername,jdbcType=NVARCHAR},
      </if>
      <if test="createUserId != null">
        create_user_id = #{createUserId,jdbcType=NVARCHAR},
      </if>
      <if test="lastUpdateTime != null">
        last_update_time = #{lastUpdateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="lastUpdateUsername != null">
        last_update_username = #{lastUpdateUsername,jdbcType=NVARCHAR},
      </if>
      <if test="lastUpdateUserId != null">
        last_update_user_id = #{lastUpdateUserId,jdbcType=NVARCHAR},
      </if>
      <if test="lastUpdateVersion != null">
        last_update_version = #{lastUpdateVersion,jdbcType=INTEGER},
      </if>
    </set>
    where id = #{id,jdbcType=NVARCHAR}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.csci.susdev.model.EmissionReductionInfo">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Thu Jun 20 18:41:32 HKT 2024.
    -->
    update t_emission_reduction_info
    set head_id = #{headId,jdbcType=NVARCHAR},
      description = #{description,jdbcType=NVARCHAR},
      implement_time = #{implementTime,jdbcType=TIMESTAMP},
      est_annual_energy_saving = #{estAnnualEnergySaving,jdbcType=NUMERIC},
      est_annual_emission_reduction = #{estAnnualEmissionReduction,jdbcType=NUMERIC},
      est_annual_energy_saving_last_year = #{estAnnualEnergySavingLastYear,jdbcType=NUMERIC},
      est_annual_emission_reduction_last_year = #{estAnnualEmissionReductionLastYear,jdbcType=NUMERIC},
      evidence = #{evidence,jdbcType=NVARCHAR},
      creation_time = #{creationTime,jdbcType=TIMESTAMP},
      create_username = #{createUsername,jdbcType=NVARCHAR},
      create_user_id = #{createUserId,jdbcType=NVARCHAR},
      last_update_time = #{lastUpdateTime,jdbcType=TIMESTAMP},
      last_update_username = #{lastUpdateUsername,jdbcType=NVARCHAR},
      last_update_user_id = #{lastUpdateUserId,jdbcType=NVARCHAR},
      last_update_version = #{lastUpdateVersion,jdbcType=INTEGER}
    where id = #{id,jdbcType=NVARCHAR}
  </update>
</mapper>