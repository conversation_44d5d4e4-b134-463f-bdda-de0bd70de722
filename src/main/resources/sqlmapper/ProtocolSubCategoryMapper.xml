<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.csci.susdev.mapper.ProtocolSubCategoryMapper">
  <resultMap id="BaseResultMap" type="com.csci.susdev.model.ProtocolSubCategory">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Tue Apr 09 18:07:14 HKT 2024.
    -->
    <id column="id" jdbcType="CHAR" property="id" />
    <result column="category_id" jdbcType="CHAR" property="categoryId" />
    <result column="sub_category_name" jdbcType="NVARCHAR" property="subCategoryName" />
    <result column="sub_category_name_sc" jdbcType="NVARCHAR" property="subCategoryNameSc" />
    <result column="sub_category_name_en" jdbcType="NVARCHAR" property="subCategoryNameEn" />
    <result column="description" jdbcType="NVARCHAR" property="description" />
    <result column="creation_time" jdbcType="TIMESTAMP" property="creationTime" />
    <result column="create_username" jdbcType="NVARCHAR" property="createUsername" />
    <result column="create_user_id" jdbcType="NVARCHAR" property="createUserId" />
    <result column="last_update_time" jdbcType="TIMESTAMP" property="lastUpdateTime" />
    <result column="last_update_username" jdbcType="NVARCHAR" property="lastUpdateUsername" />
    <result column="last_update_user_id" jdbcType="NVARCHAR" property="lastUpdateUserId" />
    <result column="last_update_version" jdbcType="INTEGER" property="lastUpdateVersion" />
    <result column="is_deleted" jdbcType="BIT" property="isDeleted" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Tue Apr 09 18:07:14 HKT 2024.
    -->
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Tue Apr 09 18:07:14 HKT 2024.
    -->
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Tue Apr 09 18:07:14 HKT 2024.
    -->
    id, category_id, sub_category_name, sub_category_name_sc, sub_category_name_en, description, 
    creation_time, create_username, create_user_id, last_update_time, last_update_username, 
    last_update_user_id, last_update_version, is_deleted
  </sql>
  <select id="selectByExample" parameterType="com.csci.susdev.model.ProtocolSubCategoryExample" resultMap="BaseResultMap">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Tue Apr 09 18:07:14 HKT 2024.
    -->
    select
    <if test="distinct">
      distinct
    </if>
    'true' as QUERYID,
    <include refid="Base_Column_List" />
    from t_protocol_sub_category
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.String" resultMap="BaseResultMap">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Tue Apr 09 18:07:14 HKT 2024.
    -->
    select 
    <include refid="Base_Column_List" />
    from t_protocol_sub_category
    where id = #{id,jdbcType=CHAR}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.String">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Tue Apr 09 18:07:14 HKT 2024.
    -->
    delete from t_protocol_sub_category
    where id = #{id,jdbcType=CHAR}
  </delete>
  <delete id="deleteByExample" parameterType="com.csci.susdev.model.ProtocolSubCategoryExample">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Tue Apr 09 18:07:14 HKT 2024.
    -->
    delete from t_protocol_sub_category
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.csci.susdev.model.ProtocolSubCategory">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Tue Apr 09 18:07:14 HKT 2024.
    -->
    insert into t_protocol_sub_category (id, category_id, sub_category_name, 
      sub_category_name_sc, sub_category_name_en, 
      description, creation_time, create_username, 
      create_user_id, last_update_time, last_update_username, 
      last_update_user_id, last_update_version, is_deleted
      )
    values (#{id,jdbcType=CHAR}, #{categoryId,jdbcType=CHAR}, #{subCategoryName,jdbcType=NVARCHAR}, 
      #{subCategoryNameSc,jdbcType=NVARCHAR}, #{subCategoryNameEn,jdbcType=NVARCHAR}, 
      #{description,jdbcType=NVARCHAR}, #{creationTime,jdbcType=TIMESTAMP}, #{createUsername,jdbcType=NVARCHAR}, 
      #{createUserId,jdbcType=NVARCHAR}, #{lastUpdateTime,jdbcType=TIMESTAMP}, #{lastUpdateUsername,jdbcType=NVARCHAR}, 
      #{lastUpdateUserId,jdbcType=NVARCHAR}, #{lastUpdateVersion,jdbcType=INTEGER}, #{isDeleted,jdbcType=BIT}
      )
  </insert>
  <insert id="insertSelective" parameterType="com.csci.susdev.model.ProtocolSubCategory">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Tue Apr 09 18:07:14 HKT 2024.
    -->
    insert into t_protocol_sub_category
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="categoryId != null">
        category_id,
      </if>
      <if test="subCategoryName != null">
        sub_category_name,
      </if>
      <if test="subCategoryNameSc != null">
        sub_category_name_sc,
      </if>
      <if test="subCategoryNameEn != null">
        sub_category_name_en,
      </if>
      <if test="description != null">
        description,
      </if>
      <if test="creationTime != null">
        creation_time,
      </if>
      <if test="createUsername != null">
        create_username,
      </if>
      <if test="createUserId != null">
        create_user_id,
      </if>
      <if test="lastUpdateTime != null">
        last_update_time,
      </if>
      <if test="lastUpdateUsername != null">
        last_update_username,
      </if>
      <if test="lastUpdateUserId != null">
        last_update_user_id,
      </if>
      <if test="lastUpdateVersion != null">
        last_update_version,
      </if>
      <if test="isDeleted != null">
        is_deleted,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=CHAR},
      </if>
      <if test="categoryId != null">
        #{categoryId,jdbcType=CHAR},
      </if>
      <if test="subCategoryName != null">
        #{subCategoryName,jdbcType=NVARCHAR},
      </if>
      <if test="subCategoryNameSc != null">
        #{subCategoryNameSc,jdbcType=NVARCHAR},
      </if>
      <if test="subCategoryNameEn != null">
        #{subCategoryNameEn,jdbcType=NVARCHAR},
      </if>
      <if test="description != null">
        #{description,jdbcType=NVARCHAR},
      </if>
      <if test="creationTime != null">
        #{creationTime,jdbcType=TIMESTAMP},
      </if>
      <if test="createUsername != null">
        #{createUsername,jdbcType=NVARCHAR},
      </if>
      <if test="createUserId != null">
        #{createUserId,jdbcType=NVARCHAR},
      </if>
      <if test="lastUpdateTime != null">
        #{lastUpdateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="lastUpdateUsername != null">
        #{lastUpdateUsername,jdbcType=NVARCHAR},
      </if>
      <if test="lastUpdateUserId != null">
        #{lastUpdateUserId,jdbcType=NVARCHAR},
      </if>
      <if test="lastUpdateVersion != null">
        #{lastUpdateVersion,jdbcType=INTEGER},
      </if>
      <if test="isDeleted != null">
        #{isDeleted,jdbcType=BIT},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.csci.susdev.model.ProtocolSubCategoryExample" resultType="java.lang.Long">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Tue Apr 09 18:07:14 HKT 2024.
    -->
    select count(*) from t_protocol_sub_category
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Tue Apr 09 18:07:14 HKT 2024.
    -->
    update t_protocol_sub_category
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=CHAR},
      </if>
      <if test="record.categoryId != null">
        category_id = #{record.categoryId,jdbcType=CHAR},
      </if>
      <if test="record.subCategoryName != null">
        sub_category_name = #{record.subCategoryName,jdbcType=NVARCHAR},
      </if>
      <if test="record.subCategoryNameSc != null">
        sub_category_name_sc = #{record.subCategoryNameSc,jdbcType=NVARCHAR},
      </if>
      <if test="record.subCategoryNameEn != null">
        sub_category_name_en = #{record.subCategoryNameEn,jdbcType=NVARCHAR},
      </if>
      <if test="record.description != null">
        description = #{record.description,jdbcType=NVARCHAR},
      </if>
      <if test="record.creationTime != null">
        creation_time = #{record.creationTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.createUsername != null">
        create_username = #{record.createUsername,jdbcType=NVARCHAR},
      </if>
      <if test="record.createUserId != null">
        create_user_id = #{record.createUserId,jdbcType=NVARCHAR},
      </if>
      <if test="record.lastUpdateTime != null">
        last_update_time = #{record.lastUpdateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.lastUpdateUsername != null">
        last_update_username = #{record.lastUpdateUsername,jdbcType=NVARCHAR},
      </if>
      <if test="record.lastUpdateUserId != null">
        last_update_user_id = #{record.lastUpdateUserId,jdbcType=NVARCHAR},
      </if>
      <if test="record.lastUpdateVersion != null">
        last_update_version = #{record.lastUpdateVersion,jdbcType=INTEGER},
      </if>
      <if test="record.isDeleted != null">
        is_deleted = #{record.isDeleted,jdbcType=BIT},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Tue Apr 09 18:07:14 HKT 2024.
    -->
    update t_protocol_sub_category
    set id = #{record.id,jdbcType=CHAR},
      category_id = #{record.categoryId,jdbcType=CHAR},
      sub_category_name = #{record.subCategoryName,jdbcType=NVARCHAR},
      sub_category_name_sc = #{record.subCategoryNameSc,jdbcType=NVARCHAR},
      sub_category_name_en = #{record.subCategoryNameEn,jdbcType=NVARCHAR},
      description = #{record.description,jdbcType=NVARCHAR},
      creation_time = #{record.creationTime,jdbcType=TIMESTAMP},
      create_username = #{record.createUsername,jdbcType=NVARCHAR},
      create_user_id = #{record.createUserId,jdbcType=NVARCHAR},
      last_update_time = #{record.lastUpdateTime,jdbcType=TIMESTAMP},
      last_update_username = #{record.lastUpdateUsername,jdbcType=NVARCHAR},
      last_update_user_id = #{record.lastUpdateUserId,jdbcType=NVARCHAR},
      last_update_version = #{record.lastUpdateVersion,jdbcType=INTEGER},
      is_deleted = #{record.isDeleted,jdbcType=BIT}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.csci.susdev.model.ProtocolSubCategory">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Tue Apr 09 18:07:14 HKT 2024.
    -->
    update t_protocol_sub_category
    <set>
      <if test="categoryId != null">
        category_id = #{categoryId,jdbcType=CHAR},
      </if>
      <if test="subCategoryName != null">
        sub_category_name = #{subCategoryName,jdbcType=NVARCHAR},
      </if>
      <if test="subCategoryNameSc != null">
        sub_category_name_sc = #{subCategoryNameSc,jdbcType=NVARCHAR},
      </if>
      <if test="subCategoryNameEn != null">
        sub_category_name_en = #{subCategoryNameEn,jdbcType=NVARCHAR},
      </if>
      <if test="description != null">
        description = #{description,jdbcType=NVARCHAR},
      </if>
      <if test="creationTime != null">
        creation_time = #{creationTime,jdbcType=TIMESTAMP},
      </if>
      <if test="createUsername != null">
        create_username = #{createUsername,jdbcType=NVARCHAR},
      </if>
      <if test="createUserId != null">
        create_user_id = #{createUserId,jdbcType=NVARCHAR},
      </if>
      <if test="lastUpdateTime != null">
        last_update_time = #{lastUpdateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="lastUpdateUsername != null">
        last_update_username = #{lastUpdateUsername,jdbcType=NVARCHAR},
      </if>
      <if test="lastUpdateUserId != null">
        last_update_user_id = #{lastUpdateUserId,jdbcType=NVARCHAR},
      </if>
      <if test="lastUpdateVersion != null">
        last_update_version = #{lastUpdateVersion,jdbcType=INTEGER},
      </if>
      <if test="isDeleted != null">
        is_deleted = #{isDeleted,jdbcType=BIT},
      </if>
    </set>
    where id = #{id,jdbcType=CHAR}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.csci.susdev.model.ProtocolSubCategory">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Tue Apr 09 18:07:14 HKT 2024.
    -->
    update t_protocol_sub_category
    set category_id = #{categoryId,jdbcType=CHAR},
      sub_category_name = #{subCategoryName,jdbcType=NVARCHAR},
      sub_category_name_sc = #{subCategoryNameSc,jdbcType=NVARCHAR},
      sub_category_name_en = #{subCategoryNameEn,jdbcType=NVARCHAR},
      description = #{description,jdbcType=NVARCHAR},
      creation_time = #{creationTime,jdbcType=TIMESTAMP},
      create_username = #{createUsername,jdbcType=NVARCHAR},
      create_user_id = #{createUserId,jdbcType=NVARCHAR},
      last_update_time = #{lastUpdateTime,jdbcType=TIMESTAMP},
      last_update_username = #{lastUpdateUsername,jdbcType=NVARCHAR},
      last_update_user_id = #{lastUpdateUserId,jdbcType=NVARCHAR},
      last_update_version = #{lastUpdateVersion,jdbcType=INTEGER},
      is_deleted = #{isDeleted,jdbcType=BIT}
    where id = #{id,jdbcType=CHAR}
  </update>
</mapper>