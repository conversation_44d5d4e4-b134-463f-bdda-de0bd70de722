<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.csci.susdev.mapper.SubmissionMonthConfigMapper">
  <resultMap id="BaseResultMap" type="com.csci.susdev.model.SubmissionMonthConfig">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Thu Apr 18 11:23:09 HKT 2024.
    -->
    <id column="id" jdbcType="CHAR" property="id" />
    <result column="year" jdbcType="INTEGER" property="year" />
    <result column="month1" jdbcType="BIT" property="month1" />
    <result column="month2" jdbcType="BIT" property="month2" />
    <result column="month3" jdbcType="BIT" property="month3" />
    <result column="month4" jdbcType="BIT" property="month4" />
    <result column="month5" jdbcType="BIT" property="month5" />
    <result column="month6" jdbcType="BIT" property="month6" />
    <result column="month7" jdbcType="BIT" property="month7" />
    <result column="month8" jdbcType="BIT" property="month8" />
    <result column="month9" jdbcType="BIT" property="month9" />
    <result column="month10" jdbcType="BIT" property="month10" />
    <result column="month11" jdbcType="BIT" property="month11" />
    <result column="month12" jdbcType="BIT" property="month12" />
    <result column="creation_time" jdbcType="TIMESTAMP" property="creationTime" />
    <result column="create_username" jdbcType="NVARCHAR" property="createUsername" />
    <result column="create_user_id" jdbcType="NVARCHAR" property="createUserId" />
    <result column="last_update_time" jdbcType="TIMESTAMP" property="lastUpdateTime" />
    <result column="last_update_username" jdbcType="NVARCHAR" property="lastUpdateUsername" />
    <result column="last_update_user_id" jdbcType="NVARCHAR" property="lastUpdateUserId" />
    <result column="last_update_version" jdbcType="INTEGER" property="lastUpdateVersion" />
    <result column="is_deleted" jdbcType="BIT" property="isDeleted" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Thu Apr 18 11:23:09 HKT 2024.
    -->
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Thu Apr 18 11:23:09 HKT 2024.
    -->
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Thu Apr 18 11:23:09 HKT 2024.
    -->
    id, year, month1, month2, month3, month4, month5, month6, month7, month8, month9, 
    month10, month11, month12, creation_time, create_username, create_user_id, last_update_time, 
    last_update_username, last_update_user_id, last_update_version, is_deleted
  </sql>
  <select id="selectByExample" parameterType="com.csci.susdev.model.SubmissionMonthConfigExample" resultMap="BaseResultMap">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Thu Apr 18 11:23:09 HKT 2024.
    -->
    select
    <if test="distinct">
      distinct
    </if>
    'true' as QUERYID,
    <include refid="Base_Column_List" />
    from t_submission_month_config
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.String" resultMap="BaseResultMap">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Thu Apr 18 11:23:09 HKT 2024.
    -->
    select 
    <include refid="Base_Column_List" />
    from t_submission_month_config
    where id = #{id,jdbcType=CHAR}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.String">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Thu Apr 18 11:23:09 HKT 2024.
    -->
    delete from t_submission_month_config
    where id = #{id,jdbcType=CHAR}
  </delete>
  <delete id="deleteByExample" parameterType="com.csci.susdev.model.SubmissionMonthConfigExample">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Thu Apr 18 11:23:09 HKT 2024.
    -->
    delete from t_submission_month_config
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.csci.susdev.model.SubmissionMonthConfig">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Thu Apr 18 11:23:09 HKT 2024.
    -->
    insert into t_submission_month_config (id, year, month1, month2, 
      month3, month4, month5, month6, 
      month7, month8, month9, month10, 
      month11, month12, creation_time, 
      create_username, create_user_id, last_update_time, 
      last_update_username, last_update_user_id, 
      last_update_version, is_deleted)
    values (#{id,jdbcType=CHAR}, #{year,jdbcType=INTEGER}, #{month1,jdbcType=BIT}, #{month2,jdbcType=BIT}, 
      #{month3,jdbcType=BIT}, #{month4,jdbcType=BIT}, #{month5,jdbcType=BIT}, #{month6,jdbcType=BIT}, 
      #{month7,jdbcType=BIT}, #{month8,jdbcType=BIT}, #{month9,jdbcType=BIT}, #{month10,jdbcType=BIT}, 
      #{month11,jdbcType=BIT}, #{month12,jdbcType=BIT}, #{creationTime,jdbcType=TIMESTAMP}, 
      #{createUsername,jdbcType=NVARCHAR}, #{createUserId,jdbcType=NVARCHAR}, #{lastUpdateTime,jdbcType=TIMESTAMP}, 
      #{lastUpdateUsername,jdbcType=NVARCHAR}, #{lastUpdateUserId,jdbcType=NVARCHAR}, 
      #{lastUpdateVersion,jdbcType=INTEGER}, #{isDeleted,jdbcType=BIT})
  </insert>
  <insert id="insertSelective" parameterType="com.csci.susdev.model.SubmissionMonthConfig">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Thu Apr 18 11:23:09 HKT 2024.
    -->
    insert into t_submission_month_config
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="year != null">
        year,
      </if>
      <if test="month1 != null">
        month1,
      </if>
      <if test="month2 != null">
        month2,
      </if>
      <if test="month3 != null">
        month3,
      </if>
      <if test="month4 != null">
        month4,
      </if>
      <if test="month5 != null">
        month5,
      </if>
      <if test="month6 != null">
        month6,
      </if>
      <if test="month7 != null">
        month7,
      </if>
      <if test="month8 != null">
        month8,
      </if>
      <if test="month9 != null">
        month9,
      </if>
      <if test="month10 != null">
        month10,
      </if>
      <if test="month11 != null">
        month11,
      </if>
      <if test="month12 != null">
        month12,
      </if>
      <if test="creationTime != null">
        creation_time,
      </if>
      <if test="createUsername != null">
        create_username,
      </if>
      <if test="createUserId != null">
        create_user_id,
      </if>
      <if test="lastUpdateTime != null">
        last_update_time,
      </if>
      <if test="lastUpdateUsername != null">
        last_update_username,
      </if>
      <if test="lastUpdateUserId != null">
        last_update_user_id,
      </if>
      <if test="lastUpdateVersion != null">
        last_update_version,
      </if>
      <if test="isDeleted != null">
        is_deleted,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=CHAR},
      </if>
      <if test="year != null">
        #{year,jdbcType=INTEGER},
      </if>
      <if test="month1 != null">
        #{month1,jdbcType=BIT},
      </if>
      <if test="month2 != null">
        #{month2,jdbcType=BIT},
      </if>
      <if test="month3 != null">
        #{month3,jdbcType=BIT},
      </if>
      <if test="month4 != null">
        #{month4,jdbcType=BIT},
      </if>
      <if test="month5 != null">
        #{month5,jdbcType=BIT},
      </if>
      <if test="month6 != null">
        #{month6,jdbcType=BIT},
      </if>
      <if test="month7 != null">
        #{month7,jdbcType=BIT},
      </if>
      <if test="month8 != null">
        #{month8,jdbcType=BIT},
      </if>
      <if test="month9 != null">
        #{month9,jdbcType=BIT},
      </if>
      <if test="month10 != null">
        #{month10,jdbcType=BIT},
      </if>
      <if test="month11 != null">
        #{month11,jdbcType=BIT},
      </if>
      <if test="month12 != null">
        #{month12,jdbcType=BIT},
      </if>
      <if test="creationTime != null">
        #{creationTime,jdbcType=TIMESTAMP},
      </if>
      <if test="createUsername != null">
        #{createUsername,jdbcType=NVARCHAR},
      </if>
      <if test="createUserId != null">
        #{createUserId,jdbcType=NVARCHAR},
      </if>
      <if test="lastUpdateTime != null">
        #{lastUpdateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="lastUpdateUsername != null">
        #{lastUpdateUsername,jdbcType=NVARCHAR},
      </if>
      <if test="lastUpdateUserId != null">
        #{lastUpdateUserId,jdbcType=NVARCHAR},
      </if>
      <if test="lastUpdateVersion != null">
        #{lastUpdateVersion,jdbcType=INTEGER},
      </if>
      <if test="isDeleted != null">
        #{isDeleted,jdbcType=BIT},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.csci.susdev.model.SubmissionMonthConfigExample" resultType="java.lang.Long">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Thu Apr 18 11:23:09 HKT 2024.
    -->
    select count(*) from t_submission_month_config
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Thu Apr 18 11:23:09 HKT 2024.
    -->
    update t_submission_month_config
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=CHAR},
      </if>
      <if test="record.year != null">
        year = #{record.year,jdbcType=INTEGER},
      </if>
      <if test="record.month1 != null">
        month1 = #{record.month1,jdbcType=BIT},
      </if>
      <if test="record.month2 != null">
        month2 = #{record.month2,jdbcType=BIT},
      </if>
      <if test="record.month3 != null">
        month3 = #{record.month3,jdbcType=BIT},
      </if>
      <if test="record.month4 != null">
        month4 = #{record.month4,jdbcType=BIT},
      </if>
      <if test="record.month5 != null">
        month5 = #{record.month5,jdbcType=BIT},
      </if>
      <if test="record.month6 != null">
        month6 = #{record.month6,jdbcType=BIT},
      </if>
      <if test="record.month7 != null">
        month7 = #{record.month7,jdbcType=BIT},
      </if>
      <if test="record.month8 != null">
        month8 = #{record.month8,jdbcType=BIT},
      </if>
      <if test="record.month9 != null">
        month9 = #{record.month9,jdbcType=BIT},
      </if>
      <if test="record.month10 != null">
        month10 = #{record.month10,jdbcType=BIT},
      </if>
      <if test="record.month11 != null">
        month11 = #{record.month11,jdbcType=BIT},
      </if>
      <if test="record.month12 != null">
        month12 = #{record.month12,jdbcType=BIT},
      </if>
      <if test="record.creationTime != null">
        creation_time = #{record.creationTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.createUsername != null">
        create_username = #{record.createUsername,jdbcType=NVARCHAR},
      </if>
      <if test="record.createUserId != null">
        create_user_id = #{record.createUserId,jdbcType=NVARCHAR},
      </if>
      <if test="record.lastUpdateTime != null">
        last_update_time = #{record.lastUpdateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.lastUpdateUsername != null">
        last_update_username = #{record.lastUpdateUsername,jdbcType=NVARCHAR},
      </if>
      <if test="record.lastUpdateUserId != null">
        last_update_user_id = #{record.lastUpdateUserId,jdbcType=NVARCHAR},
      </if>
      <if test="record.lastUpdateVersion != null">
        last_update_version = #{record.lastUpdateVersion,jdbcType=INTEGER},
      </if>
      <if test="record.isDeleted != null">
        is_deleted = #{record.isDeleted,jdbcType=BIT},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Thu Apr 18 11:23:09 HKT 2024.
    -->
    update t_submission_month_config
    set id = #{record.id,jdbcType=CHAR},
      year = #{record.year,jdbcType=INTEGER},
      month1 = #{record.month1,jdbcType=BIT},
      month2 = #{record.month2,jdbcType=BIT},
      month3 = #{record.month3,jdbcType=BIT},
      month4 = #{record.month4,jdbcType=BIT},
      month5 = #{record.month5,jdbcType=BIT},
      month6 = #{record.month6,jdbcType=BIT},
      month7 = #{record.month7,jdbcType=BIT},
      month8 = #{record.month8,jdbcType=BIT},
      month9 = #{record.month9,jdbcType=BIT},
      month10 = #{record.month10,jdbcType=BIT},
      month11 = #{record.month11,jdbcType=BIT},
      month12 = #{record.month12,jdbcType=BIT},
      creation_time = #{record.creationTime,jdbcType=TIMESTAMP},
      create_username = #{record.createUsername,jdbcType=NVARCHAR},
      create_user_id = #{record.createUserId,jdbcType=NVARCHAR},
      last_update_time = #{record.lastUpdateTime,jdbcType=TIMESTAMP},
      last_update_username = #{record.lastUpdateUsername,jdbcType=NVARCHAR},
      last_update_user_id = #{record.lastUpdateUserId,jdbcType=NVARCHAR},
      last_update_version = #{record.lastUpdateVersion,jdbcType=INTEGER},
      is_deleted = #{record.isDeleted,jdbcType=BIT}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.csci.susdev.model.SubmissionMonthConfig">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Thu Apr 18 11:23:09 HKT 2024.
    -->
    update t_submission_month_config
    <set>
      <if test="year != null">
        year = #{year,jdbcType=INTEGER},
      </if>
      <if test="month1 != null">
        month1 = #{month1,jdbcType=BIT},
      </if>
      <if test="month2 != null">
        month2 = #{month2,jdbcType=BIT},
      </if>
      <if test="month3 != null">
        month3 = #{month3,jdbcType=BIT},
      </if>
      <if test="month4 != null">
        month4 = #{month4,jdbcType=BIT},
      </if>
      <if test="month5 != null">
        month5 = #{month5,jdbcType=BIT},
      </if>
      <if test="month6 != null">
        month6 = #{month6,jdbcType=BIT},
      </if>
      <if test="month7 != null">
        month7 = #{month7,jdbcType=BIT},
      </if>
      <if test="month8 != null">
        month8 = #{month8,jdbcType=BIT},
      </if>
      <if test="month9 != null">
        month9 = #{month9,jdbcType=BIT},
      </if>
      <if test="month10 != null">
        month10 = #{month10,jdbcType=BIT},
      </if>
      <if test="month11 != null">
        month11 = #{month11,jdbcType=BIT},
      </if>
      <if test="month12 != null">
        month12 = #{month12,jdbcType=BIT},
      </if>
      <if test="creationTime != null">
        creation_time = #{creationTime,jdbcType=TIMESTAMP},
      </if>
      <if test="createUsername != null">
        create_username = #{createUsername,jdbcType=NVARCHAR},
      </if>
      <if test="createUserId != null">
        create_user_id = #{createUserId,jdbcType=NVARCHAR},
      </if>
      <if test="lastUpdateTime != null">
        last_update_time = #{lastUpdateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="lastUpdateUsername != null">
        last_update_username = #{lastUpdateUsername,jdbcType=NVARCHAR},
      </if>
      <if test="lastUpdateUserId != null">
        last_update_user_id = #{lastUpdateUserId,jdbcType=NVARCHAR},
      </if>
      <if test="lastUpdateVersion != null">
        last_update_version = #{lastUpdateVersion,jdbcType=INTEGER},
      </if>
      <if test="isDeleted != null">
        is_deleted = #{isDeleted,jdbcType=BIT},
      </if>
    </set>
    where id = #{id,jdbcType=CHAR}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.csci.susdev.model.SubmissionMonthConfig">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Thu Apr 18 11:23:09 HKT 2024.
    -->
    update t_submission_month_config
    set year = #{year,jdbcType=INTEGER},
      month1 = #{month1,jdbcType=BIT},
      month2 = #{month2,jdbcType=BIT},
      month3 = #{month3,jdbcType=BIT},
      month4 = #{month4,jdbcType=BIT},
      month5 = #{month5,jdbcType=BIT},
      month6 = #{month6,jdbcType=BIT},
      month7 = #{month7,jdbcType=BIT},
      month8 = #{month8,jdbcType=BIT},
      month9 = #{month9,jdbcType=BIT},
      month10 = #{month10,jdbcType=BIT},
      month11 = #{month11,jdbcType=BIT},
      month12 = #{month12,jdbcType=BIT},
      creation_time = #{creationTime,jdbcType=TIMESTAMP},
      create_username = #{createUsername,jdbcType=NVARCHAR},
      create_user_id = #{createUserId,jdbcType=NVARCHAR},
      last_update_time = #{lastUpdateTime,jdbcType=TIMESTAMP},
      last_update_username = #{lastUpdateUsername,jdbcType=NVARCHAR},
      last_update_user_id = #{lastUpdateUserId,jdbcType=NVARCHAR},
      last_update_version = #{lastUpdateVersion,jdbcType=INTEGER},
      is_deleted = #{isDeleted,jdbcType=BIT}
    where id = #{id,jdbcType=CHAR}
  </update>
</mapper>