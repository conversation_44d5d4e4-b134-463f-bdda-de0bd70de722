<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.csci.susdev.mapper.FcCarbonFactorHkMapper">
  <resultMap id="BaseResultMap" type="com.csci.susdev.model.FcCarbonFactorHk">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Mon Apr 08 13:02:25 HKT 2024.
    -->
    <id column="id" jdbcType="CHAR" property="id" />
    <result column="chinese_name" jdbcType="NVARCHAR" property="chineseName" />
    <result column="material_attribute" jdbcType="NVARCHAR" property="materialAttribute" />
    <result column="supplier" jdbcType="NVARCHAR" property="supplier" />
    <result column="production_process" jdbcType="NVARCHAR" property="productionProcess" />
    <result column="material_belongs_to" jdbcType="NVARCHAR" property="materialBelongsTo" />
    <result column="unit" jdbcType="NVARCHAR" property="unit" />
    <result column="carbon_factor" jdbcType="NUMERIC" property="carbonFactor" />
    <result column="carbon_factor_unit" jdbcType="NVARCHAR" property="carbonFactorUnit" />
    <result column="description" jdbcType="NVARCHAR" property="description" />
    <result column="source_description" jdbcType="NVARCHAR" property="sourceDescription" />
    <result column="creation_time" jdbcType="TIMESTAMP" property="creationTime" />
    <result column="create_username" jdbcType="NVARCHAR" property="createUsername" />
    <result column="create_user_id" jdbcType="CHAR" property="createUserId" />
    <result column="last_update_time" jdbcType="TIMESTAMP" property="lastUpdateTime" />
    <result column="last_update_username" jdbcType="NVARCHAR" property="lastUpdateUsername" />
    <result column="last_update_user_id" jdbcType="CHAR" property="lastUpdateUserId" />
    <result column="last_update_version" jdbcType="INTEGER" property="lastUpdateVersion" />
    <result column="is_deleted" jdbcType="BIT" property="isDeleted" />
    <result column="record_year" jdbcType="INTEGER" property="recordYear" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Mon Apr 08 13:02:25 HKT 2024.
    -->
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Mon Apr 08 13:02:25 HKT 2024.
    -->
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Mon Apr 08 13:02:25 HKT 2024.
    -->
    id, chinese_name, material_attribute, supplier, production_process, material_belongs_to, 
    unit, carbon_factor, carbon_factor_unit, description, source_description, creation_time, 
    create_username, create_user_id, last_update_time, last_update_username, last_update_user_id, 
    last_update_version, is_deleted, record_year
  </sql>
  <select id="selectByExample" parameterType="com.csci.susdev.model.FcCarbonFactorHkExample" resultMap="BaseResultMap">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Mon Apr 08 13:02:25 HKT 2024.
    -->
    select
    <if test="distinct">
      distinct
    </if>
    'true' as QUERYID,
    <include refid="Base_Column_List" />
    from t_fc_carbon_factor_hk
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.String" resultMap="BaseResultMap">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Mon Apr 08 13:02:25 HKT 2024.
    -->
    select 
    <include refid="Base_Column_List" />
    from t_fc_carbon_factor_hk
    where id = #{id,jdbcType=CHAR}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.String">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Mon Apr 08 13:02:25 HKT 2024.
    -->
    delete from t_fc_carbon_factor_hk
    where id = #{id,jdbcType=CHAR}
  </delete>
  <delete id="deleteByExample" parameterType="com.csci.susdev.model.FcCarbonFactorHkExample">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Mon Apr 08 13:02:25 HKT 2024.
    -->
    delete from t_fc_carbon_factor_hk
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.csci.susdev.model.FcCarbonFactorHk">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Mon Apr 08 13:02:25 HKT 2024.
    -->
    insert into t_fc_carbon_factor_hk (id, chinese_name, material_attribute, 
      supplier, production_process, material_belongs_to, 
      unit, carbon_factor, carbon_factor_unit, 
      description, source_description, creation_time, 
      create_username, create_user_id, last_update_time, 
      last_update_username, last_update_user_id, last_update_version, 
      is_deleted, record_year)
    values (#{id,jdbcType=CHAR}, #{chineseName,jdbcType=NVARCHAR}, #{materialAttribute,jdbcType=NVARCHAR}, 
      #{supplier,jdbcType=NVARCHAR}, #{productionProcess,jdbcType=NVARCHAR}, #{materialBelongsTo,jdbcType=NVARCHAR}, 
      #{unit,jdbcType=NVARCHAR}, #{carbonFactor,jdbcType=NUMERIC}, #{carbonFactorUnit,jdbcType=NVARCHAR}, 
      #{description,jdbcType=NVARCHAR}, #{sourceDescription,jdbcType=NVARCHAR}, #{creationTime,jdbcType=TIMESTAMP}, 
      #{createUsername,jdbcType=NVARCHAR}, #{createUserId,jdbcType=CHAR}, #{lastUpdateTime,jdbcType=TIMESTAMP}, 
      #{lastUpdateUsername,jdbcType=NVARCHAR}, #{lastUpdateUserId,jdbcType=CHAR}, #{lastUpdateVersion,jdbcType=INTEGER}, 
      #{isDeleted,jdbcType=BIT}, #{recordYear,jdbcType=INTEGER})
  </insert>
  <insert id="insertSelective" parameterType="com.csci.susdev.model.FcCarbonFactorHk">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Mon Apr 08 13:02:25 HKT 2024.
    -->
    insert into t_fc_carbon_factor_hk
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="chineseName != null">
        chinese_name,
      </if>
      <if test="materialAttribute != null">
        material_attribute,
      </if>
      <if test="supplier != null">
        supplier,
      </if>
      <if test="productionProcess != null">
        production_process,
      </if>
      <if test="materialBelongsTo != null">
        material_belongs_to,
      </if>
      <if test="unit != null">
        unit,
      </if>
      <if test="carbonFactor != null">
        carbon_factor,
      </if>
      <if test="carbonFactorUnit != null">
        carbon_factor_unit,
      </if>
      <if test="description != null">
        description,
      </if>
      <if test="sourceDescription != null">
        source_description,
      </if>
      <if test="creationTime != null">
        creation_time,
      </if>
      <if test="createUsername != null">
        create_username,
      </if>
      <if test="createUserId != null">
        create_user_id,
      </if>
      <if test="lastUpdateTime != null">
        last_update_time,
      </if>
      <if test="lastUpdateUsername != null">
        last_update_username,
      </if>
      <if test="lastUpdateUserId != null">
        last_update_user_id,
      </if>
      <if test="lastUpdateVersion != null">
        last_update_version,
      </if>
      <if test="isDeleted != null">
        is_deleted,
      </if>
      <if test="recordYear != null">
        record_year,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=CHAR},
      </if>
      <if test="chineseName != null">
        #{chineseName,jdbcType=NVARCHAR},
      </if>
      <if test="materialAttribute != null">
        #{materialAttribute,jdbcType=NVARCHAR},
      </if>
      <if test="supplier != null">
        #{supplier,jdbcType=NVARCHAR},
      </if>
      <if test="productionProcess != null">
        #{productionProcess,jdbcType=NVARCHAR},
      </if>
      <if test="materialBelongsTo != null">
        #{materialBelongsTo,jdbcType=NVARCHAR},
      </if>
      <if test="unit != null">
        #{unit,jdbcType=NVARCHAR},
      </if>
      <if test="carbonFactor != null">
        #{carbonFactor,jdbcType=NUMERIC},
      </if>
      <if test="carbonFactorUnit != null">
        #{carbonFactorUnit,jdbcType=NVARCHAR},
      </if>
      <if test="description != null">
        #{description,jdbcType=NVARCHAR},
      </if>
      <if test="sourceDescription != null">
        #{sourceDescription,jdbcType=NVARCHAR},
      </if>
      <if test="creationTime != null">
        #{creationTime,jdbcType=TIMESTAMP},
      </if>
      <if test="createUsername != null">
        #{createUsername,jdbcType=NVARCHAR},
      </if>
      <if test="createUserId != null">
        #{createUserId,jdbcType=CHAR},
      </if>
      <if test="lastUpdateTime != null">
        #{lastUpdateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="lastUpdateUsername != null">
        #{lastUpdateUsername,jdbcType=NVARCHAR},
      </if>
      <if test="lastUpdateUserId != null">
        #{lastUpdateUserId,jdbcType=CHAR},
      </if>
      <if test="lastUpdateVersion != null">
        #{lastUpdateVersion,jdbcType=INTEGER},
      </if>
      <if test="isDeleted != null">
        #{isDeleted,jdbcType=BIT},
      </if>
      <if test="recordYear != null">
        #{recordYear,jdbcType=INTEGER},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.csci.susdev.model.FcCarbonFactorHkExample" resultType="java.lang.Long">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Mon Apr 08 13:02:25 HKT 2024.
    -->
    select count(*) from t_fc_carbon_factor_hk
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Mon Apr 08 13:02:25 HKT 2024.
    -->
    update t_fc_carbon_factor_hk
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=CHAR},
      </if>
      <if test="record.chineseName != null">
        chinese_name = #{record.chineseName,jdbcType=NVARCHAR},
      </if>
      <if test="record.materialAttribute != null">
        material_attribute = #{record.materialAttribute,jdbcType=NVARCHAR},
      </if>
      <if test="record.supplier != null">
        supplier = #{record.supplier,jdbcType=NVARCHAR},
      </if>
      <if test="record.productionProcess != null">
        production_process = #{record.productionProcess,jdbcType=NVARCHAR},
      </if>
      <if test="record.materialBelongsTo != null">
        material_belongs_to = #{record.materialBelongsTo,jdbcType=NVARCHAR},
      </if>
      <if test="record.unit != null">
        unit = #{record.unit,jdbcType=NVARCHAR},
      </if>
      <if test="record.carbonFactor != null">
        carbon_factor = #{record.carbonFactor,jdbcType=NUMERIC},
      </if>
      <if test="record.carbonFactorUnit != null">
        carbon_factor_unit = #{record.carbonFactorUnit,jdbcType=NVARCHAR},
      </if>
      <if test="record.description != null">
        description = #{record.description,jdbcType=NVARCHAR},
      </if>
      <if test="record.sourceDescription != null">
        source_description = #{record.sourceDescription,jdbcType=NVARCHAR},
      </if>
      <if test="record.creationTime != null">
        creation_time = #{record.creationTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.createUsername != null">
        create_username = #{record.createUsername,jdbcType=NVARCHAR},
      </if>
      <if test="record.createUserId != null">
        create_user_id = #{record.createUserId,jdbcType=CHAR},
      </if>
      <if test="record.lastUpdateTime != null">
        last_update_time = #{record.lastUpdateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.lastUpdateUsername != null">
        last_update_username = #{record.lastUpdateUsername,jdbcType=NVARCHAR},
      </if>
      <if test="record.lastUpdateUserId != null">
        last_update_user_id = #{record.lastUpdateUserId,jdbcType=CHAR},
      </if>
      <if test="record.lastUpdateVersion != null">
        last_update_version = #{record.lastUpdateVersion,jdbcType=INTEGER},
      </if>
      <if test="record.isDeleted != null">
        is_deleted = #{record.isDeleted,jdbcType=BIT},
      </if>
      <if test="record.recordYear != null">
        record_year = #{record.recordYear,jdbcType=INTEGER},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Mon Apr 08 13:02:25 HKT 2024.
    -->
    update t_fc_carbon_factor_hk
    set id = #{record.id,jdbcType=CHAR},
      chinese_name = #{record.chineseName,jdbcType=NVARCHAR},
      material_attribute = #{record.materialAttribute,jdbcType=NVARCHAR},
      supplier = #{record.supplier,jdbcType=NVARCHAR},
      production_process = #{record.productionProcess,jdbcType=NVARCHAR},
      material_belongs_to = #{record.materialBelongsTo,jdbcType=NVARCHAR},
      unit = #{record.unit,jdbcType=NVARCHAR},
      carbon_factor = #{record.carbonFactor,jdbcType=NUMERIC},
      carbon_factor_unit = #{record.carbonFactorUnit,jdbcType=NVARCHAR},
      description = #{record.description,jdbcType=NVARCHAR},
      source_description = #{record.sourceDescription,jdbcType=NVARCHAR},
      creation_time = #{record.creationTime,jdbcType=TIMESTAMP},
      create_username = #{record.createUsername,jdbcType=NVARCHAR},
      create_user_id = #{record.createUserId,jdbcType=CHAR},
      last_update_time = #{record.lastUpdateTime,jdbcType=TIMESTAMP},
      last_update_username = #{record.lastUpdateUsername,jdbcType=NVARCHAR},
      last_update_user_id = #{record.lastUpdateUserId,jdbcType=CHAR},
      last_update_version = #{record.lastUpdateVersion,jdbcType=INTEGER},
      is_deleted = #{record.isDeleted,jdbcType=BIT},
      record_year = #{record.recordYear,jdbcType=INTEGER}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.csci.susdev.model.FcCarbonFactorHk">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Mon Apr 08 13:02:25 HKT 2024.
    -->
    update t_fc_carbon_factor_hk
    <set>
      <if test="chineseName != null">
        chinese_name = #{chineseName,jdbcType=NVARCHAR},
      </if>
      <if test="materialAttribute != null">
        material_attribute = #{materialAttribute,jdbcType=NVARCHAR},
      </if>
      <if test="supplier != null">
        supplier = #{supplier,jdbcType=NVARCHAR},
      </if>
      <if test="productionProcess != null">
        production_process = #{productionProcess,jdbcType=NVARCHAR},
      </if>
      <if test="materialBelongsTo != null">
        material_belongs_to = #{materialBelongsTo,jdbcType=NVARCHAR},
      </if>
      <if test="unit != null">
        unit = #{unit,jdbcType=NVARCHAR},
      </if>
      <if test="carbonFactor != null">
        carbon_factor = #{carbonFactor,jdbcType=NUMERIC},
      </if>
      <if test="carbonFactorUnit != null">
        carbon_factor_unit = #{carbonFactorUnit,jdbcType=NVARCHAR},
      </if>
      <if test="description != null">
        description = #{description,jdbcType=NVARCHAR},
      </if>
      <if test="sourceDescription != null">
        source_description = #{sourceDescription,jdbcType=NVARCHAR},
      </if>
      <if test="creationTime != null">
        creation_time = #{creationTime,jdbcType=TIMESTAMP},
      </if>
      <if test="createUsername != null">
        create_username = #{createUsername,jdbcType=NVARCHAR},
      </if>
      <if test="createUserId != null">
        create_user_id = #{createUserId,jdbcType=CHAR},
      </if>
      <if test="lastUpdateTime != null">
        last_update_time = #{lastUpdateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="lastUpdateUsername != null">
        last_update_username = #{lastUpdateUsername,jdbcType=NVARCHAR},
      </if>
      <if test="lastUpdateUserId != null">
        last_update_user_id = #{lastUpdateUserId,jdbcType=CHAR},
      </if>
      <if test="lastUpdateVersion != null">
        last_update_version = #{lastUpdateVersion,jdbcType=INTEGER},
      </if>
      <if test="isDeleted != null">
        is_deleted = #{isDeleted,jdbcType=BIT},
      </if>
      <if test="recordYear != null">
        record_year = #{recordYear,jdbcType=INTEGER},
      </if>
    </set>
    where id = #{id,jdbcType=CHAR}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.csci.susdev.model.FcCarbonFactorHk">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Mon Apr 08 13:02:25 HKT 2024.
    -->
    update t_fc_carbon_factor_hk
    set chinese_name = #{chineseName,jdbcType=NVARCHAR},
      material_attribute = #{materialAttribute,jdbcType=NVARCHAR},
      supplier = #{supplier,jdbcType=NVARCHAR},
      production_process = #{productionProcess,jdbcType=NVARCHAR},
      material_belongs_to = #{materialBelongsTo,jdbcType=NVARCHAR},
      unit = #{unit,jdbcType=NVARCHAR},
      carbon_factor = #{carbonFactor,jdbcType=NUMERIC},
      carbon_factor_unit = #{carbonFactorUnit,jdbcType=NVARCHAR},
      description = #{description,jdbcType=NVARCHAR},
      source_description = #{sourceDescription,jdbcType=NVARCHAR},
      creation_time = #{creationTime,jdbcType=TIMESTAMP},
      create_username = #{createUsername,jdbcType=NVARCHAR},
      create_user_id = #{createUserId,jdbcType=CHAR},
      last_update_time = #{lastUpdateTime,jdbcType=TIMESTAMP},
      last_update_username = #{lastUpdateUsername,jdbcType=NVARCHAR},
      last_update_user_id = #{lastUpdateUserId,jdbcType=CHAR},
      last_update_version = #{lastUpdateVersion,jdbcType=INTEGER},
      is_deleted = #{isDeleted,jdbcType=BIT},
      record_year = #{recordYear,jdbcType=INTEGER}
    where id = #{id,jdbcType=CHAR}
  </update>
</mapper>