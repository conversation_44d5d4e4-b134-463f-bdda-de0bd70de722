<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.csci.susdev.mapper.AmbientDetailMapper">
  <resultMap id="BaseResultMap" type="com.csci.susdev.model.AmbientDetail">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    <id column="id" jdbcType="VARCHAR" property="id" />
    <result column="head_id" jdbcType="VARCHAR" property="headId" />
    <result column="category" jdbcType="VARCHAR" property="category" />
    <result column="category_digest" jdbcType="VARCHAR" property="categoryDigest" />
    <result column="type" jdbcType="VARCHAR" property="type" />
    <result column="type2" jdbcType="VARCHAR" property="type2" />
    <result column="unit" jdbcType="VARCHAR" property="unit" />
    <result column="unit_code" jdbcType="VARCHAR" property="unitCode" />
    <result column="month_value_1" jdbcType="VARCHAR" property="monthValue1" />
    <result column="month_value_2" jdbcType="VARCHAR" property="monthValue2" />
    <result column="month_value_3" jdbcType="VARCHAR" property="monthValue3" />
    <result column="season_value_1" jdbcType="VARCHAR" property="seasonValue1" />
    <result column="month_value_4" jdbcType="VARCHAR" property="monthValue4" />
    <result column="month_value_5" jdbcType="VARCHAR" property="monthValue5" />
    <result column="month_value_6" jdbcType="VARCHAR" property="monthValue6" />
    <result column="season_value_2" jdbcType="VARCHAR" property="seasonValue2" />
    <result column="month_value_7" jdbcType="VARCHAR" property="monthValue7" />
    <result column="month_value_8" jdbcType="VARCHAR" property="monthValue8" />
    <result column="month_value_9" jdbcType="VARCHAR" property="monthValue9" />
    <result column="season_value_3" jdbcType="VARCHAR" property="seasonValue3" />
    <result column="month_value_10" jdbcType="VARCHAR" property="monthValue10" />
    <result column="month_value_11" jdbcType="VARCHAR" property="monthValue11" />
    <result column="month_value_12" jdbcType="VARCHAR" property="monthValue12" />
    <result column="season_value_4" jdbcType="VARCHAR" property="seasonValue4" />
    <result column="year_total_value" jdbcType="VARCHAR" property="yearTotalValue" />
    <result column="remark" jdbcType="VARCHAR" property="remark" />
    <result column="seq" jdbcType="INTEGER" property="seq" />
    <result column="creation_time" jdbcType="TIMESTAMP" property="creationTime" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    id, head_id, category, category_digest, type, type2, unit, unit_code, month_value_1, 
    month_value_2, month_value_3, season_value_1, month_value_4, month_value_5, month_value_6, 
    season_value_2, month_value_7, month_value_8, month_value_9, season_value_3, month_value_10, 
    month_value_11, month_value_12, season_value_4, year_total_value, remark, seq, creation_time
  </sql>
  <select id="selectByExample" parameterType="com.csci.susdev.model.AmbientDetailExample" resultMap="BaseResultMap">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from t_ambient_detail
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.String" resultMap="BaseResultMap">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    select 
    <include refid="Base_Column_List" />
    from t_ambient_detail
    where id = #{id,jdbcType=VARCHAR}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.String">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    delete from t_ambient_detail
    where id = #{id,jdbcType=VARCHAR}
  </delete>
  <delete id="deleteByExample" parameterType="com.csci.susdev.model.AmbientDetailExample">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    delete from t_ambient_detail
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.csci.susdev.model.AmbientDetail">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    insert into t_ambient_detail (id, head_id, category, 
      category_digest, type, type2, 
      unit, unit_code, month_value_1, 
      month_value_2, month_value_3, season_value_1, 
      month_value_4, month_value_5, month_value_6, 
      season_value_2, month_value_7, month_value_8, 
      month_value_9, season_value_3, month_value_10, 
      month_value_11, month_value_12, season_value_4, 
      year_total_value, remark, seq, 
      creation_time)
    values (#{id,jdbcType=VARCHAR}, #{headId,jdbcType=VARCHAR}, #{category,jdbcType=VARCHAR}, 
      #{categoryDigest,jdbcType=VARCHAR}, #{type,jdbcType=VARCHAR}, #{type2,jdbcType=VARCHAR}, 
      #{unit,jdbcType=VARCHAR}, #{unitCode,jdbcType=VARCHAR}, #{monthValue1,jdbcType=VARCHAR}, 
      #{monthValue2,jdbcType=VARCHAR}, #{monthValue3,jdbcType=VARCHAR}, #{seasonValue1,jdbcType=VARCHAR}, 
      #{monthValue4,jdbcType=VARCHAR}, #{monthValue5,jdbcType=VARCHAR}, #{monthValue6,jdbcType=VARCHAR}, 
      #{seasonValue2,jdbcType=VARCHAR}, #{monthValue7,jdbcType=VARCHAR}, #{monthValue8,jdbcType=VARCHAR}, 
      #{monthValue9,jdbcType=VARCHAR}, #{seasonValue3,jdbcType=VARCHAR}, #{monthValue10,jdbcType=VARCHAR}, 
      #{monthValue11,jdbcType=VARCHAR}, #{monthValue12,jdbcType=VARCHAR}, #{seasonValue4,jdbcType=VARCHAR}, 
      #{yearTotalValue,jdbcType=VARCHAR}, #{remark,jdbcType=VARCHAR}, #{seq,jdbcType=INTEGER}, 
      #{creationTime,jdbcType=TIMESTAMP})
  </insert>
  <insert id="insertSelective" parameterType="com.csci.susdev.model.AmbientDetail">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    insert into t_ambient_detail
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="headId != null">
        head_id,
      </if>
      <if test="category != null">
        category,
      </if>
      <if test="categoryDigest != null">
        category_digest,
      </if>
      <if test="type != null">
        type,
      </if>
      <if test="type2 != null">
        type2,
      </if>
      <if test="unit != null">
        unit,
      </if>
      <if test="unitCode != null">
        unit_code,
      </if>
      <if test="monthValue1 != null">
        month_value_1,
      </if>
      <if test="monthValue2 != null">
        month_value_2,
      </if>
      <if test="monthValue3 != null">
        month_value_3,
      </if>
      <if test="seasonValue1 != null">
        season_value_1,
      </if>
      <if test="monthValue4 != null">
        month_value_4,
      </if>
      <if test="monthValue5 != null">
        month_value_5,
      </if>
      <if test="monthValue6 != null">
        month_value_6,
      </if>
      <if test="seasonValue2 != null">
        season_value_2,
      </if>
      <if test="monthValue7 != null">
        month_value_7,
      </if>
      <if test="monthValue8 != null">
        month_value_8,
      </if>
      <if test="monthValue9 != null">
        month_value_9,
      </if>
      <if test="seasonValue3 != null">
        season_value_3,
      </if>
      <if test="monthValue10 != null">
        month_value_10,
      </if>
      <if test="monthValue11 != null">
        month_value_11,
      </if>
      <if test="monthValue12 != null">
        month_value_12,
      </if>
      <if test="seasonValue4 != null">
        season_value_4,
      </if>
      <if test="yearTotalValue != null">
        year_total_value,
      </if>
      <if test="remark != null">
        remark,
      </if>
      <if test="seq != null">
        seq,
      </if>
      <if test="creationTime != null">
        creation_time,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=VARCHAR},
      </if>
      <if test="headId != null">
        #{headId,jdbcType=VARCHAR},
      </if>
      <if test="category != null">
        #{category,jdbcType=VARCHAR},
      </if>
      <if test="categoryDigest != null">
        #{categoryDigest,jdbcType=VARCHAR},
      </if>
      <if test="type != null">
        #{type,jdbcType=VARCHAR},
      </if>
      <if test="type2 != null">
        #{type2,jdbcType=VARCHAR},
      </if>
      <if test="unit != null">
        #{unit,jdbcType=VARCHAR},
      </if>
      <if test="unitCode != null">
        #{unitCode,jdbcType=VARCHAR},
      </if>
      <if test="monthValue1 != null">
        #{monthValue1,jdbcType=VARCHAR},
      </if>
      <if test="monthValue2 != null">
        #{monthValue2,jdbcType=VARCHAR},
      </if>
      <if test="monthValue3 != null">
        #{monthValue3,jdbcType=VARCHAR},
      </if>
      <if test="seasonValue1 != null">
        #{seasonValue1,jdbcType=VARCHAR},
      </if>
      <if test="monthValue4 != null">
        #{monthValue4,jdbcType=VARCHAR},
      </if>
      <if test="monthValue5 != null">
        #{monthValue5,jdbcType=VARCHAR},
      </if>
      <if test="monthValue6 != null">
        #{monthValue6,jdbcType=VARCHAR},
      </if>
      <if test="seasonValue2 != null">
        #{seasonValue2,jdbcType=VARCHAR},
      </if>
      <if test="monthValue7 != null">
        #{monthValue7,jdbcType=VARCHAR},
      </if>
      <if test="monthValue8 != null">
        #{monthValue8,jdbcType=VARCHAR},
      </if>
      <if test="monthValue9 != null">
        #{monthValue9,jdbcType=VARCHAR},
      </if>
      <if test="seasonValue3 != null">
        #{seasonValue3,jdbcType=VARCHAR},
      </if>
      <if test="monthValue10 != null">
        #{monthValue10,jdbcType=VARCHAR},
      </if>
      <if test="monthValue11 != null">
        #{monthValue11,jdbcType=VARCHAR},
      </if>
      <if test="monthValue12 != null">
        #{monthValue12,jdbcType=VARCHAR},
      </if>
      <if test="seasonValue4 != null">
        #{seasonValue4,jdbcType=VARCHAR},
      </if>
      <if test="yearTotalValue != null">
        #{yearTotalValue,jdbcType=VARCHAR},
      </if>
      <if test="remark != null">
        #{remark,jdbcType=VARCHAR},
      </if>
      <if test="seq != null">
        #{seq,jdbcType=INTEGER},
      </if>
      <if test="creationTime != null">
        #{creationTime,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.csci.susdev.model.AmbientDetailExample" resultType="java.lang.Long">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    select count(*) from t_ambient_detail
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    update t_ambient_detail
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=VARCHAR},
      </if>
      <if test="record.headId != null">
        head_id = #{record.headId,jdbcType=VARCHAR},
      </if>
      <if test="record.category != null">
        category = #{record.category,jdbcType=VARCHAR},
      </if>
      <if test="record.categoryDigest != null">
        category_digest = #{record.categoryDigest,jdbcType=VARCHAR},
      </if>
      <if test="record.type != null">
        type = #{record.type,jdbcType=VARCHAR},
      </if>
      <if test="record.type2 != null">
        type2 = #{record.type2,jdbcType=VARCHAR},
      </if>
      <if test="record.unit != null">
        unit = #{record.unit,jdbcType=VARCHAR},
      </if>
      <if test="record.unitCode != null">
        unit_code = #{record.unitCode,jdbcType=VARCHAR},
      </if>
      <if test="record.monthValue1 != null">
        month_value_1 = #{record.monthValue1,jdbcType=VARCHAR},
      </if>
      <if test="record.monthValue2 != null">
        month_value_2 = #{record.monthValue2,jdbcType=VARCHAR},
      </if>
      <if test="record.monthValue3 != null">
        month_value_3 = #{record.monthValue3,jdbcType=VARCHAR},
      </if>
      <if test="record.seasonValue1 != null">
        season_value_1 = #{record.seasonValue1,jdbcType=VARCHAR},
      </if>
      <if test="record.monthValue4 != null">
        month_value_4 = #{record.monthValue4,jdbcType=VARCHAR},
      </if>
      <if test="record.monthValue5 != null">
        month_value_5 = #{record.monthValue5,jdbcType=VARCHAR},
      </if>
      <if test="record.monthValue6 != null">
        month_value_6 = #{record.monthValue6,jdbcType=VARCHAR},
      </if>
      <if test="record.seasonValue2 != null">
        season_value_2 = #{record.seasonValue2,jdbcType=VARCHAR},
      </if>
      <if test="record.monthValue7 != null">
        month_value_7 = #{record.monthValue7,jdbcType=VARCHAR},
      </if>
      <if test="record.monthValue8 != null">
        month_value_8 = #{record.monthValue8,jdbcType=VARCHAR},
      </if>
      <if test="record.monthValue9 != null">
        month_value_9 = #{record.monthValue9,jdbcType=VARCHAR},
      </if>
      <if test="record.seasonValue3 != null">
        season_value_3 = #{record.seasonValue3,jdbcType=VARCHAR},
      </if>
      <if test="record.monthValue10 != null">
        month_value_10 = #{record.monthValue10,jdbcType=VARCHAR},
      </if>
      <if test="record.monthValue11 != null">
        month_value_11 = #{record.monthValue11,jdbcType=VARCHAR},
      </if>
      <if test="record.monthValue12 != null">
        month_value_12 = #{record.monthValue12,jdbcType=VARCHAR},
      </if>
      <if test="record.seasonValue4 != null">
        season_value_4 = #{record.seasonValue4,jdbcType=VARCHAR},
      </if>
      <if test="record.yearTotalValue != null">
        year_total_value = #{record.yearTotalValue,jdbcType=VARCHAR},
      </if>
      <if test="record.remark != null">
        remark = #{record.remark,jdbcType=VARCHAR},
      </if>
      <if test="record.seq != null">
        seq = #{record.seq,jdbcType=INTEGER},
      </if>
      <if test="record.creationTime != null">
        creation_time = #{record.creationTime,jdbcType=TIMESTAMP},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    update t_ambient_detail
    set id = #{record.id,jdbcType=VARCHAR},
      head_id = #{record.headId,jdbcType=VARCHAR},
      category = #{record.category,jdbcType=VARCHAR},
      category_digest = #{record.categoryDigest,jdbcType=VARCHAR},
      type = #{record.type,jdbcType=VARCHAR},
      type2 = #{record.type2,jdbcType=VARCHAR},
      unit = #{record.unit,jdbcType=VARCHAR},
      unit_code = #{record.unitCode,jdbcType=VARCHAR},
      month_value_1 = #{record.monthValue1,jdbcType=VARCHAR},
      month_value_2 = #{record.monthValue2,jdbcType=VARCHAR},
      month_value_3 = #{record.monthValue3,jdbcType=VARCHAR},
      season_value_1 = #{record.seasonValue1,jdbcType=VARCHAR},
      month_value_4 = #{record.monthValue4,jdbcType=VARCHAR},
      month_value_5 = #{record.monthValue5,jdbcType=VARCHAR},
      month_value_6 = #{record.monthValue6,jdbcType=VARCHAR},
      season_value_2 = #{record.seasonValue2,jdbcType=VARCHAR},
      month_value_7 = #{record.monthValue7,jdbcType=VARCHAR},
      month_value_8 = #{record.monthValue8,jdbcType=VARCHAR},
      month_value_9 = #{record.monthValue9,jdbcType=VARCHAR},
      season_value_3 = #{record.seasonValue3,jdbcType=VARCHAR},
      month_value_10 = #{record.monthValue10,jdbcType=VARCHAR},
      month_value_11 = #{record.monthValue11,jdbcType=VARCHAR},
      month_value_12 = #{record.monthValue12,jdbcType=VARCHAR},
      season_value_4 = #{record.seasonValue4,jdbcType=VARCHAR},
      year_total_value = #{record.yearTotalValue,jdbcType=VARCHAR},
      remark = #{record.remark,jdbcType=VARCHAR},
      seq = #{record.seq,jdbcType=INTEGER},
      creation_time = #{record.creationTime,jdbcType=TIMESTAMP}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.csci.susdev.model.AmbientDetail">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    update t_ambient_detail
    <set>
      <if test="headId != null">
        head_id = #{headId,jdbcType=VARCHAR},
      </if>
      <if test="category != null">
        category = #{category,jdbcType=VARCHAR},
      </if>
      <if test="categoryDigest != null">
        category_digest = #{categoryDigest,jdbcType=VARCHAR},
      </if>
      <if test="type != null">
        type = #{type,jdbcType=VARCHAR},
      </if>
      <if test="type2 != null">
        type2 = #{type2,jdbcType=VARCHAR},
      </if>
      <if test="unit != null">
        unit = #{unit,jdbcType=VARCHAR},
      </if>
      <if test="unitCode != null">
        unit_code = #{unitCode,jdbcType=VARCHAR},
      </if>
      <if test="monthValue1 != null">
        month_value_1 = #{monthValue1,jdbcType=VARCHAR},
      </if>
      <if test="monthValue2 != null">
        month_value_2 = #{monthValue2,jdbcType=VARCHAR},
      </if>
      <if test="monthValue3 != null">
        month_value_3 = #{monthValue3,jdbcType=VARCHAR},
      </if>
      <if test="seasonValue1 != null">
        season_value_1 = #{seasonValue1,jdbcType=VARCHAR},
      </if>
      <if test="monthValue4 != null">
        month_value_4 = #{monthValue4,jdbcType=VARCHAR},
      </if>
      <if test="monthValue5 != null">
        month_value_5 = #{monthValue5,jdbcType=VARCHAR},
      </if>
      <if test="monthValue6 != null">
        month_value_6 = #{monthValue6,jdbcType=VARCHAR},
      </if>
      <if test="seasonValue2 != null">
        season_value_2 = #{seasonValue2,jdbcType=VARCHAR},
      </if>
      <if test="monthValue7 != null">
        month_value_7 = #{monthValue7,jdbcType=VARCHAR},
      </if>
      <if test="monthValue8 != null">
        month_value_8 = #{monthValue8,jdbcType=VARCHAR},
      </if>
      <if test="monthValue9 != null">
        month_value_9 = #{monthValue9,jdbcType=VARCHAR},
      </if>
      <if test="seasonValue3 != null">
        season_value_3 = #{seasonValue3,jdbcType=VARCHAR},
      </if>
      <if test="monthValue10 != null">
        month_value_10 = #{monthValue10,jdbcType=VARCHAR},
      </if>
      <if test="monthValue11 != null">
        month_value_11 = #{monthValue11,jdbcType=VARCHAR},
      </if>
      <if test="monthValue12 != null">
        month_value_12 = #{monthValue12,jdbcType=VARCHAR},
      </if>
      <if test="seasonValue4 != null">
        season_value_4 = #{seasonValue4,jdbcType=VARCHAR},
      </if>
      <if test="yearTotalValue != null">
        year_total_value = #{yearTotalValue,jdbcType=VARCHAR},
      </if>
      <if test="remark != null">
        remark = #{remark,jdbcType=VARCHAR},
      </if>
      <if test="seq != null">
        seq = #{seq,jdbcType=INTEGER},
      </if>
      <if test="creationTime != null">
        creation_time = #{creationTime,jdbcType=TIMESTAMP},
      </if>
    </set>
    where id = #{id,jdbcType=VARCHAR}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.csci.susdev.model.AmbientDetail">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    update t_ambient_detail
    set head_id = #{headId,jdbcType=VARCHAR},
      category = #{category,jdbcType=VARCHAR},
      category_digest = #{categoryDigest,jdbcType=VARCHAR},
      type = #{type,jdbcType=VARCHAR},
      type2 = #{type2,jdbcType=VARCHAR},
      unit = #{unit,jdbcType=VARCHAR},
      unit_code = #{unitCode,jdbcType=VARCHAR},
      month_value_1 = #{monthValue1,jdbcType=VARCHAR},
      month_value_2 = #{monthValue2,jdbcType=VARCHAR},
      month_value_3 = #{monthValue3,jdbcType=VARCHAR},
      season_value_1 = #{seasonValue1,jdbcType=VARCHAR},
      month_value_4 = #{monthValue4,jdbcType=VARCHAR},
      month_value_5 = #{monthValue5,jdbcType=VARCHAR},
      month_value_6 = #{monthValue6,jdbcType=VARCHAR},
      season_value_2 = #{seasonValue2,jdbcType=VARCHAR},
      month_value_7 = #{monthValue7,jdbcType=VARCHAR},
      month_value_8 = #{monthValue8,jdbcType=VARCHAR},
      month_value_9 = #{monthValue9,jdbcType=VARCHAR},
      season_value_3 = #{seasonValue3,jdbcType=VARCHAR},
      month_value_10 = #{monthValue10,jdbcType=VARCHAR},
      month_value_11 = #{monthValue11,jdbcType=VARCHAR},
      month_value_12 = #{monthValue12,jdbcType=VARCHAR},
      season_value_4 = #{seasonValue4,jdbcType=VARCHAR},
      year_total_value = #{yearTotalValue,jdbcType=VARCHAR},
      remark = #{remark,jdbcType=VARCHAR},
      seq = #{seq,jdbcType=INTEGER},
      creation_time = #{creationTime,jdbcType=TIMESTAMP}
    where id = #{id,jdbcType=VARCHAR}
  </update>
</mapper>