<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.csci.susdev.mapper.CdmsMaterialInvoiceMapper">
  <resultMap id="BaseResultMap" type="com.csci.susdev.model.CdmsMaterialInvoice">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Fri Jan 05 15:59:13 HKT 2024.
    -->
    <id column="id" jdbcType="VARCHAR" property="id" />
    <result column="organization_id" jdbcType="VARCHAR" property="organizationId" />
    <result column="organization_name" jdbcType="VARCHAR" property="organizationName" />
    <result column="record_year_month" jdbcType="INTEGER" property="recordYearMonth" />
    <result column="carbon_emission_location" jdbcType="VARCHAR" property="carbonEmissionLocation" />
    <result column="material_code" jdbcType="VARCHAR" property="materialCode" />
    <result column="classification" jdbcType="VARCHAR" property="classification" />
    <result column="description" jdbcType="VARCHAR" property="description" />
    <result column="pur_invoice_id" jdbcType="VARCHAR" property="purInvoiceId" />
    <result column="invoice_file" jdbcType="VARCHAR" property="invoiceFile" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Fri Jan 05 15:59:13 HKT 2024.
    -->
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Fri Jan 05 15:59:13 HKT 2024.
    -->
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Fri Jan 05 15:59:13 HKT 2024.
    -->
    id, organization_id, organization_name, record_year_month, carbon_emission_location, 
    material_code, classification, description, pur_invoice_id, invoice_file
  </sql>
  <select id="selectByExample" parameterType="com.csci.susdev.model.CdmsMaterialInvoiceExample" resultMap="BaseResultMap">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Fri Jan 05 15:59:13 HKT 2024.
    -->
    select
    <if test="distinct">
      distinct
    </if>
    'true' as QUERYID,
    <include refid="Base_Column_List" />
    from t_cdms_material_invoice
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.String" resultMap="BaseResultMap">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Fri Jan 05 15:59:13 HKT 2024.
    -->
    select 
    <include refid="Base_Column_List" />
    from t_cdms_material_invoice
    where id = #{id,jdbcType=VARCHAR}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.String">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Fri Jan 05 15:59:13 HKT 2024.
    -->
    delete from t_cdms_material_invoice
    where id = #{id,jdbcType=VARCHAR}
  </delete>
  <delete id="deleteByExample" parameterType="com.csci.susdev.model.CdmsMaterialInvoiceExample">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Fri Jan 05 15:59:13 HKT 2024.
    -->
    delete from t_cdms_material_invoice
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.csci.susdev.model.CdmsMaterialInvoice">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Fri Jan 05 15:59:13 HKT 2024.
    -->
    insert into t_cdms_material_invoice (id, organization_id, organization_name, 
      record_year_month, carbon_emission_location, 
      material_code, classification, description, 
      pur_invoice_id, invoice_file)
    values (#{id,jdbcType=VARCHAR}, #{organizationId,jdbcType=VARCHAR}, #{organizationName,jdbcType=VARCHAR}, 
      #{recordYearMonth,jdbcType=INTEGER}, #{carbonEmissionLocation,jdbcType=VARCHAR}, 
      #{materialCode,jdbcType=VARCHAR}, #{classification,jdbcType=VARCHAR}, #{description,jdbcType=VARCHAR}, 
      #{purInvoiceId,jdbcType=VARCHAR}, #{invoiceFile,jdbcType=VARCHAR})
  </insert>
  <insert id="insertSelective" parameterType="com.csci.susdev.model.CdmsMaterialInvoice">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Fri Jan 05 15:59:13 HKT 2024.
    -->
    insert into t_cdms_material_invoice
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="organizationId != null">
        organization_id,
      </if>
      <if test="organizationName != null">
        organization_name,
      </if>
      <if test="recordYearMonth != null">
        record_year_month,
      </if>
      <if test="carbonEmissionLocation != null">
        carbon_emission_location,
      </if>
      <if test="materialCode != null">
        material_code,
      </if>
      <if test="classification != null">
        classification,
      </if>
      <if test="description != null">
        description,
      </if>
      <if test="purInvoiceId != null">
        pur_invoice_id,
      </if>
      <if test="invoiceFile != null">
        invoice_file,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=VARCHAR},
      </if>
      <if test="organizationId != null">
        #{organizationId,jdbcType=VARCHAR},
      </if>
      <if test="organizationName != null">
        #{organizationName,jdbcType=VARCHAR},
      </if>
      <if test="recordYearMonth != null">
        #{recordYearMonth,jdbcType=INTEGER},
      </if>
      <if test="carbonEmissionLocation != null">
        #{carbonEmissionLocation,jdbcType=VARCHAR},
      </if>
      <if test="materialCode != null">
        #{materialCode,jdbcType=VARCHAR},
      </if>
      <if test="classification != null">
        #{classification,jdbcType=VARCHAR},
      </if>
      <if test="description != null">
        #{description,jdbcType=VARCHAR},
      </if>
      <if test="purInvoiceId != null">
        #{purInvoiceId,jdbcType=VARCHAR},
      </if>
      <if test="invoiceFile != null">
        #{invoiceFile,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.csci.susdev.model.CdmsMaterialInvoiceExample" resultType="java.lang.Long">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Fri Jan 05 15:59:13 HKT 2024.
    -->
    select count(*) from t_cdms_material_invoice
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Fri Jan 05 15:59:13 HKT 2024.
    -->
    update t_cdms_material_invoice
    <set>
      <if test="row.id != null">
        id = #{row.id,jdbcType=VARCHAR},
      </if>
      <if test="row.organizationId != null">
        organization_id = #{row.organizationId,jdbcType=VARCHAR},
      </if>
      <if test="row.organizationName != null">
        organization_name = #{row.organizationName,jdbcType=VARCHAR},
      </if>
      <if test="row.recordYearMonth != null">
        record_year_month = #{row.recordYearMonth,jdbcType=INTEGER},
      </if>
      <if test="row.carbonEmissionLocation != null">
        carbon_emission_location = #{row.carbonEmissionLocation,jdbcType=VARCHAR},
      </if>
      <if test="row.materialCode != null">
        material_code = #{row.materialCode,jdbcType=VARCHAR},
      </if>
      <if test="row.classification != null">
        classification = #{row.classification,jdbcType=VARCHAR},
      </if>
      <if test="row.description != null">
        description = #{row.description,jdbcType=VARCHAR},
      </if>
      <if test="row.purInvoiceId != null">
        pur_invoice_id = #{row.purInvoiceId,jdbcType=VARCHAR},
      </if>
      <if test="row.invoiceFile != null">
        invoice_file = #{row.invoiceFile,jdbcType=VARCHAR},
      </if>
    </set>
    <if test="example != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Fri Jan 05 15:59:13 HKT 2024.
    -->
    update t_cdms_material_invoice
    set id = #{row.id,jdbcType=VARCHAR},
      organization_id = #{row.organizationId,jdbcType=VARCHAR},
      organization_name = #{row.organizationName,jdbcType=VARCHAR},
      record_year_month = #{row.recordYearMonth,jdbcType=INTEGER},
      carbon_emission_location = #{row.carbonEmissionLocation,jdbcType=VARCHAR},
      material_code = #{row.materialCode,jdbcType=VARCHAR},
      classification = #{row.classification,jdbcType=VARCHAR},
      description = #{row.description,jdbcType=VARCHAR},
      pur_invoice_id = #{row.purInvoiceId,jdbcType=VARCHAR},
      invoice_file = #{row.invoiceFile,jdbcType=VARCHAR}
    <if test="example != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.csci.susdev.model.CdmsMaterialInvoice">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Fri Jan 05 15:59:13 HKT 2024.
    -->
    update t_cdms_material_invoice
    <set>
      <if test="organizationId != null">
        organization_id = #{organizationId,jdbcType=VARCHAR},
      </if>
      <if test="organizationName != null">
        organization_name = #{organizationName,jdbcType=VARCHAR},
      </if>
      <if test="recordYearMonth != null">
        record_year_month = #{recordYearMonth,jdbcType=INTEGER},
      </if>
      <if test="carbonEmissionLocation != null">
        carbon_emission_location = #{carbonEmissionLocation,jdbcType=VARCHAR},
      </if>
      <if test="materialCode != null">
        material_code = #{materialCode,jdbcType=VARCHAR},
      </if>
      <if test="classification != null">
        classification = #{classification,jdbcType=VARCHAR},
      </if>
      <if test="description != null">
        description = #{description,jdbcType=VARCHAR},
      </if>
      <if test="purInvoiceId != null">
        pur_invoice_id = #{purInvoiceId,jdbcType=VARCHAR},
      </if>
      <if test="invoiceFile != null">
        invoice_file = #{invoiceFile,jdbcType=VARCHAR},
      </if>
    </set>
    where id = #{id,jdbcType=VARCHAR}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.csci.susdev.model.CdmsMaterialInvoice">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Fri Jan 05 15:59:13 HKT 2024.
    -->
    update t_cdms_material_invoice
    set organization_id = #{organizationId,jdbcType=VARCHAR},
      organization_name = #{organizationName,jdbcType=VARCHAR},
      record_year_month = #{recordYearMonth,jdbcType=INTEGER},
      carbon_emission_location = #{carbonEmissionLocation,jdbcType=VARCHAR},
      material_code = #{materialCode,jdbcType=VARCHAR},
      classification = #{classification,jdbcType=VARCHAR},
      description = #{description,jdbcType=VARCHAR},
      pur_invoice_id = #{purInvoiceId,jdbcType=VARCHAR},
      invoice_file = #{invoiceFile,jdbcType=VARCHAR}
    where id = #{id,jdbcType=VARCHAR}
  </update>
</mapper>