<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.csci.susdev.mapper.MinioAttachmentMapper">
  <resultMap id="BaseResultMap" type="com.csci.susdev.model.MinioAttachment">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Wed Dec 18 15:54:10 CST 2024.
    -->
    <result column="id" jdbcType="NVARCHAR" property="id" />
    <result column="ref_id" jdbcType="NVARCHAR" property="refId" />
    <result column="category" jdbcType="NVARCHAR" property="category" />
    <result column="section" jdbcType="NVARCHAR" property="section" />
    <result column="type" jdbcType="NVARCHAR" property="type" />
    <result column="name" jdbcType="NVARCHAR" property="name" />
    <result column="minio_file_name" jdbcType="NVARCHAR" property="minioFileName" />
    <result column="minio_file_url" jdbcType="NVARCHAR" property="minioFileUrl" />
    <result column="creation_time" jdbcType="TIMESTAMP" property="creationTime" />
    <result column="create_username" jdbcType="NVARCHAR" property="createUsername" />
    <result column="create_user_id" jdbcType="NVARCHAR" property="createUserId" />
    <result column="last_update_time" jdbcType="TIMESTAMP" property="lastUpdateTime" />
    <result column="last_update_username" jdbcType="NVARCHAR" property="lastUpdateUsername" />
    <result column="last_update_user_id" jdbcType="NVARCHAR" property="lastUpdateUserId" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Wed Dec 18 15:54:10 CST 2024.
    -->
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Wed Dec 18 15:54:10 CST 2024.
    -->
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Wed Dec 18 15:54:10 CST 2024.
    -->
    id, ref_id, category, section, type, name, minio_file_name, minio_file_url, creation_time, 
    create_username, create_user_id, last_update_time, last_update_username, last_update_user_id
  </sql>
  <select id="selectByExample" parameterType="com.csci.susdev.model.MinioAttachmentExample" resultMap="BaseResultMap">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Wed Dec 18 15:54:10 CST 2024.
    -->
    select
    <if test="distinct">
      distinct
    </if>
    'true' as QUERYID,
    <include refid="Base_Column_List" />
    from t_minio_attachment
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>
  <delete id="deleteByExample" parameterType="com.csci.susdev.model.MinioAttachmentExample">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Wed Dec 18 15:54:10 CST 2024.
    -->
    delete from t_minio_attachment
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.csci.susdev.model.MinioAttachment">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Wed Dec 18 15:54:10 CST 2024.
    -->
    insert into t_minio_attachment (id, ref_id, category, 
      section, type, name, 
      minio_file_name, minio_file_url, creation_time, 
      create_username, create_user_id, last_update_time, 
      last_update_username, last_update_user_id)
    values (#{id,jdbcType=NVARCHAR}, #{refId,jdbcType=NVARCHAR}, #{category,jdbcType=NVARCHAR}, 
      #{section,jdbcType=NVARCHAR}, #{type,jdbcType=NVARCHAR}, #{name,jdbcType=NVARCHAR}, 
      #{minioFileName,jdbcType=NVARCHAR}, #{minioFileUrl,jdbcType=NVARCHAR}, #{creationTime,jdbcType=TIMESTAMP}, 
      #{createUsername,jdbcType=NVARCHAR}, #{createUserId,jdbcType=NVARCHAR}, #{lastUpdateTime,jdbcType=TIMESTAMP}, 
      #{lastUpdateUsername,jdbcType=NVARCHAR}, #{lastUpdateUserId,jdbcType=NVARCHAR})
  </insert>
  <insert id="insertSelective" parameterType="com.csci.susdev.model.MinioAttachment">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Wed Dec 18 15:54:10 CST 2024.
    -->
    insert into t_minio_attachment
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="refId != null">
        ref_id,
      </if>
      <if test="category != null">
        category,
      </if>
      <if test="section != null">
        section,
      </if>
      <if test="type != null">
        type,
      </if>
      <if test="name != null">
        name,
      </if>
      <if test="minioFileName != null">
        minio_file_name,
      </if>
      <if test="minioFileUrl != null">
        minio_file_url,
      </if>
      <if test="creationTime != null">
        creation_time,
      </if>
      <if test="createUsername != null">
        create_username,
      </if>
      <if test="createUserId != null">
        create_user_id,
      </if>
      <if test="lastUpdateTime != null">
        last_update_time,
      </if>
      <if test="lastUpdateUsername != null">
        last_update_username,
      </if>
      <if test="lastUpdateUserId != null">
        last_update_user_id,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=NVARCHAR},
      </if>
      <if test="refId != null">
        #{refId,jdbcType=NVARCHAR},
      </if>
      <if test="category != null">
        #{category,jdbcType=NVARCHAR},
      </if>
      <if test="section != null">
        #{section,jdbcType=NVARCHAR},
      </if>
      <if test="type != null">
        #{type,jdbcType=NVARCHAR},
      </if>
      <if test="name != null">
        #{name,jdbcType=NVARCHAR},
      </if>
      <if test="minioFileName != null">
        #{minioFileName,jdbcType=NVARCHAR},
      </if>
      <if test="minioFileUrl != null">
        #{minioFileUrl,jdbcType=NVARCHAR},
      </if>
      <if test="creationTime != null">
        #{creationTime,jdbcType=TIMESTAMP},
      </if>
      <if test="createUsername != null">
        #{createUsername,jdbcType=NVARCHAR},
      </if>
      <if test="createUserId != null">
        #{createUserId,jdbcType=NVARCHAR},
      </if>
      <if test="lastUpdateTime != null">
        #{lastUpdateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="lastUpdateUsername != null">
        #{lastUpdateUsername,jdbcType=NVARCHAR},
      </if>
      <if test="lastUpdateUserId != null">
        #{lastUpdateUserId,jdbcType=NVARCHAR},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.csci.susdev.model.MinioAttachmentExample" resultType="java.lang.Long">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Wed Dec 18 15:54:10 CST 2024.
    -->
    select count(*) from t_minio_attachment
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Wed Dec 18 15:54:10 CST 2024.
    -->
    update t_minio_attachment
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=NVARCHAR},
      </if>
      <if test="record.refId != null">
        ref_id = #{record.refId,jdbcType=NVARCHAR},
      </if>
      <if test="record.category != null">
        category = #{record.category,jdbcType=NVARCHAR},
      </if>
      <if test="record.section != null">
        section = #{record.section,jdbcType=NVARCHAR},
      </if>
      <if test="record.type != null">
        type = #{record.type,jdbcType=NVARCHAR},
      </if>
      <if test="record.name != null">
        name = #{record.name,jdbcType=NVARCHAR},
      </if>
      <if test="record.minioFileName != null">
        minio_file_name = #{record.minioFileName,jdbcType=NVARCHAR},
      </if>
      <if test="record.minioFileUrl != null">
        minio_file_url = #{record.minioFileUrl,jdbcType=NVARCHAR},
      </if>
      <if test="record.creationTime != null">
        creation_time = #{record.creationTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.createUsername != null">
        create_username = #{record.createUsername,jdbcType=NVARCHAR},
      </if>
      <if test="record.createUserId != null">
        create_user_id = #{record.createUserId,jdbcType=NVARCHAR},
      </if>
      <if test="record.lastUpdateTime != null">
        last_update_time = #{record.lastUpdateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.lastUpdateUsername != null">
        last_update_username = #{record.lastUpdateUsername,jdbcType=NVARCHAR},
      </if>
      <if test="record.lastUpdateUserId != null">
        last_update_user_id = #{record.lastUpdateUserId,jdbcType=NVARCHAR},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Wed Dec 18 15:54:10 CST 2024.
    -->
    update t_minio_attachment
    set id = #{record.id,jdbcType=NVARCHAR},
      ref_id = #{record.refId,jdbcType=NVARCHAR},
      category = #{record.category,jdbcType=NVARCHAR},
      section = #{record.section,jdbcType=NVARCHAR},
      type = #{record.type,jdbcType=NVARCHAR},
      name = #{record.name,jdbcType=NVARCHAR},
      minio_file_name = #{record.minioFileName,jdbcType=NVARCHAR},
      minio_file_url = #{record.minioFileUrl,jdbcType=NVARCHAR},
      creation_time = #{record.creationTime,jdbcType=TIMESTAMP},
      create_username = #{record.createUsername,jdbcType=NVARCHAR},
      create_user_id = #{record.createUserId,jdbcType=NVARCHAR},
      last_update_time = #{record.lastUpdateTime,jdbcType=TIMESTAMP},
      last_update_username = #{record.lastUpdateUsername,jdbcType=NVARCHAR},
      last_update_user_id = #{record.lastUpdateUserId,jdbcType=NVARCHAR}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
</mapper>