<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.csci.susdev.mapper.AmbientHeadMapperCustom">

<select id="selectAllSubmittedHeadId" resultType="java.lang.String">
    select id
    from t_ambient_head h
    <where>
        h.is_active = 1
        <if test="year != null">
            and h.year = #{year}
        </if>
        <if test="month != null">
            and h.month = #{month}
        </if>
        <if test="organizationIds != null and organizationIds.size() > 0">
            and h.organization_id in
            <foreach collection="organizationIds" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        and exists(select 1 from t_workflow_control c where c.business_id = h.id and c.is_active = 1)
    </where>
</select>

<select id="selectAllSubmittedHeadIdByYearMonth" resultType="java.lang.String">
    select id
    from t_ambient_head h
    <where>
        h.is_active = 1
        <if test="year != null">
            and h.year = #{year}
        </if>
        <if test="month != null">
            and h.month = #{month}
        </if>
        and exists(select 1 from t_workflow_control c where c.business_id = h.id and c.is_active = 1 and c.state = 1)
    </where>
</select>
</mapper>