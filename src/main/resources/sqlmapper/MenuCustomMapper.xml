<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.csci.susdev.mapper.MenuCustomMapper">


  <select id="listMenuExt" resultType="com.csci.susdev.vo.MenuExtVO">
    SELECT
      menu.id,
      menu.parent_id parentId,
      menu.path,
      menu.description,
      menu.route_icon routeIcon,
      menu.route_title routeTitle,
      menu.route_path routePath,
      menu.route_redirect routeRedirect,
      menu.route_component routeComponent,
      menu.seq,
      menu.is_active isActive,
      parentMenu.description parentDescription
    FROM
      t_menu menu
        LEFT JOIN t_menu parentMenu ON parentMenu.id= menu.parent_id
    WHERE
      1 = 1
      <if test="description != null  and description != ''">
        and menu.description like CONCAT('%', #{description},'%')
      </if>
      <if test="onlyLeafMenu != null  and onlyLeafMenu == true">
        and menu.route_component NOT IN ('BlankLayout')
      </if>
      <if test="menuIds != null">
        AND menu.id IN
        <foreach collection="menuIds" open="(" close=")" separator="," item="id">
          #{id}
        </foreach>
      </if>

    ORDER BY
        menu.seq
  </select>
    <select id="getMenuByRoutePath" resultType="com.csci.susdev.model.Menu">
      SELECT
        description description,
        parent_id parentId
      FROM
      t_menu
      WHERE
        route_path = #{path}
        AND description IS NOT NULL
        AND description &lt;&gt; '' UNION ALL
      SELECT
        title description,
        '' parentId
      FROM
      Tzh_Bs_Menu
      WHERE
      name = #{path}
      AND title IS NOT NULL
      AND title &lt;&gt; ''
    </select>
</mapper>