<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.csci.susdev.mapper.VehicleFuelUsageCustomMapper">
    <update id="updateNewIdByOldId">
        UPDATE t_ab_vehicle_fuel_usage
        SET id = #{newId}
        WHERE
            id = #{oldId};
    </update>
    <delete id="batchDeleteByIdList">
      delete from t_ab_vehicle_fuel_usage where id in
      <foreach collection="ids" open="(" close=")" separator="," item="id">
        #{id}
      </foreach>
    </delete>
</mapper>