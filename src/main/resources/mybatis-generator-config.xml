<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE generatorConfiguration PUBLIC "-//mybatis.org//DTD MyBatis Generator Configuration 1.0//EN" "http://mybatis.org/dtd/mybatis-generator-config_1_0.dtd">
<generatorConfiguration>
	<context id="MyBatis" targetRuntime="MyBatis3">

		<jdbcConnection
				driverClass="com.microsoft.sqlserver.jdbc.SQLServerDriver"
				connectionURL="***************************************************;"
				userId="esg" password="7kL8Q57S3hK5R7ls"/>

		<javaTypeResolver>
		    <property name="forceBigDecimals" value="false"/>
		    <property name="useJSR310Types" value="true"/>
		</javaTypeResolver>

		<javaModelGenerator
			targetPackage="com.csci.susdev.model"
			targetProject="src/main/java">
			<property name="enableSubPackages" value="true" />
			<property name="trimStrings" value="true" />
		</javaModelGenerator>

		<sqlMapGenerator
			targetPackage="com.csci.susdev.mapper"
			targetProject="src/main/java">
			<property name="enableSubPackages" value="true" />
		</sqlMapGenerator>

		<javaClientGenerator
			targetPackage="com.csci.susdev.mapper"
			targetProject="src/main/java" type="XMLMAPPER">
			<property name="enableSubPackages" value="true" />
		</javaClientGenerator>

		<table schema="" tableName="t_material_trans_detail"
			domainObjectName="MaterialTransDetail" enableCountByExample="true"
			enableUpdateByExample="true" enableDeleteByExample="true"
			enableSelectByExample="true" selectByExampleQueryId="true">
		</table>
	</context>
</generatorConfiguration>
